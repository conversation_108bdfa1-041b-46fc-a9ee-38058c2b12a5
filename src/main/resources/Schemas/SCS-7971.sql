alter table bk_invoice modify column status enum('PENDING','PAID','DELETED','PARTIAL_PAID','WRITE_OFF');

alter table creditnote modify column status enum('PENDING','REALIZED','PARTIAL_REALIZED','WRITE_OFF');

alter table creditnote modify column closure_type enum('MANUAL', 'SETTLEMENT', 'WRITE_OFF');

alter table bk_vendor_ledger modify column document_type enum ('PURCHASE_INVOICE','DEBIT_NOTE_PR','CREDIT_NOTE_PR','VENDOR_PAYMENT','PAYMENT_ROUND_OFF','ADJUSTMENT','DN_SR_ACCEPTED','DN_SR_EXPIRED','SALE_INVOICE','CX_RECEIPT','RECEIPT_ROUND_OFF','CN_SR_ACCEPTED','CN_SR_EXPIRED','CREDIT_NOTE_DISCOUNT','DEBIT_NOTE_DISCOUNT','CX_CN_DISCOUNT','CX_DN_DISCOUNT','VN_CN_DISCOUNT','VN_DN_DISCOUNT','CREDIT_NOTE_NSR','DEBIT_NOTE_NSR','CHEQUE_BOUNCED','ADVANCE_PAYMENT','DN_ST_RETURN','DN_ICS_RETURN','CN_ST_RETURN','CN_ICS_RETURN','CHEQUE_BOUNCED_CHARGE','CN_ADVANCE_PAYMENT','DN_ADVANCE_PAYMENT', 'WRITE_OFF') COLLATE utf8mb4_unicode_ci DEFAULT NULL
create table ledger_write_off(
 id bigint(20) NOT NULL AUTO_INCREMENT,
 created_on datetime DEFAULT CURRENT_TIMESTAMP,
 created_by varchar(100),
 bk_invoice_id bigint(20),
 total_amount decimal(11,2),
 total_pending_amount decimal(11,2),
 write_off_amount double,
 PRIMARY KEY (id)
 )