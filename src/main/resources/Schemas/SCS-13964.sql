CREATE TABLE `delivery_challan` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `dc_number` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_on` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `amount` decimal(11,2) DEFAULT NULL,
  `pending_amount` decimal(11,2) DEFAULT NULL,
  `status` enum('OPEN','PARTIAL_CLOSED','CLOSED') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `updated_on` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `partner_detail_id` bigint(20) DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `partner_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `tenant` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
);


CREATE TABLE `delivery_challan_log_entry` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `total_value` decimal(11,2) DEFAULT NULL,
  `reference_number` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `reference_date` date DEFAULT NULL,
  `reference_amount` decimal(11,2) DEFAULT NULL,
  `status` enum('CN_SETOFF','INVOICE_SETOFF','WRITE_OFF','FREE_GOODS_SETOFF') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `remark` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_on` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
);

CREATE TABLE `delivery_challan_mapping` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_on` datetime DEFAULT CURRENT_TIMESTAMP,
  `delivery_challan_id` bigint(20) DEFAULT NULL,
  `delivery_challan_log_entry_id` bigint(20) DEFAULT NULL,
  `dc_amount_used` decimal(11,2) DEFAULT NULL,
  `status` enum('OPEN','PARTIAL_CLOSED','CLOSED') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
);


CREATE TABLE `delivery_challan_tax_logs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `delivery_challan_log_entry_id` bigint(20) DEFAULT NULL,
  `tax` decimal(11,2) DEFAULT NULL,
  `taxable_value` decimal(11,2) DEFAULT NULL,
  `tax_value` decimal(11,2) DEFAULT NULL,
  `total_value` decimal(11,2) DEFAULT NULL,
  `hsn` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_on` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

