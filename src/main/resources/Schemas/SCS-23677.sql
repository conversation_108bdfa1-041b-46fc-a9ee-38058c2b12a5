ALTER TABLE settlement_debit_note_distribution_mapping
ADD UNIQUE KEY uq_retailer_dn_settlement (retailer_debit_note_id, settlement_id, advance_payment_id, credit_note_id);

ALTER TABLE retailer_debit_note
  ADD UNIQUE KEY uniq_document_number (document_number),
  ADD COLUMN uuid CHAR(36) NOT NULL UNIQUE,
  ADD COLUMN created_by_userid VARCHAR(255) DEFAULT NULL,
  ADD COLUMN updated_by_userid VARCHAR(255) DEFAULT NULL,
  ADD version smallint unsigned NOT NULL DEFAULT '0';
  MODIFY COLUMN updated_on DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  MODIFY COLUMN amount DECIMAL(15,2) DEFAULT 0,
  MODIFY COLUMN amount_received DECIMAL(15,2) DEFAULT 0,
  MODIFY COLUMN taxable_value DECIMAL(15,2) DEFAULT 0,
  MODIFY COLUMN tax_amount DECIMAL(15,2) DEFAULT 0,
  ADD CONSTRAINT fk_company FOREIGN KEY (company_id) REFERENCES company(id);

 ALTER TABLE settlement add uuid CHAR(36) NOT NULL UNIQUE;

