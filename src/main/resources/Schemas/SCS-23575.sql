CREATE TABLE bulk_cn_reason_code_mapping (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    reason_code BIGINT NOT NULL,
    bulk_cn_category VARCHAR(255) NOT NULL,
    bulk_cn_category_description VARCHAR(255),
    created_by <PERSON><PERSON><PERSON><PERSON>(255),
    created_on DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_by <PERSON><PERSON><PERSON><PERSON>(255),
    updated_on DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    dp_update_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uq_reason_category (reason_code, bulk_cn_category)
);

alter table creditnote add bulk_cn_reason_code_mapping_id BIGINT default 13;

alter table flat_discount_task modify `type` enum('PURCHASE_SLAB_DISCOUNT_CN','ADVANCE_PAYMENT','OFFLINE_PROMOTION_DISCOUNT_CN','OTC_MARGIN_LEAKAGE_DISCOUNT_CN','SALES_INCENTIVE_DISCOUNT_CN','SR_AARUSH_CN','FINANCE_DISCOUNT_CN') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL;

alter table creditnote modify `note_type` enum('PURCHASE_RETURN','DISCOUNT','SR_ACCEPTED','SR_EXPIRED','NSR','ST_RETURN','ICS_RETURN','SLAB_DISCOUNT_CN','SLAB_DISCOUNT_DN','ADVANCE_PAYMENT','OFFLINE_PROMOTION_DISCOUNT_CN','OTC_MARGIN_LEAKAGE_DISCOUNT_CN','SALES_INCENTIVE_DISCOUNT_CN','PURCHASE_SLAB_DISCOUNT_CN','NSR_ACCEPTED','NSR_EXPIRED','NSR_DAMAGED','SR_DAMAGED','FINANCE_DISCOUNT_CN') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL;

alter table debitnote modify `note_type` enum('PURCHASE_RETURN','DISCOUNT','SR_ACCEPTED','SR_EXPIRED','NSR','ST_RETURN','ICS_RETURN','SLAB_DISCOUNT_CN','SLAB_DISCOUNT_DN','ADVANCE_PAYMENT','OFFLINE_PROMOTION_DISCOUNT_DN','OTC_MARGIN_LEAKAGE_DISCOUNT_DN','SALES_INCENTIVE_DISCOUNT_DN','PURCHASE_SLAB_DISCOUNT_DN','NSR_ACCEPTED','NSR_EXPIRED','SR_DAMAGED','NSR_DAMAGED','FINANCE_DISCOUNT_DN') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL;

alter table flat_discount_task add uuid varchar(255) default null;

alter table flat_discount_task_detail add reason_code BIGINT default 13;

alter table vault_document modify `type` enum('PURCHASE_INVOICE','DEBIT_NOTE_PR','CREDIT_NOTE_PR','VENDOR_PAYMENT','PAYMENT_ROUND_OFF','ADJUSTMENT','DN_SR_ACCEPTED','DN_SR_EXPIRED','SALE_INVOICE','CX_RECEIPT','RECEIPT_ROUND_OFF','CN_SR_ACCEPTED','CN_SR_EXPIRED','CREDIT_NOTE_DISCOUNT','DEBIT_NOTE_DISCOUNT','CX_CN_DISCOUNT','CX_DN_DISCOUNT','VN_CN_DISCOUNT','VN_DN_DISCOUNT','CREDIT_NOTE_NSR','DEBIT_NOTE_NSR','CHEQUE_BOUNCED','ADVANCE_PAYMENT','DN_ST_RETURN','DN_ICS_RETURN','CN_ST_RETURN','CN_ICS_RETURN','CHEQUE_BOUNCED_CHARGE','CN_SR_ACCEPTED_B2C','CN_SR_EXPIRED_B2C','CN_SR_ACCEPTED_B2B','CN_SR_EXPIRED_B2B','DN_SR_ACCEPTED_B2C','DN_SR_EXPIRED_B2C','DN_SR_ACCEPTED_B2B','DN_SR_EXPIRED_B2B','CN_ADVANCE_PAYMENT','DN_ADVANCE_PAYMENT','PAYMENT_REVERSAL','RECEIPT_ENTRY','RETAILER_CN_DN','RETAILER_ADHOC_DN','RETAILER_DN','FINANCE_DISCOUNT_CN') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL;

BEGIN declare suffix VARCHAR(10); declare yy BIGINT(5); declare mm BIGINT(5); declare pattern VARCHAR(25); SELECT MONTH(CURDATE()) into mm; select DATE_FORMAT(CURDATE(), "%y") into yy; IF(mm < 4) THEN SET yy = yy - 1; ELSE SET yy = yy; END IF; select substring(NEW.company_code,2) into suffix; select concat(yy , suffix) into pattern; INSERT INTO vault_document( `created_by`,`updated_by`,`category`,`type`,`series`, `year`,`suffix`,`from_number`,`to_number`,`current_number`,`optlock`,`v2_enabled`,`series_v2`,`from_number_v2`,`to_number_v2`,`current_number_v2`) VALUES ( 'SYSTEM', 'SYSTEM', 'DEBIT_NOTE', 'DEBIT_NOTE_PR', NULL, yy, suffix, concat('20', pattern ,'/00000001'),concat('20', pattern ,'/99999999'), concat('20', pattern,'/1') ,0,0,NULL,concat('20', pattern ,'/00000001'),concat('20', pattern ,'/99999999'),concat('20', pattern,'/1')), ( 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'CREDIT_NOTE_PR', 31, yy, suffix, concat('31',pattern,'/00000001'), concat('31',pattern,'/99999999'),concat('31', pattern,'/1'), 0,0,131,concat('131', pattern ,'/00000001'),concat('131', pattern ,'/99999999'),concat('131', pattern,'/1')), ( 'SYSTEM', 'SYSTEM', 'PAYMENT', 'VENDOR_PAYMENT', 20, yy, suffix, concat('20', pattern ,'/00000001'),concat('20', pattern ,'/99999999'), concat('20', pattern,'/1'), 0,0,120,concat('120', pattern ,'/00000001'),concat('120', pattern ,'/99999999'),concat('120', pattern,'/1')), ( 'SYSTEM', 'SYSTEM', 'ADJUSTMENT', 'ADJUSTMENT', 10, yy, suffix, concat('10', pattern ,'/00000001'),concat('10', pattern ,'/99999999'), concat('10', pattern,'/1'), 0,0,110,concat('110', pattern ,'/00000001'),concat('110', pattern ,'/99999999'),concat('110', pattern,'/1')), ( 'SYSTEM', 'SYSTEM', 'PURCHASE', 'PURCHASE_INVOICE', NULL,yy, suffix, concat('20', pattern ,'/00000001'),concat('20', pattern ,'/99999999'), concat('20', pattern,'/1'), 0,0,120,concat('120', pattern ,'/00000001'),concat('120', pattern ,'/99999999'),concat('120', pattern,'/1')), ( 'SYSTEM', 'SYSTEM', 'DEBIT_NOTE', 'DN_SR_ACCEPTED_B2C', 41, yy, suffix,concat('41', pattern ,'/00000001'),concat('41', pattern ,'/99999999'), concat('41', pattern,'/1'), 0,0,141,concat('141', pattern ,'/00000001'),concat('141', pattern ,'/99999999'),concat('141', pattern,'/1')), ( 'SYSTEM', 'SYSTEM', 'DEBIT_NOTE', 'DN_SR_EXPIRED_B2C', 42, yy, suffix, concat('42', pattern ,'/00000001'),concat('42', pattern ,'/99999999'), concat('42', pattern,'/1'), 0,0,142,concat('142', pattern ,'/00000001'),concat('142', pattern ,'/99999999'),concat('142', pattern,'/1')), ( 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'CN_SR_ACCEPTED_B2C', 51, yy, suffix, concat('51', pattern ,'/00000001'),concat('51', pattern ,'/99999999'), concat('51', pattern,'/1'), 0,0,251,concat('251', pattern ,'/00000001'),concat('251', pattern ,'/99999999'),concat('251', pattern,'/1')), ( 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'CN_SR_EXPIRED_B2C', 52, yy, suffix, concat('52', pattern ,'/00000001'),concat('52', pattern ,'/99999999'), concat('52', pattern,'/1'), 0,0,252,concat('252', pattern ,'/00000001'),concat('252', pattern ,'/99999999'),concat('252', pattern,'/1')), ( 'SYSTEM', 'SYSTEM', 'DEBIT_NOTE', 'DN_SR_ACCEPTED_B2B', 48, yy, suffix,concat('48', pattern ,'/00000001'),concat('48', pattern ,'/99999999'), concat('48', pattern,'/1'), 0,0,148,concat('148', pattern ,'/00000001'),concat('148', pattern ,'/99999999'),concat('148', pattern,'/1')), ( 'SYSTEM', 'SYSTEM', 'DEBIT_NOTE', 'DN_SR_EXPIRED_B2B', 49, yy, suffix, concat('49', pattern ,'/00000001'),concat('49', pattern ,'/99999999'), concat('49', pattern,'/1'), 0,0,149,concat('149', pattern ,'/00000001'),concat('149', pattern ,'/99999999'),concat('149', pattern,'/1')), ( 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'CN_SR_ACCEPTED_B2B', 58, yy, suffix, concat('58', pattern ,'/00000001'),concat('58', pattern ,'/99999999'), concat('58', pattern,'/1'), 0,0,258,concat('258', pattern ,'/00000001'),concat('258', pattern ,'/99999999'),concat('258', pattern,'/1')), ( 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'CN_SR_EXPIRED_B2B', 59, yy, suffix, concat('59', pattern ,'/00000001'),concat('59', pattern ,'/99999999'), concat('59', pattern,'/1'), 0,0,259,concat('259', pattern ,'/00000001'),concat('259', pattern ,'/99999999'),concat('259', pattern,'/1')), ( 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'CX_RECEIPT', 40, yy, suffix, concat('40', pattern ,'/00000001'),concat('40', pattern ,'/99999999'), concat('40', pattern,'/1'), 0,0,140,concat('140', pattern ,'/00000001'),concat('140', pattern ,'/99999999'),concat('140', pattern,'/1')), ( 'SYSTEM', 'SYSTEM', 'DEBIT_NOTE', 'VN_DN_DISCOUNT', 22, yy, suffix, concat('22', pattern ,'/00000001'),concat('22', pattern ,'/99999999'), concat('22', pattern,'/1'), 0,0,122,concat('122', pattern ,'/00000001'),concat('122', pattern ,'/99999999'),concat('122', pattern,'/1')), ( 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'VN_CN_DISCOUNT', 32, yy, suffix, concat('32', pattern ,'/00000001'),concat('32', pattern ,'/99999999'), concat('32', pattern,'/1'), 0,0,132,concat('132', pattern ,'/00000001'),concat('132', pattern ,'/99999999'),concat('132', pattern,'/1')), ( 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'CX_CN_DISCOUNT', 53, yy, suffix, concat('53', pattern ,'/00000001'),concat('53', pattern ,'/99999999'), concat('53', pattern,'/1'), 0,0,253,concat('253', pattern ,'/00000001'),concat('253', pattern ,'/99999999'),concat('253', pattern,'/1')), ( 'SYSTEM', 'SYSTEM', 'DEBIT_NOTE', 'CX_DN_DISCOUNT', 43, yy, suffix, concat('43', pattern ,'/00000001'),concat('43', pattern ,'/99999999'), concat('43', pattern,'/1'), 0,0,143,concat('143', pattern ,'/00000001'),concat('143', pattern ,'/99999999'),concat('143', pattern,'/1')), ( 'SYSTEM', 'SYSTEM', 'DEBIT_NOTE', 'DEBIT_NOTE_NSR', 44, yy, suffix, concat('44', pattern ,'/00000001'),concat('44', pattern ,'/99999999'), concat('44', pattern,'/1'), 0,0,144,concat('144', pattern ,'/00000001'),concat('144', pattern ,'/99999999'),concat('144', pattern,'/1')), ( 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'CREDIT_NOTE_NSR', 54, yy, suffix, concat('54', pattern ,'/00000001'),concat('54', pattern ,'/99999999'), concat('54', pattern,'/1'), 0,0,254,concat('254', pattern ,'/00000001'),concat('254', pattern ,'/99999999'),concat('254', pattern,'/1')), ( 'SYSTEM', 'SYSTEM', 'ADJUSTMENT', 'CHEQUE_BOUNCED', 11, yy, suffix, concat('11', pattern ,'/00000001'),concat('11', pattern ,'/99999999'), concat('11', pattern,'/1'), 0,0,111,concat('111', pattern ,'/00000001'),concat('111', pattern ,'/99999999'),concat('111', pattern,'/1')), ( 'SYSTEM', 'SYSTEM', 'ADJUSTMENT', 'ADVANCE_PAYMENT', 12, yy, suffix, concat('12', pattern ,'/00000001'),concat('12', pattern ,'/99999999'), concat('12', pattern,'/1'), 0,0,112,concat('112', pattern ,'/00000001'),concat('112', pattern ,'/99999999'),concat('112', pattern,'/1')), ( 'SYSTEM', 'SYSTEM', 'DEBIT_NOTE', 'DN_ICS_RETURN', 46, yy, suffix, concat('46', pattern ,'/00000001'),concat('46', pattern ,'/99999999'), concat('46', pattern,'/1'), 0,0,146,concat('146', pattern ,'/00000001'),concat('146', pattern ,'/99999999'),concat('146', pattern,'/1')), ( 'SYSTEM', 'SYSTEM', 'DEBIT_NOTE', 'DN_ST_RETURN', 45, yy, suffix, concat('45', pattern ,'/00000001'),concat('45', pattern ,'/99999999'), concat('45', pattern,'/1'), 0,0,145,concat('145', pattern ,'/00000001'),concat('145', pattern ,'/99999999'),concat('145', pattern,'/1')), ( 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'CN_ICS_RETURN', 56, yy, suffix, concat('56', pattern ,'/00000001'),concat('56', pattern ,'/99999999'), concat('56', pattern,'/1'), 0,0,256,concat('256', pattern ,'/00000001'),concat('256', pattern ,'/99999999'),concat('256', pattern,'/1')), ( 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'CN_ST_RETURN', 55, yy, suffix, concat('55', pattern ,'/00000001'),concat('55', pattern ,'/99999999'), concat('55', pattern,'/1'), 0,0,255,concat('255', pattern ,'/00000001'),concat('255', pattern ,'/99999999'),concat('255', pattern,'/1')), ( 'SYSTEM', 'SYSTEM', 'PURCHASE', 'CHEQUE_BOUNCED_CHARGE', 13, yy, suffix, concat('13', pattern ,'/00000001'),concat('13', pattern ,'/99999999'), concat('13', pattern,'/1'), 0,0,113,concat('113', pattern ,'/00000001'),concat('113', pattern ,'/99999999'),concat('113', pattern,'/1')), ( 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'CN_ADVANCE_PAYMENT', 90, yy, suffix, concat('90', pattern ,'/00000001'),concat('90', pattern ,'/99999999'), concat('90', pattern,'/1'), 0,0,190,concat('190', pattern ,'/00000001'),concat('190', pattern ,'/99999999'),concat('190', pattern,'/1')), ( 'SYSTEM', 'SYSTEM', 'DEBIT_NOTE', 'DN_ADVANCE_PAYMENT', 50, yy, suffix, concat('50', pattern ,'/00000001'),concat('50', pattern ,'/99999999'), concat('50', pattern,'/1'), 0,0,150,concat('150', pattern ,'/00000001'),concat('150', pattern ,'/99999999'),concat('150', pattern,'/1')), ( 'SYSTEM', 'SYSTEM', 'PAYMENT', 'PAYMENT_REVERSAL', 17, yy, suffix, concat('17', pattern ,'/00000001'),concat('17', pattern ,'/99999999'), concat('17', pattern,'/1'), 0,0,117,concat('117', pattern ,'/00000001'),concat('117', pattern ,'/99999999'),concat('117', pattern,'/1')), ( 'SYSTEM', 'SYSTEM', 'RECEIPT_AGAINST_SETTLEMENT', 'RECEIPT_ENTRY', 39, yy, suffix, concat('39', pattern ,'/00000001'),concat('39', pattern ,'/99999999'), concat('39', pattern,'/1'), 0,0,139,concat('139', pattern ,'/00000001'),concat('139', pattern ,'/99999999'),concat('139', pattern,'/1')),('SYSTEM','SYSTEM','DEBIT_NOTE','RETAILER_DN',60,yy,suffix,Concat('60', pattern ,'/00000001'),Concat('60', pattern ,'/99999999'),Concat('60', pattern,'/1'),0,0,260,Concat('260', pattern ,'/00000001'),Concat('260', pattern ,'/99999999'),Concat('260', pattern,'/1'))
,('SYSTEM','SYSTEM','CREDIT_NOTE','FINANCE_DISCOUNT_CN',14,yy,suffix,Concat('14', pattern ,'/00000001'),Concat('14', pattern ,'/99999999'),Concat('14', pattern,'/1'),0,0,260,Concat('214', pattern ,'/00000001'),Concat('214', pattern ,'/99999999'),Concat('214', pattern,'/1')) ; END


INSERT INTO vault_document (created_on, updated_on, created_by, updated_by, category, type, series, year, suffix, from_number, to_number, current_number, optlock, dp_updated_at, v2_enabled, series_v2, from_number_v2, to_number_v2, current_number_v2) VALUES
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 8, '14258/00000001', '14258/9999999', '14258/00000001', '0', '2025-04-25 12:45:46', 0, 114, '114258/00000001', '114258/9999999', '114258/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 9, '14259/00000001', '14259/9999999', '14259/00000001', '0', '2025-04-25 12:45:46', 0, 114, '114259/00000001', '114259/9999999', '114259/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 11, '142511/00000001', '142511/9999999', '142511/00000001', '0', '2025-04-25 12:45:46', 0, 114, '1142511/00000001', '1142511/9999999', '1142511/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 14, '142514/00000001', '142514/9999999', '142514/00000001', '0', '2025-04-25 12:45:46', 0, 114, '1142514/00000001', '1142514/9999999', '1142514/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 32, '142532/00000001', '142532/9999999', '142532/00000001', '0', '2025-04-25 12:45:46', 0, 114, '1142532/00000001', '1142532/9999999', '1142532/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 33, '142533/00000001', '142533/9999999', '142533/00000001', '0', '2025-04-25 12:45:46', 0, 114, '1142533/00000001', '1142533/9999999', '1142533/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 40, '142540/00000001', '142540/9999999', '142540/00000001', '0', '2025-04-25 12:45:46', 0, 114, '1142540/00000001', '1142540/9999999', '1142540/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 44, '142544/00000001', '142544/9999999', '142544/00000001', '0', '2025-04-25 12:45:46', 0, 114, '1142544/00000001', '1142544/9999999', '1142544/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 79, '142579/00000001', '142579/9999999', '142579/00000001', '0', '2025-04-25 12:45:46', 0, 114, '1142579/00000001', '1142579/9999999', '1142579/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 1, '14251/00000001', '14251/9999999', '14251/00000001', '0', '2025-04-25 12:45:46', 0, 114, '114251/00000001', '114251/9999999', '114251/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 11, '142511/00000001', '142511/9999999', '142511/00000001', '0', '2025-04-25 12:45:46', 0, 114, '1142511/00000001', '1142511/9999999', '1142511/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 111, '1425111/00000001', '1425111/9999999', '1425111/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425111/00000001', '11425111/9999999', '11425111/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 124, '1425124/00000001', '1425124/9999999', '1425124/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425124/00000001', '11425124/9999999', '11425124/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 14, '142514/00000001', '142514/9999999', '142514/00000001', '0', '2025-04-25 12:45:46', 0, 114, '1142514/00000001', '1142514/9999999', '1142514/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 141, '1425141/00000001', '1425141/9999999', '1425141/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425141/00000001', '11425141/9999999', '11425141/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 201, '1425201/00000001', '1425201/9999999', '1425201/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425201/00000001', '11425201/9999999', '11425201/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 202, '1425202/00000001', '1425202/9999999', '1425202/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425202/00000001', '11425202/9999999', '11425202/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 203, '1425203/00000001', '1425203/9999999', '1425203/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425203/00000001', '11425203/9999999', '11425203/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 204, '1425204/00000001', '1425204/9999999', '1425204/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425204/00000001', '11425204/9999999', '11425204/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 205, '1425205/00000001', '1425205/9999999', '1425205/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425205/00000001', '11425205/9999999', '11425205/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 211, '1425211/00000001', '1425211/9999999', '1425211/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425211/00000001', '11425211/9999999', '11425211/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 212, '1425212/00000001', '1425212/9999999', '1425212/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425212/00000001', '11425212/9999999', '11425212/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 213, '1425213/00000001', '1425213/9999999', '1425213/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425213/00000001', '11425213/9999999', '11425213/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 214, '1425214/00000001', '1425214/9999999', '1425214/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425214/00000001', '11425214/9999999', '11425214/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 215, '1425215/00000001', '1425215/9999999', '1425215/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425215/00000001', '11425215/9999999', '11425215/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 216, '1425216/00000001', '1425216/9999999', '1425216/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425216/00000001', '11425216/9999999', '11425216/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 217, '1425217/00000001', '1425217/9999999', '1425217/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425217/00000001', '11425217/9999999', '11425217/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 222, '1425222/00000001', '1425222/9999999', '1425222/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425222/00000001', '11425222/9999999', '11425222/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 224, '1425224/00000001', '1425224/9999999', '1425224/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425224/00000001', '11425224/9999999', '11425224/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 225, '1425225/00000001', '1425225/9999999', '1425225/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425225/00000001', '11425225/9999999', '11425225/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 240, '1425240/00000001', '1425240/9999999', '1425240/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425240/00000001', '11425240/9999999', '11425240/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 244, '1425244/00000001', '1425244/9999999', '1425244/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425244/00000001', '11425244/9999999', '11425244/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 245, '1425245/00000001', '1425245/9999999', '1425245/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425245/00000001', '11425245/9999999', '11425245/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 250, '1425250/00000001', '1425250/9999999', '1425250/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425250/00000001', '11425250/9999999', '11425250/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 251, '1425251/00000001', '1425251/9999999', '1425251/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425251/00000001', '11425251/9999999', '11425251/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 252, '1425252/00000001', '1425252/9999999', '1425252/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425252/00000001', '11425252/9999999', '11425252/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 253, '1425253/00000001', '1425253/9999999', '1425253/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425253/00000001', '11425253/9999999', '11425253/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 254, '1425254/00000001', '1425254/9999999', '1425254/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425254/00000001', '11425254/9999999', '11425254/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 255, '1425255/00000001', '1425255/9999999', '1425255/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425255/00000001', '11425255/9999999', '11425255/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 256, '1425256/00000001', '1425256/9999999', '1425256/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425256/00000001', '11425256/9999999', '11425256/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 257, '1425257/00000001', '1425257/9999999', '1425257/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425257/00000001', '11425257/9999999', '11425257/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 258, '1425258/00000001', '1425258/9999999', '1425258/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425258/00000001', '11425258/9999999', '11425258/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 259, '1425259/00000001', '1425259/9999999', '1425259/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425259/00000001', '11425259/9999999', '11425259/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 260, '1425260/00000001', '1425260/9999999', '1425260/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425260/00000001', '11425260/9999999', '11425260/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 261, '1425261/00000001', '1425261/9999999', '1425261/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425261/00000001', '11425261/9999999', '11425261/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 262, '1425262/00000001', '1425262/9999999', '1425262/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425262/00000001', '11425262/9999999', '11425262/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 263, '1425263/00000001', '1425263/9999999', '1425263/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425263/00000001', '11425263/9999999', '11425263/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 264, '1425264/00000001', '1425264/9999999', '1425264/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425264/00000001', '11425264/9999999', '11425264/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 265, '1425265/00000001', '1425265/9999999', '1425265/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425265/00000001', '11425265/9999999', '11425265/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 266, '1425266/00000001', '1425266/9999999', '1425266/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425266/00000001', '11425266/9999999', '11425266/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 267, '1425267/00000001', '1425267/9999999', '1425267/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425267/00000001', '11425267/9999999', '11425267/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 268, '1425268/00000001', '1425268/9999999', '1425268/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425268/00000001', '11425268/9999999', '11425268/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 269, '1425269/00000001', '1425269/9999999', '1425269/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425269/00000001', '11425269/9999999', '11425269/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 270, '1425270/00000001', '1425270/9999999', '1425270/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425270/00000001', '11425270/9999999', '11425270/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 271, '1425271/00000001', '1425271/9999999', '1425271/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425271/00000001', '11425271/9999999', '11425271/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 272, '1425272/00000001', '1425272/9999999', '1425272/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425272/00000001', '11425272/9999999', '11425272/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 273, '1425273/00000001', '1425273/9999999', '1425273/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425273/00000001', '11425273/9999999', '11425273/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 274, '1425274/00000001', '1425274/9999999', '1425274/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425274/00000001', '11425274/9999999', '11425274/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 275, '1425275/00000001', '1425275/9999999', '1425275/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425275/00000001', '11425275/9999999', '11425275/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 276, '1425276/00000001', '1425276/9999999', '1425276/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425276/00000001', '11425276/9999999', '11425276/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 277, '1425277/00000001', '1425277/9999999', '1425277/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425277/00000001', '11425277/9999999', '11425277/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 278, '1425278/00000001', '1425278/9999999', '1425278/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425278/00000001', '11425278/9999999', '11425278/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 279, '1425279/00000001', '1425279/9999999', '1425279/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425279/00000001', '11425279/9999999', '11425279/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 280, '1425280/00000001', '1425280/9999999', '1425280/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425280/00000001', '11425280/9999999', '11425280/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 281, '1425281/00000001', '1425281/9999999', '1425281/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425281/00000001', '11425281/9999999', '11425281/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 282, '1425282/00000001', '1425282/9999999', '1425282/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425282/00000001', '11425282/9999999', '11425282/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 283, '1425283/00000001', '1425283/9999999', '1425283/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425283/00000001', '11425283/9999999', '11425283/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 284, '1425284/00000001', '1425284/9999999', '1425284/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425284/00000001', '11425284/9999999', '11425284/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 301, '1425301/00000001', '1425301/9999999', '1425301/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425301/00000001', '11425301/9999999', '11425301/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 302, '1425302/00000001', '1425302/9999999', '1425302/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425302/00000001', '11425302/9999999', '11425302/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 303, '1425303/00000001', '1425303/9999999', '1425303/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425303/00000001', '11425303/9999999', '11425303/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 32, '142532/00000001', '142532/9999999', '142532/00000001', '0', '2025-04-25 12:45:46', 0, 114, '1142532/00000001', '1142532/9999999', '1142532/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 33, '142533/00000001', '142533/9999999', '142533/00000001', '0', '2025-04-25 12:45:46', 0, 114, '1142533/00000001', '1142533/9999999', '1142533/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 333, '1425333/00000001', '1425333/9999999', '1425333/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425333/00000001', '11425333/9999999', '11425333/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 40, '142540/00000001', '142540/9999999', '142540/00000001', '0', '2025-04-25 12:45:46', 0, 114, '1142540/00000001', '1142540/9999999', '1142540/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 44, '142544/00000001', '142544/9999999', '142544/00000001', '0', '2025-04-25 12:45:46', 0, 114, '1142544/00000001', '1142544/9999999', '1142544/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 501, '1425501/00000001', '1425501/9999999', '1425501/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425501/00000001', '11425501/9999999', '11425501/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 502, '1425502/00000001', '1425502/9999999', '1425502/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425502/00000001', '11425502/9999999', '11425502/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 503, '1425503/00000001', '1425503/9999999', '1425503/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425503/00000001', '11425503/9999999', '11425503/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 504, '1425504/00000001', '1425504/9999999', '1425504/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425504/00000001', '11425504/9999999', '11425504/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 505, '1425505/00000001', '1425505/9999999', '1425505/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425505/00000001', '11425505/9999999', '11425505/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 506, '1425506/00000001', '1425506/9999999', '1425506/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425506/00000001', '11425506/9999999', '11425506/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 507, '1425507/00000001', '1425507/9999999', '1425507/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425507/00000001', '11425507/9999999', '11425507/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 508, '1425508/00000001', '1425508/9999999', '1425508/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425508/00000001', '11425508/9999999', '11425508/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 509, '1425509/00000001', '1425509/9999999', '1425509/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425509/00000001', '11425509/9999999', '11425509/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 511, '1425511/00000001', '1425511/9999999', '1425511/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425511/00000001', '11425511/9999999', '11425511/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 514, '1425514/00000001', '1425514/9999999', '1425514/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425514/00000001', '11425514/9999999', '11425514/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 524, '1425524/00000001', '1425524/9999999', '1425524/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425524/00000001', '11425524/9999999', '11425524/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 532, '1425532/00000001', '1425532/9999999', '1425532/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425532/00000001', '11425532/9999999', '11425532/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 533, '1425533/00000001', '1425533/9999999', '1425533/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425533/00000001', '11425533/9999999', '11425533/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 540, '1425540/00000001', '1425540/9999999', '1425540/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425540/00000001', '11425540/9999999', '11425540/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 541, '1425541/00000001', '1425541/9999999', '1425541/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425541/00000001', '11425541/9999999', '11425541/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 544, '1425544/00000001', '1425544/9999999', '1425544/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425544/00000001', '11425544/9999999', '11425544/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 579, '1425579/00000001', '1425579/9999999', '1425579/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425579/00000001', '11425579/9999999', '11425579/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 601, '1425601/00000001', '1425601/9999999', '1425601/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425601/00000001', '11425601/9999999', '11425601/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 602, '1425602/00000001', '1425602/9999999', '1425602/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425602/00000001', '11425602/9999999', '11425602/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 603, '1425603/00000001', '1425603/9999999', '1425603/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425603/00000001', '11425603/9999999', '11425603/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 604, '1425604/00000001', '1425604/9999999', '1425604/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425604/00000001', '11425604/9999999', '11425604/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 605, '1425605/00000001', '1425605/9999999', '1425605/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425605/00000001', '11425605/9999999', '11425605/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 612, '1425612/00000001', '1425612/9999999', '1425612/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425612/00000001', '11425612/9999999', '11425612/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 614, '1425614/00000001', '1425614/9999999', '1425614/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425614/00000001', '11425614/9999999', '11425614/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 615, '1425615/00000001', '1425615/9999999', '1425615/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425615/00000001', '11425615/9999999', '11425615/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 616, '1425616/00000001', '1425616/9999999', '1425616/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425616/00000001', '11425616/9999999', '11425616/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 624, '1425624/00000001', '1425624/9999999', '1425624/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425624/00000001', '11425624/9999999', '11425624/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 640, '1425640/00000001', '1425640/9999999', '1425640/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425640/00000001', '11425640/9999999', '11425640/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 644, '1425644/00000001', '1425644/9999999', '1425644/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425644/00000001', '11425644/9999999', '11425644/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 679, '1425679/00000001', '1425679/9999999', '1425679/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425679/00000001', '11425679/9999999', '11425679/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 701, '1425701/00000001', '1425701/9999999', '1425701/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425701/00000001', '11425701/9999999', '11425701/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 702, '1425702/00000001', '1425702/9999999', '1425702/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425702/00000001', '11425702/9999999', '11425702/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 703, '1425703/00000001', '1425703/9999999', '1425703/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425703/00000001', '11425703/9999999', '11425703/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 710, '1425710/00000001', '1425710/9999999', '1425710/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425710/00000001', '11425710/9999999', '11425710/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 777, '1425777/00000001', '1425777/9999999', '1425777/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425777/00000001', '11425777/9999999', '11425777/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 778, '1425778/00000001', '1425778/9999999', '1425778/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425778/00000001', '11425778/9999999', '11425778/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 779, '1425779/00000001', '1425779/9999999', '1425779/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425779/00000001', '11425779/9999999', '11425779/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 780, '1425780/00000001', '1425780/9999999', '1425780/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425780/00000001', '11425780/9999999', '11425780/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 781, '1425781/00000001', '1425781/9999999', '1425781/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425781/00000001', '11425781/9999999', '11425781/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 782, '1425782/00000001', '1425782/9999999', '1425782/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425782/00000001', '11425782/9999999', '11425782/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 783, '1425783/00000001', '1425783/9999999', '1425783/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425783/00000001', '11425783/9999999', '11425783/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 784, '1425784/00000001', '1425784/9999999', '1425784/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425784/00000001', '11425784/9999999', '11425784/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 785, '1425785/00000001', '1425785/9999999', '1425785/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425785/00000001', '11425785/9999999', '11425785/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 786, '1425786/00000001', '1425786/9999999', '1425786/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425786/00000001', '11425786/9999999', '11425786/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 787, '1425787/00000001', '1425787/9999999', '1425787/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425787/00000001', '11425787/9999999', '11425787/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 788, '1425788/00000001', '1425788/9999999', '1425788/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425788/00000001', '11425788/9999999', '11425788/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 789, '1425789/00000001', '1425789/9999999', '1425789/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425789/00000001', '11425789/9999999', '11425789/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 79, '142579/00000001', '142579/9999999', '142579/00000001', '0', '2025-04-25 12:45:46', 0, 114, '1142579/00000001', '1142579/9999999', '1142579/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 790, '1425790/00000001', '1425790/9999999', '1425790/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425790/00000001', '11425790/9999999', '11425790/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 791, '1425791/00000001', '1425791/9999999', '1425791/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425791/00000001', '11425791/9999999', '11425791/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 792, '1425792/00000001', '1425792/9999999', '1425792/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425792/00000001', '11425792/9999999', '11425792/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 793, '1425793/00000001', '1425793/9999999', '1425793/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425793/00000001', '11425793/9999999', '11425793/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 794, '1425794/00000001', '1425794/9999999', '1425794/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425794/00000001', '11425794/9999999', '11425794/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 795, '1425795/00000001', '1425795/9999999', '1425795/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425795/00000001', '11425795/9999999', '11425795/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 796, '1425796/00000001', '1425796/9999999', '1425796/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425796/00000001', '11425796/9999999', '11425796/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 797, '1425797/00000001', '1425797/9999999', '1425797/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425797/00000001', '11425797/9999999', '11425797/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 798, '1425798/00000001', '1425798/9999999', '1425798/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425798/00000001', '11425798/9999999', '11425798/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 799, '1425799/00000001', '1425799/9999999', '1425799/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425799/00000001', '11425799/9999999', '11425799/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 8, '14258/00000001', '14258/9999999', '14258/00000001', '0', '2025-04-25 12:45:46', 0, 114, '114258/00000001', '114258/9999999', '114258/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 800, '1425800/00000001', '1425800/9999999', '1425800/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425800/00000001', '11425800/9999999', '11425800/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 801, '1425801/00000001', '1425801/9999999', '1425801/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425801/00000001', '11425801/9999999', '11425801/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 802, '1425802/00000001', '1425802/9999999', '1425802/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425802/00000001', '11425802/9999999', '11425802/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 803, '1425803/00000001', '1425803/9999999', '1425803/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425803/00000001', '11425803/9999999', '11425803/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 804, '1425804/00000001', '1425804/9999999', '1425804/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425804/00000001', '11425804/9999999', '11425804/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 805, '1425805/00000001', '1425805/9999999', '1425805/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425805/00000001', '11425805/9999999', '11425805/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 806, '1425806/00000001', '1425806/9999999', '1425806/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425806/00000001', '11425806/9999999', '11425806/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 807, '1425807/00000001', '1425807/9999999', '1425807/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425807/00000001', '11425807/9999999', '11425807/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 808, '1425808/00000001', '1425808/9999999', '1425808/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425808/00000001', '11425808/9999999', '11425808/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 809, '1425809/00000001', '1425809/9999999', '1425809/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425809/00000001', '11425809/9999999', '11425809/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 810, '1425810/00000001', '1425810/9999999', '1425810/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425810/00000001', '11425810/9999999', '11425810/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 811, '1425811/00000001', '1425811/9999999', '1425811/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425811/00000001', '11425811/9999999', '11425811/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 812, '1425812/00000001', '1425812/9999999', '1425812/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425812/00000001', '11425812/9999999', '11425812/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 813, '1425813/00000001', '1425813/9999999', '1425813/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425813/00000001', '11425813/9999999', '11425813/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 814, '1425814/00000001', '1425814/9999999', '1425814/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425814/00000001', '11425814/9999999', '11425814/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 815, '1425815/00000001', '1425815/9999999', '1425815/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425815/00000001', '11425815/9999999', '11425815/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 816, '1425816/00000001', '1425816/9999999', '1425816/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425816/00000001', '11425816/9999999', '11425816/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 817, '1425817/00000001', '1425817/9999999', '1425817/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425817/00000001', '11425817/9999999', '11425817/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 818, '1425818/00000001', '1425818/9999999', '1425818/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425818/00000001', '11425818/9999999', '11425818/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 819, '1425819/00000001', '1425819/9999999', '1425819/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425819/00000001', '11425819/9999999', '11425819/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 820, '1425820/00000001', '1425820/9999999', '1425820/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425820/00000001', '11425820/9999999', '11425820/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 821, '1425821/00000001', '1425821/9999999', '1425821/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425821/00000001', '11425821/9999999', '11425821/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 822, '1425822/00000001', '1425822/9999999', '1425822/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425822/00000001', '11425822/9999999', '11425822/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 823, '1425823/00000001', '1425823/9999999', '1425823/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425823/00000001', '11425823/9999999', '11425823/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 824, '1425824/00000001', '1425824/9999999', '1425824/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425824/00000001', '11425824/9999999', '11425824/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 825, '1425825/00000001', '1425825/9999999', '1425825/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425825/00000001', '11425825/9999999', '11425825/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 826, '1425826/00000001', '1425826/9999999', '1425826/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425826/00000001', '11425826/9999999', '11425826/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 827, '1425827/00000001', '1425827/9999999', '1425827/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425827/00000001', '11425827/9999999', '11425827/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 828, '1425828/00000001', '1425828/9999999', '1425828/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425828/00000001', '11425828/9999999', '11425828/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 829, '1425829/00000001', '1425829/9999999', '1425829/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425829/00000001', '11425829/9999999', '11425829/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 9, '14259/00000001', '14259/9999999', '14259/00000001', '0', '2025-04-25 12:45:46', 0, 114, '114259/00000001', '114259/9999999', '114259/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 901, '1425901/00000001', '1425901/9999999', '1425901/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425901/00000001', '11425901/9999999', '11425901/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 902, '1425902/00000001', '1425902/9999999', '1425902/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425902/00000001', '11425902/9999999', '11425902/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 903, '1425903/00000001', '1425903/9999999', '1425903/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425903/00000001', '11425903/9999999', '11425903/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 904, '1425904/00000001', '1425904/9999999', '1425904/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425904/00000001', '11425904/9999999', '11425904/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 905, '1425905/00000001', '1425905/9999999', '1425905/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425905/00000001', '11425905/9999999', '11425905/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 906, '1425906/00000001', '1425906/9999999', '1425906/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425906/00000001', '11425906/9999999', '11425906/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 907, '1425907/00000001', '1425907/9999999', '1425907/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425907/00000001', '11425907/9999999', '11425907/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 908, '1425908/00000001', '1425908/9999999', '1425908/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425908/00000001', '11425908/9999999', '11425908/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 909, '1425909/00000001', '1425909/9999999', '1425909/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425909/00000001', '11425909/9999999', '11425909/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 911, '1425911/00000001', '1425911/9999999', '1425911/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425911/00000001', '11425911/9999999', '11425911/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 912, '1425912/00000001', '1425912/9999999', '1425912/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425912/00000001', '11425912/9999999', '11425912/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 913, '1425913/00000001', '1425913/9999999', '1425913/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425913/00000001', '11425913/9999999', '11425913/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 914, '1425914/00000001', '1425914/9999999', '1425914/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425914/00000001', '11425914/9999999', '11425914/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 915, '1425915/00000001', '1425915/9999999', '1425915/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425915/00000001', '11425915/9999999', '11425915/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 916, '1425916/00000001', '1425916/9999999', '1425916/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425916/00000001', '11425916/9999999', '11425916/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 917, '1425917/00000001', '1425917/9999999', '1425917/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425917/00000001', '11425917/9999999', '11425917/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 918, '1425918/00000001', '1425918/9999999', '1425918/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425918/00000001', '11425918/9999999', '11425918/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 919, '1425919/00000001', '1425919/9999999', '1425919/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425919/00000001', '11425919/9999999', '11425919/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 920, '1425920/00000001', '1425920/9999999', '1425920/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425920/00000001', '11425920/9999999', '11425920/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 921, '1425921/00000001', '1425921/9999999', '1425921/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425921/00000001', '11425921/9999999', '11425921/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 922, '1425922/00000001', '1425922/9999999', '1425922/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425922/00000001', '11425922/9999999', '11425922/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 923, '1425923/00000001', '1425923/9999999', '1425923/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425923/00000001', '11425923/9999999', '11425923/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 924, '1425924/00000001', '1425924/9999999', '1425924/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425924/00000001', '11425924/9999999', '11425924/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 950, '1425950/00000001', '1425950/9999999', '1425950/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425950/00000001', '11425950/9999999', '11425950/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 951, '1425951/00000001', '1425951/9999999', '1425951/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425951/00000001', '11425951/9999999', '11425951/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 952, '1425952/00000001', '1425952/9999999', '1425952/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425952/00000001', '11425952/9999999', '11425952/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 953, '1425953/00000001', '1425953/9999999', '1425953/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425953/00000001', '11425953/9999999', '11425953/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 954, '1425954/00000001', '1425954/9999999', '1425954/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425954/00000001', '11425954/9999999', '11425954/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 955, '1425955/00000001', '1425955/9999999', '1425955/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425955/00000001', '11425955/9999999', '11425955/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 956, '1425956/00000001', '1425956/9999999', '1425956/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425956/00000001', '11425956/9999999', '11425956/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 957, '1425957/00000001', '1425957/9999999', '1425957/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425957/00000001', '11425957/9999999', '11425957/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 958, '1425958/00000001', '1425958/9999999', '1425958/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425958/00000001', '11425958/9999999', '11425958/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 959, '1425959/00000001', '1425959/9999999', '1425959/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425959/00000001', '11425959/9999999', '11425959/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 960, '1425960/00000001', '1425960/9999999', '1425960/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425960/00000001', '11425960/9999999', '11425960/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 961, '1425961/00000001', '1425961/9999999', '1425961/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425961/00000001', '11425961/9999999', '11425961/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 962, '1425962/00000001', '1425962/9999999', '1425962/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425962/00000001', '11425962/9999999', '11425962/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 963, '1425963/00000001', '1425963/9999999', '1425963/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425963/00000001', '11425963/9999999', '11425963/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 964, '1425964/00000001', '1425964/9999999', '1425964/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425964/00000001', '11425964/9999999', '11425964/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 965, '1425965/00000001', '1425965/9999999', '1425965/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425965/00000001', '11425965/9999999', '11425965/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 966, '1425966/00000001', '1425966/9999999', '1425966/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425966/00000001', '11425966/9999999', '11425966/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 967, '1425967/00000001', '1425967/9999999', '1425967/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425967/00000001', '11425967/9999999', '11425967/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 968, '1425968/00000001', '1425968/9999999', '1425968/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425968/00000001', '11425968/9999999', '11425968/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 969, '1425969/00000001', '1425969/9999999', '1425969/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425969/00000001', '11425969/9999999', '11425969/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 970, '1425970/00000001', '1425970/9999999', '1425970/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425970/00000001', '11425970/9999999', '11425970/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 972, '1425972/00000001', '1425972/9999999', '1425972/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425972/00000001', '11425972/9999999', '11425972/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 973, '1425973/00000001', '1425973/9999999', '1425973/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425973/00000001', '11425973/9999999', '11425973/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 974, '1425974/00000001', '1425974/9999999', '1425974/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425974/00000001', '11425974/9999999', '11425974/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 975, '1425975/00000001', '1425975/9999999', '1425975/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425975/00000001', '11425975/9999999', '11425975/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 976, '1425976/00000001', '1425976/9999999', '1425976/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425976/00000001', '11425976/9999999', '11425976/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 977, '1425977/00000001', '1425977/9999999', '1425977/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425977/00000001', '11425977/9999999', '11425977/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 978, '1425978/00000001', '1425978/9999999', '1425978/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425978/00000001', '11425978/9999999', '11425978/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 979, '1425979/00000001', '1425979/9999999', '1425979/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425979/00000001', '11425979/9999999', '11425979/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 980, '1425980/00000001', '1425980/9999999', '1425980/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425980/00000001', '11425980/9999999', '11425980/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 981, '1425981/00000001', '1425981/9999999', '1425981/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425981/00000001', '11425981/9999999', '11425981/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 982, '1425982/00000001', '1425982/9999999', '1425982/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425982/00000001', '11425982/9999999', '11425982/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 983, '1425983/00000001', '1425983/9999999', '1425983/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425983/00000001', '11425983/9999999', '11425983/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 984, '1425984/00000001', '1425984/9999999', '1425984/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425984/00000001', '11425984/9999999', '11425984/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 985, '1425985/00000001', '1425985/9999999', '1425985/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425985/00000001', '11425985/9999999', '11425985/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 986, '1425986/00000001', '1425986/9999999', '1425986/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425986/00000001', '11425986/9999999', '11425986/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 987, '1425987/00000001', '1425987/9999999', '1425987/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425987/00000001', '11425987/9999999', '11425987/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 988, '1425988/00000001', '1425988/9999999', '1425988/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425988/00000001', '11425988/9999999', '11425988/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 989, '1425989/00000001', '1425989/9999999', '1425989/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425989/00000001', '11425989/9999999', '11425989/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 990, '1425990/00000001', '1425990/9999999', '1425990/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425990/00000001', '11425990/9999999', '11425990/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 991, '1425991/00000001', '1425991/9999999', '1425991/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425991/00000001', '11425991/9999999', '11425991/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 992, '1425992/00000001', '1425992/9999999', '1425992/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425992/00000001', '11425992/9999999', '11425992/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 993, '1425993/00000001', '1425993/9999999', '1425993/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425993/00000001', '11425993/9999999', '11425993/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 994, '1425994/00000001', '1425994/9999999', '1425994/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425994/00000001', '11425994/9999999', '11425994/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 995, '1425995/00000001', '1425995/9999999', '1425995/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425995/00000001', '11425995/9999999', '11425995/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 996, '1425996/00000001', '1425996/9999999', '1425996/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425996/00000001', '11425996/9999999', '11425996/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 997, '1425997/00000001', '1425997/9999999', '1425997/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425997/00000001', '11425997/9999999', '11425997/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 998, '1425998/00000001', '1425998/9999999', '1425998/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425998/00000001', '11425998/9999999', '11425998/00000001'),
('2025-04-25 12:45:46', '2025-04-25 12:45:46', 'SYSTEM', 'SYSTEM', 'CREDIT_NOTE', 'FINANCE_DISCOUNT_CN', 14, 25, 999, '1425999/00000001', '1425999/9999999', '1425999/00000001', '0', '2025-04-25 12:45:46', 0, 114, '11425999/00000001', '11425999/9999999', '11425999/00000001');