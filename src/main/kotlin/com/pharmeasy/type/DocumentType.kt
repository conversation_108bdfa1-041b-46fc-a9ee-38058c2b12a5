package com.pharmeasy.type

enum class DocumentType {
    PURCHASE_INVOICE, DEBIT_NOTE_PR, CREDIT_NOTE_PR, VENDOR_PAYMENT, PAYMENT_ROUND_OFF, ADJUSTMENT,
    DN_SR_ACCEPTED, DN_SR_EXPIRED, SALE_INVOICE, CX_RECEIPT, RECEIPT_ROUND_OFF, CN_SR_ACCEPTED, CN_SR_EXPIRED,
    CREDIT_NOTE_DISCOUNT, DEBIT_NOTE_DISCOUNT, CX_CN_DISCOUNT, CX_DN_DISCOUNT, VN_CN_DISCOUNT, VN_DN_DISCOUNT,
    DEBIT_NOTE_NSR, CREDIT_NOTE_NSR, CHEQUE_BOUNCED, ADVANCE_PAYMENT,DN_ST_RETURN, DN_ICS_RETURN,CN_ST_RETURN, CN_ICS_RETURN,
    CHEQUE_BOUNCED_CHARGE,DN_SR_ACCEPTED_B2B, DN_SR_EXPIRED_B2B,DN_SR_ACCEPTED_B2C, DN_SR_EXPIRED_B2C, CN_SR_ACCEPTED_B2B, CN_SR_EXPIRED_B2B,
    CN_SR_ACCEPTED_B2C, CN_SR_EXPIRED_B2C, CN_ADVANCE_PAYMENT, DN_ADVANCE_PAYMENT, WRITE_OFF,CN_SETOFF,INVOICE_SETOFF, DIGITAL_RECEIPT,PAYMENT_REVERSAL,RECEIPT_ENTRY,
    RETAILER_CN_DN,RETAILER_ADHOC_DN,RETAILER_BOUNCED_DN,RETAILER_DN, TRADE_CREDIT, TRADE_CREDIT_REPAYMENT, FINANCE_DISCOUNT_CN}