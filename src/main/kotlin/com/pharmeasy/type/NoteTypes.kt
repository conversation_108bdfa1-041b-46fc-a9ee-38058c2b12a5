package com.pharmeasy.type

enum class NoteTypes {
    PURCHASE_RETURN, D<PERSON><PERSON>UN<PERSON>, SR_ACCEPTED, SR_REJECTED, SR_NON_SOLD_RETURNS,
    SR_EXPIRED, NSR,ST_RETURN,ICS_RETURN, SLAB_DISCOUNT_CN, SLAB_DISCOUNT_DN, ADVANCE_PAYMENT,
    OFFLINE_PROMOTION_DISCOUNT_CN,OTC_MARGIN_LEAKAGE_DISCOUNT_CN,SALES_INCENTIVE_DISCOUNT_CN,OFFLINE_PROMOTION_DISCOUNT_DN,OTC_MARGIN_LEAKAGE_DISCOUNT_DN,
    SALES_INCENTIVE_DISCOUNT_DN,PURCHASE_SLAB_DISCOUNT_CN,PURCHASE_SLAB_DISCOUNT_DN,NSR_ACCEPTED,NSR_EXPIRED,NSR_DAMAGED,SR_DAMAGED,FINANCE_DISCOUNT_CN,FINANCE_DISCOUNT_DN
}