package com.pharmeasy.type

enum class PaymentType {
    CASH, CHEQUE, NEFT, CREDITNOTE, ADVANCE_PAYMENT, RIO_PAY, UPI, DIRECT_PAYMENT, OTHERS, TRADE_CREDIT;

    val particular: String
        get() = when (this) {
            CASH -> "Paid By Cash"
            CHEQUE -> "Paid By Cheque"
            NEFT -> "Paid By NEFT"
            CREDITNOTE -> "Paid By Credit Note"
            ADVANCE_PAYMENT -> "Paid By Advance Payment"
            RIO_PAY -> "Paid By RIO Pay"
            UPI -> "Paid By UPI QR Code"
            DIRECT_PAYMENT -> ""
            OTHERS -> ""
            TRADE_CREDIT -> "Paid By Trade Credit"
        }
}
