package com.pharmeasy.proxy

import com.pharmeasy.model.PartnerGeneralDataResponseDTO
import org.springframework.cloud.openfeign.FeignClient
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestParam

@FeignClient(name = "partner-master-proxy", url = "partner-master-service")
interface PartnerMasterProxy {


    @GetMapping("/partners-details")
    fun getPartnerGeneralData(
        @RequestParam("partner_detail_ids", required = false) partnerDetailIds: List<Long>? = null,
        @RequestParam("fields", required = false) fields: List<String>? = null,
        @RequestParam("page", required = false) page: Int? = null,
        @RequestParam("size", required = false) size: Int? = null,
        @RequestParam("force") force: Boolean? = false
    ): PartnerGeneralDataResponseDTO?

}

