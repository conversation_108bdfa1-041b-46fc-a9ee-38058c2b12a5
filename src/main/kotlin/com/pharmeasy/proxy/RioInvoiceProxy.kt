package com.pharmeasy.proxy

import org.springframework.cloud.openfeign.FeignClient
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.multipart.MultipartFile
import java.io.File


@FeignClient(name = "rioInvoice-proxy", url = "https://distributor-store.svc.staging.gorio.in/")
interface RioInvoiceProxy {
    @RequestMapping("distributorStoreInvoices/myInvoices/usingFile?fileZipped=true",  method = [RequestMethod.POST])
    fun sendRioData(@RequestParam("file") file: MultipartFile)
}