package com.pharmeasy.proxy

import com.pharmeasy.model.PartnerBlockedProcessDTO
import com.pharmeasy.model.Supplier
import com.pharmeasy.model.SupplierListDTO
import com.pharmeasy.util.name_header
import com.pharmeasy.annotation.Get
import com.pharmeasy.data.DistributorPartnerMapping
import com.pharmeasy.model.*
import org.springframework.cloud.openfeign.FeignClient
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.*

// change: url
@FeignClient(name = "supplier-proxy", url = "partner-master-service")
interface SupplierProxy {

    @RequestMapping("partners/info/accounts", method = [RequestMethod.GET], consumes = [MediaType.APPLICATION_JSON_VALUE], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun supplier(@RequestParam("partnerId") supplierId: List<Long?>?,
                 @RequestParam("partnerDetailId") partnerDetailId: Long? = null): List<Supplier?>

    @RequestMapping("/partner/profile/partnerId", method = [RequestMethod.GET], consumes = [MediaType.APPLICATION_JSON_VALUE], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun getMasterId(@RequestParam("partnerDetailId") pDId: Long): Long?

    @RequestMapping("/partner/profile/partner-address-detail", method = [RequestMethod.POST], consumes = [MediaType.APPLICATION_JSON_VALUE], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun allSupplier(@RequestBody partnerDetailIds: List<Long?>): List<SupplierDetailDTO?>

    @RequestMapping("/partner/profile/valid-list", method = [RequestMethod.POST], consumes = [MediaType.APPLICATION_JSON_VALUE], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun allTotalSupplier(
        @RequestParam("include_blocked") include_blocked: Boolean,
        @RequestBody PartnerDetailIdList: List<Long?>): List<SupplierListDTO?>

    @RequestMapping("/firm-types", method = [RequestMethod.GET], produces = [MediaType.APPLICATION_JSON_VALUE])
    @ResponseBody
    fun getFirmTypes(
            @RequestParam("retailer") retailer: Boolean?,
            @RequestParam("distributor") distributor: Boolean?,
            @RequestParam("manufacturer") manufacturer: Boolean?
    ): List<FirmDTO?>?

    @Get("partner/profile/v2/all")
    fun getPartnerDetails(
            @RequestParam("partnerName") partnerName: String?,
            @RequestParam("firmTypes") firmTypes: List<Int>?,
            @RequestParam("gst") gst: String?,
            @RequestParam("cityId") cityId: Int?,
            @RequestParam("partnerDetailId") id: Int?,
            @RequestParam("tenantCodes") tenantCodes: String? = null,
            @RequestParam("withBankDetail")withBankDetail: Boolean? = false
    ): MutableList<PartnerDetailDto>?

    @RequestMapping("/partners/blocked", method = [RequestMethod.PUT], consumes = [MediaType.APPLICATION_JSON_VALUE], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun blockPartners(@RequestBody partnerBlockProcessList: List<PartnerBlockedProcessDTO>,@RequestHeader(name_header) user: String)


    @RequestMapping("/partner/profile/detail", method = [RequestMethod.GET], consumes = [MediaType.APPLICATION_JSON_VALUE], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun supplierByTenant(@RequestParam("tenant") tenant: String): List<SupplierByTenantDTO?>

    @Get("/store/external/retailers")
    fun getStoreExternalRetailerMappingList(@RequestParam("externalPartnerDetailId") externalPartnerDetailId: Int?,@RequestParam("enabled") enabled: Boolean?) : RetailerPaginationDto

    @RequestMapping("partners", method = [RequestMethod.GET], consumes = [MediaType.APPLICATION_JSON_VALUE], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun partners(@RequestParam("partnerId") partnerId: Long?): PartnersDto?

    @GetMapping("/partners-details/{DistributorPDI}/distributor/warehouse/mapping")
    fun getDistributorPartnerMapping(@PathVariable("DistributorPDI") distributorPdi: Long): DistributorPartnerMapping

    @Get("partner/accounts/retailer")
    fun getPaymentTerms(@RequestParam("partnerId")partnerId:Long?): PartnerPaymentTermsDto


    @RequestMapping("/v2/partners-details", method = [RequestMethod.GET], consumes = [MediaType.APPLICATION_JSON_VALUE], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun partnerGenericDto(@RequestParam("partner_detail_ids") partnerDetailIds: MutableList<Long>): PartnerGenericDetailDTO?


    @RequestMapping("/partners/ids")
    fun getPartnersByFirmId(@RequestParam("firmTypeIds")firmTypeIds:List<Int>): List<PartnerByFirmTypeDto?>
    @RequestMapping("partners/retailer", method = [RequestMethod.GET], consumes = [MediaType.APPLICATION_JSON_VALUE], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun retailerInfo(@RequestParam("partnerDetailIds") partnerDetailIds: Long): List<RetailerDetailDTO?>

    @RequestMapping("/partner/profile", method = [RequestMethod.GET])
    fun getPartnerDetailsByPartnerId(@RequestParam("partnerId") partnerId: Long,
                                     @RequestParam("include_disabled") include_disabled: Boolean): PartnerProfileDto

    @GetMapping("/partners-details")
    fun getPartnerGeneralData(@RequestParam("partner_detail_ids",required = false) partnerDetailIds:List<Long>?=null,
                              @RequestParam("fields",required = false) fields: List<String>?=null,
                              @RequestParam("page",required = false) page:Int?=null,
                              @RequestParam("size", required = false) size:Int?=null,
                              @RequestParam("force")force:Boolean?=false): PartnerGeneralDataResponseDTO?

}

