package com.pharmeasy.proxy

import com.pharmeasy.dto.RetailerPartner
import org.springframework.cloud.openfeign.FeignClient
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable

@FeignClient(name = "core-service-proxy", url = "core-service")
interface CoreServiceProxy {

    @GetMapping("/retailer/partner/{partnerDetailId}")
    fun getRetailerByPartnerDetailId(@PathVariable("partnerDetailId") partnerDetailId: Long): RetailerPartner?

}