package com.pharmeasy.model

import com.pharmeasy.data.DeliveryChallanTaxLog
import com.pharmeasy.type.DeliveryChallanLogEntryStatusType
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime

data class DCCallBackDto (
        val eventId : Long,
        val tenant: String,
        val dcNumber: String,
        val setoffValue: Double,
        val type: DeliveryChallanLogEntryStatusType,
        val originalValue:Double,
        val setoffTime: LocalDateTime,
        val referenceNumberSetOff: String?,
        val referenceDateSetOff: LocalDate?,
        val referenceAmountSetOff: Double?,
        val reference2NumberSetOff: String?,
        val reference2DateSetOff: LocalDate?,
        val reference2AmountSetOff: Double?,
        val taxRates : MutableList<DeliveryChallanTaxLog>?,
        val remark : String?,
        val roundOffAmount: Double? = 0.0
)
