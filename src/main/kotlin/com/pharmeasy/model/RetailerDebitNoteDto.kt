package com.pharmeasy.model

import com.pharmeasy.type.DnType
import com.pharmeasy.type.UseCaseType

class RetailerDebitNoteDto(
    var remarks: String?,
    var type: DnType,
    var amount: Double,
    var amountReceived: Double?,
    var taxableValue: Double?,
    var taxRate: Double?, // This is tax amount
    var taxPercent: Double, // this is tax %
    var partnerDetailId: Long,
    var refId: Long?,
    var useCase: UseCaseType?,
    var isGstnApplicable: Boolean?,
    var hsn: String?,
    var migrationReferenceId: String? = "",
    val isMigrated: Boolean? = false,
    val uuid: String,
) {
    fun validate(): List<String> {
        val errors = mutableListOf<String>()
        if (isGstnApplicable == true && hsn.isNullOrEmpty()) {
            errors.add("HSN is mandatory when isGstnApplicable is true")
        }
        if (type != DnType.ADHOC && type != DnType.CREDIT_NOTE && type != DnType.BOUNCE_CHARGE) {
            errors.add("Type should be either ADHOC or CREDIT_NOTE or BOUNCE_CHARGE")
        }
        if (amountReceived!! < 0) {
            errors.add("Amount received cannot be negative")
        }
        if (amount < 0) {
            errors.add("Amount cannot be negative")
        }
        if (type != DnType.CREDIT_NOTE && taxableValue != null && taxRate != null && taxableValue!! < 0) {
            errors.add("Taxable value cannot be negative")
        }
        if (type != DnType.CREDIT_NOTE && taxRate != null && taxRate!! < 0) {
            errors.add("Tax rate cannot be negative")
        }

        if (type != DnType.CREDIT_NOTE && taxPercent < 0) {
            errors.add("Tax percent cannot be negative")
        }

        if (partnerDetailId <= 0) {
            errors.add("Partner detail ID should be greater than 0")
        }

        if (taxPercent > 100) {
            errors.add("Tax rate cannot be greater than 100")
        }

        val totalAmountCalculated = ((taxRate ?: 0.0) + (taxableValue ?: 0.0))

        if (type != DnType.CREDIT_NOTE && totalAmountCalculated <= 0 || amount <= 0) {
            errors.add("Tax rate + taxable value cannot be less than or equal to 0")
        }

        if (type != DnType.CREDIT_NOTE && Math.abs(totalAmountCalculated - amount) >= 1) {
            errors.add("Tax rate + taxable value cannot be greater than amount")
        }

        // If type is DnType.CREDIT_NOTE or DnType.BOUNCE_CHARGE, then refId should not be null
        if ((type == DnType.CREDIT_NOTE || type == DnType.BOUNCE_CHARGE) && (refId == null || refId == 0L)) {
            errors.add("Ref ID should not be null  or 0 when type is CREDIT_NOTE or BOUNCE_CHARGE")
        }

        if (uuid.isBlank()) {
            errors.add("UUID should not be null or blank")
        }

        if (taxableValue != null && taxRate != null && hsn.isNullOrBlank()) {
            errors.add("HSN is mandatory when taxable value and tax rate are provided")
        }

        //TODO : Add validation for valid HSN
        return errors
    }
}

