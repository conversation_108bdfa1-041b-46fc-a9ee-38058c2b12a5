package com.pharmeasy.model

import com.pharmeasy.type.DeliveryChallanLogEntryStatusType
import com.pharmeasy.type.DeliveryChallanStatusType
import java.time.LocalDate

data class DeliveryChallanSetOffDto(
    var type: DeliveryChallanLogEntryStatusType,
    var setOffAmt:Double,
    var refAmt:Double?,
    var refNum: String?,
    var refDate: LocalDate?,
    var refAmt2:Double?,
    var refNum2: String?,
    var refDate2: LocalDate?,

    var dcId:Long,
//    var dcList: MutableList<DcData>,
    var taxList:MutableList<TaxData>?,
    var fileList: MutableList<FileUploadMetaDto>?,
    var remark: String?,
    var roundOffAmt: Double? = 0.0
)
data class DcData(
    var id:Long,
    var dcNumber:String,
    var dcAmtUsed: Double,
    var dcStatus: DeliveryChallanStatusType
)

data class TaxData(
    var hsn:String?,
    var taxPercentage:Int?,
    var taxableAmt:Double?,
    var taxAmt: Double?,
    var grossAmt:Double
)
