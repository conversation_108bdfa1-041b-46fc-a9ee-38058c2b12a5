package com.pharmeasy.model.ops

import com.fasterxml.jackson.annotation.JsonIgnore
import com.pharmeasy.type.CreationType
import com.pharmeasy.type.PaymentType
import com.pharmeasy.type.ops.PaymentPartnerType
import java.util.Locale.getDefault

data class RIOPaymentDTO(
    var retailerTxnId: String? = null,
    var retailerTxnDate: Long? = null,
    var retailerName: String? = null,
    var retailerPartyCode: String? = null,
    var retailerFrontEndPartyCode: String? = null,
    var distributorInvoiceId: String? = null,
    var distributorInvoiceAmount: Double? = null,
    var distributorInvoiceOutstandingAmount: Double? = null,
    var retailerTxnAmount: Double? = null,
    var distributorInvoiceDueDate: Long? = null,
    var retailerTxnType: String? = null,
    var initiatedBy: String? = null,
    var retailerTxnStatus: String? = null,
    var advanceAmount: Double? = null,
    var paymentMode: String? = null,
    var invoicePrefix: String? = null,
    var distributorInvoiceDate: Long? = null,
    var category: String? = null,
    var invoiceNumber: String? = null,
    var salesmanName: String? = null,
    var salesmanId: String? = null,
    var chequeNo: String? = null,
    var chequeDate: Long? = null,
    var neftId: String? = null,
    var bankName: String? = null,
    var retailerTotalTxnAmount: Double? = null,
    var distributorId: Long? = null,
    var type: CreationType? = CreationType.INVOICE,
    var creditTransactionId: String? = null,
    var creditDueDate: Long? = null,
    var creditPartner: PaymentPartnerType? = null,
    var isBankDeposit: Boolean? = false,
    var bankDepositSlipNo: String? = null,
) {
    @get:JsonIgnore
    val partnerDetailId: Long?
        get() = if (retailerFrontEndPartyCode.isNullOrBlank()) null else retailerFrontEndPartyCode!!.toLong()

    @get:JsonIgnore
    val paymentType: PaymentType
        get() =
            when (retailerTxnType?.uppercase(getDefault())) {
                "CHEQUE" -> PaymentType.CHEQUE
                "CASH" -> PaymentType.CASH
                "NEFT" -> PaymentType.NEFT
                "DIRECT PAYMENT" -> PaymentType.DIRECT_PAYMENT
                "CREDIT"-> {
                    if(creditPartner == PaymentPartnerType.TRADE_CREDIT){
                        PaymentType.TRADE_CREDIT
                    }else{
                        PaymentType.RIO_PAY
                    }
                }
                else -> PaymentType.RIO_PAY
            }
}

data class RioPaymentData(
    var data: MutableList<RIOPaymentDTO>
)
