package com.pharmeasy.model

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.pharmeasy.type.SlabTaskStatus
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime

data class SlabCreateDto(
    val slabs: List<SlabDto>
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class SlabDto(
    val minAmount: BigDecimal,
    val maxAmount: BigDecimal?,
    val discountPercent: Double,
    val lastUpdatedOn: LocalDateTime?
)

data class SlabFileDto(
    val s3Url: String,
    val objectKey: String,
    val startDate: LocalDate,
    val endDate: LocalDate,
    val uuid: String?
)

data class SlabTaskDto(
    val taskId: Long,
    val taskDate: LocalDateTime,
    var createdBy: String,
    var approvedBy: String?,
    val approvedOn: LocalDateTime?,
    val status: SlabTaskStatus,
    var partnerFile: String?,
    var draftFile: String?
)