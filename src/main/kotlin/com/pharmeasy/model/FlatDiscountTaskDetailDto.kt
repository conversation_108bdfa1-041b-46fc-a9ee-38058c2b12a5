package com.pharmeasy.model

import com.pharmeasy.data.FlatDiscountTask
import com.pharmeasy.type.InvoiceType
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.math.BigDecimal
import java.time.LocalDateTime
import javax.persistence.*

data class FlatDiscountTaskDetailDto(
    var id: Long,
    val createdOn: LocalDateTime,
    val updatedOn: LocalDateTime,
    val partnerId: Long,
    val partnerDetailId: Long,
    val partnerName: String,
    val discountAmount: BigDecimal,
    val taxableAmount: BigDecimal,
    val taxAmount: BigDecimal,
    val sgst: Int,
    val cgst: Int,
    val igst: Int,
    val stateId: Long,
    val client: InvoiceType,
    val flatDiscountTask: FlatDiscountTask,
    val raisedBy: String?,
    val remark: String?,
    val reason: String?
)
