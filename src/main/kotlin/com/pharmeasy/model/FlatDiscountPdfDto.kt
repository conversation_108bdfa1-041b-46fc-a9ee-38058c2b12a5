package com.pharmeasy.model.advancepayment

import java.math.BigDecimal
import java.time.LocalDate

data class FlatDiscountCreditNotePrintDto(
    val creditNoteNumber: String,
    val creditNoteDate: LocalDate,
    var supplierName: String,
    var supplierAddress: String,
    var customerName: String,
    var customerAddress: String,
    var creditNoteAmountInWords: String,
    val cnDetail: FlatDiscountCreditNoteDetail,
    var supplierEmail: String?,
    var supplierGstin: String?,
    var emailCustomer: String,
    var customerGstin: String?,
    var supplierSignature: String?,
    var type: String?,
    var remark: String?,
    var toDL: String?,
    var fromDL: String?,
    var irn: String?,
    var qrCode: String?,
    var reasonTitle: String?,
    var reasonDesc: String?
)

data class FlatDiscountCreditNoteDetail(
    val period: String,
    val discountTax: Double,
    val taxableValue: BigDecimal,
    val partnerId: Long,
    val partnerDetailId: Long,
    val cgst: BigDecimal,
    val sgst: BigDecimal,
    val igst: BigDecimal,
    val total: BigDecimal,
    val createdOn: LocalDate,
    val gstPercent: Int,
    val hsnCode: String
)