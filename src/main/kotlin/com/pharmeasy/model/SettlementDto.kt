package com.pharmeasy.model

import com.pharmeasy.type.PaymentType
import java.time.LocalDate

data class SettlementDto(
    var partnerDetailId: Long,
    var paymentType: PaymentType,
    var paymentReferenceNumber: String,
    var paymentDate: LocalDate,
    var bankName: String? = null,
    var settledAmount: Double,
    var settledInvoiceDetails: List<SettledInvoiceDetails>,
    var settledRetailerDNDetails: List<SettledRetailerDNDetails>,
    var paymentTypeReferenceIds: List<Long>,
    var uuid: String
)

data class SettledInvoiceDetails(
    var invoiceId: Long,
    var paidAmount: Double
)

data class SettledRetailerDNDetails(
    var retailerDebitNoteId: Long,
    var paidAmount: Double
)