package com.pharmeasy.model.advancepayment

import com.pharmeasy.type.AdvancePaymentSource
import com.pharmeasy.type.PaymentType
import java.math.BigDecimal
import java.time.LocalDate

data class UpdateAdvancePaymentDto(
        var amount: BigDecimal? = null,
        var paymentReference : String? = null,
        var paymentType: PaymentType? = null,
        var paymentDate : LocalDate? = null,
        var chequeDate : LocalDate? = null,
        var bankName : String? = null,
        var remarks : String? = null,
        var tenant : String,
        var change : Boolean,
        var source: AdvancePaymentSource? = AdvancePaymentSource.SYSTEM,
        var isRioTransaction: Boolean? = false
)