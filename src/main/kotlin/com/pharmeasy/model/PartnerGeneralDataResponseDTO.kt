package com.pharmeasy.model

import java.time.LocalTime

data class PartnerGeneralDataResponseDTO(
    val elementsCount: Long? = null,
    val pagesCount: Long? = null,
    val hasPrevious: Boolean? = null,
    val hasNext: Boolean? = null,
    var data: List<GeneralData>? = null
)

data class GeneralData(
    var partnerDetailId: Long? = null,
    val b2cSalesEnabled: Boolean? = null,
    val sales: Sales? = null,
    val theaMapping: TheaMapping? = null,
    val partnerData: PartnerData? = null,
    val information: InformationGeneralDTO? = null,
    val accounts: AccountsDataDTONew? = null
)

data class InformationGeneralDTO(
    val id: Int? = null,
    val gst: String?= null,
    val state: String?= null
)


data class AccountsDataDTONew(
    var retailer: DistributorCommercialsDTO? = null
)

data class DistributorCommercialsDTO(
    var tcsPercentage: Float? = null
)

data class PartnerData(
    val id: Long,
    val blockedReason: String? = null,
    val name: String? = null,
    val isBlocked: Boolean? = null,
    val blockedProcess: List<BlockedProcess>? = null
)

data class BlockedProcess(
    val id: Int? = null,
    val processTypeId: Int? = null,
    val processType: String? = null,
    val blockedBy: String? = null,
    val isBlocked: Boolean? = null
)

data class Sales(
    val retailerDetails: RetailerDetails? = null
)

data class RetailerDetails(
    var marketPlaceERP: String? = null,
    var workStartingTime: LocalTime?=null,
    var workEndingTime: LocalTime?=null,
    var orderAcceptanceTatInSeconds: Int?=null,
    var billingTatInSeconds: Int?=null,
    var externalId: String? = null
)

data class TheaMapping(
    val tenant: String? =null,
    var warehouseFirmType: List<FirmTypeDto>? = null
)

data class FirmTypeDto(
    var id: Int? = null,
    var name: String? = null
)