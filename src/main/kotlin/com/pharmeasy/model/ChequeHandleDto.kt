package com.pharmeasy.model

import com.pharmeasy.type.ChequeHandleType
import com.pharmeasy.type.InvoiceType
import com.pharmeasy.type.PartnerType
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime

data class ChequeHandleDto(

        var id: Long,

        var chequeNum: String? = null,

        var customerName: String,

        var customerCode: Long,

        var bank: String?=null,

        var amt: Double,

        var chequeDate: LocalDate? = null,

        var updatedOn: LocalDateTime?,

        var settlementNumber: String?,

        var status: ChequeHandleType?,

        var reason : String? = null,

        var assignTo: String? = null,

        var inApprove: Boolean? = null,

        var partnarDetailId: Long? = null,

        var charge:Boolean? = false,

        var chargeAmt: Double? = 0.0,

        var type: PartnerType,

        var advancePaymentNumber: String?,

        var bounceDate: LocalDateTime? = null,

        var assignedTo: String?= null,

        var settlementAmount: Double? = 0.0
){
    constructor(l1: Long, s1: String?, s2: String, l2: Long, s3: String?, d1: Double, ld1: LocalDate?, ld2: LocalDateTime?,
        s4: String?, ch1: ChequeHandleType?, s5: String?, s6: String?, b1: Boolean?, l3: Long?,b2: Boolean?, d2: Double?,
        pt1: PartnerType, s7: String): this(id = l1, chequeNum = s1, customerName = s2,  customerCode = l2,  bank = s3,
        amt = d1, chequeDate = ld1, updatedOn = ld2, settlementNumber = s4, status = ch1, reason = s5, assignTo = s6,
        inApprove = b1, partnarDetailId = l3, charge = b2, chargeAmt = d2, type = pt1, advancePaymentNumber = s7)


}