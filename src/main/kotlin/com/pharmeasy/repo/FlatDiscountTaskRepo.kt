package com.pharmeasy.repo

import com.pharmeasy.data.FlatDiscountTask
import com.pharmeasy.type.SlabTaskStatus
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

interface FlatDiscountTaskRepo: JpaRepository<FlatDiscountTask, Long> {

    @Transactional
    @Modifying
    @Query("update FlatDiscountTask ft set ft.status = :status where ft.id = :taskId")
    fun updateTaskStatus(@Param("taskId") taskId: Long,@Param("status") status: SlabTaskStatus): Int

    @Query("select st from FlatDiscountTask st where" +
            " ((:fromDateTime = null and :toDateTime = null) or (st.createdOn between :fromDateTime and :toDateTime))" +
            " and ((:status = null) or (st.status = :status))" +
            " and st.company.id = :companyId")
    fun findAllTasks(@Param("fromDateTime") fromDateTime: LocalDateTime?,
                     @Param("toDateTime") toDateTime: LocalDateTime?,
                     @Param("status") status: SlabTaskStatus?,
                     @Param("companyId") companyId: Long,
                     pageable: Pageable
    ): Page<FlatDiscountTask?>

    @Query(" SELECT fdk from FlatDiscountTask fdk WHERE fdk.id =:id")
    fun getById(@Param("id") id:Long): FlatDiscountTask?

    @Query("SELECT fdk from FlatDiscountTask fdk where uuid =:uuid")
    fun findByUUID(@Param("uuid") uuid: String): FlatDiscountTask?
}