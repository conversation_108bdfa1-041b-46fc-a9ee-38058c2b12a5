package com.pharmeasy.repo

import com.pharmeasy.data.CashBankLedger
import com.pharmeasy.model.ChequeHandleDto
import com.pharmeasy.type.ChequeHandleType
import com.pharmeasy.type.LedgerEntryType
import com.pharmeasy.type.PartnerType
import com.pharmeasy.type.PaymentMode
import com.pharmeasy.type.SettlementType
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import java.math.BigDecimal
import java.time.LocalDate

interface CashBankLedgerRepo : JpaRepository<CashBankLedger, Long> , JpaSpecificationExecutor<CashBankLedger> {

    @Query(" SELECT cbl FROM CashBankLedger cbl WHERE cbl.companyId = :companyId " +
            "AND cbl.paymentMode = :paymentMode " +
            "AND (:documentNumber = null OR cbl.documentNumber = :documentNumber OR cbl.referenceNumber = :documentNumber ) " +
            "AND (:externalReferenceNumber = null OR cbl.externalReferenceNumber = :externalReferenceNumber) " +
            "AND (:documentType = null OR cbl.documentType = :documentType) " +
            "AND ((:fromDate = null AND :toDate = null) OR (cbl.transactionDate >= :fromDate AND cbl.transactionDate <= :toDate))")
    fun getCashBankLedgerData(
            @Param("paymentMode") paymentMode: PaymentMode,
            @Param("companyId") companyId: Long,
            @Param("documentNumber") documentNumber: String?,
            @Param("externalReferenceNumber") externalReferenceNumber: String?,
            @Param("documentType") documentType: SettlementType?,
            @Param("fromDate") fromDate: LocalDate?,
            @Param("toDate") toDate: LocalDate?,
            pageable: Pageable): Page<CashBankLedger>


    @Query(" SELECT cbl FROM CashBankLedger cbl WHERE cbl.companyId = :companyId " +
            "AND cbl.paymentMode = :paymentMode " +
            "AND (:documentNumber = null OR cbl.documentNumber = :documentNumber OR cbl.referenceNumber = :documentNumber ) " +
            "AND (:externalReferenceNumber = null OR cbl.externalReferenceNumber = :externalReferenceNumber) " +
            "AND (:documentType = null OR cbl.documentType = :documentType) " +
            "AND ((:fromDate = null AND :toDate = null) OR (cbl.transactionDate >= :fromDate AND cbl.transactionDate <= :toDate))")
    fun getCashBankLedgerDataForUrl(
            @Param("paymentMode") paymentMode: PaymentMode,
            @Param("companyId") companyId: Long,
            @Param("documentNumber") documentNumber: String?,
            @Param("externalReferenceNumber") externalReferenceNumber: String?,
            @Param("documentType") documentType: SettlementType?,
            @Param("fromDate") fromDate: LocalDate?,
            @Param("toDate") toDate: LocalDate?): MutableList<CashBankLedger?>

    @Query("SELECT cbl.balance FROM CashBankLedger cbl WHERE cbl.companyId = :companyId AND cbl.paymentMode = :paymentMode and cbl.transactionDate <= :day order by cbl.transactionDate desc, cbl.id desc")
    fun findLastTransactionBalance(@Param("day") day: LocalDate, @Param("companyId") companyId: Long, @Param("paymentMode") paymentMode: PaymentMode, pageable: Pageable): Page<BigDecimal?>


    @Query("""
            SELECT cbl
            FROM CashBankLedger cbl
            WHERE cbl.documentNumber = :documentNumber
            AND cbl.referenceNumber = :referenceNumber
            AND cbl.ledgerEntryType = :type
        """)
    fun findDup(documentNumber: String, referenceNumber: String, type: LedgerEntryType): CashBankLedger?

}
