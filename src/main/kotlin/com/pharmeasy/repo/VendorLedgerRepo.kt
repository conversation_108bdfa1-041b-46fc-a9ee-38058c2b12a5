package com.pharmeasy.repo

import com.pharmeasy.data.VendorLedger
import com.pharmeasy.model.LedgerData
import com.pharmeasy.model.LedgerReconDTO
import com.pharmeasy.model.VendorClientDto
import com.pharmeasy.type.DocumentType
import com.pharmeasy.type.InvoiceType
import com.pharmeasy.type.LedgerEntryType
import com.pharmeasy.type.PartnerType
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Slice
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime

interface VendorLedgerRepo : JpaRepository<VendorLedger, Long> , JpaSpecificationExecutor<VendorLedger> {

    @Query("SELECT vl FROM VendorLedger vl WHERE vl.documentNumber = :documentNumber AND vl.referenceNumber = :referenceNumber")
    fun findDup(@Param("documentNumber") documentNumber: String,
                @Param("referenceNumber") referenceNumber: String): VendorLedger?

    @Query( "select new com.pharmeasy.model.LedgerData(vl.partnerId, vl.vendorName, '', sum(vl.balance), sum(vl.creditAmount - vl.debitAmount) , v.balance, vl.client, vl.partnerDetailId) from VendorLedger vl JOIN Partner v on v.partnerId = vl.partnerId where vl.companyId = :companyId AND v.companyId = :companyId AND vl.partnerId IN :vendorId AND v.type = :custType AND vl.type = :custType AND ((:from = null AND :to = null) OR (vl.transactionDate >= :from AND vl.transactionDate <= :to)) AND ((:amt1 = null AND :amt2 = null) OR v.balance >= :amt1 and v.balance <= :amt2) AND (:partnerDetailId = null OR vl.partnerDetailId = :partnerDetailId) group by vl.partnerId")
    abstract fun getVendorLedgerViewDataVendorFilterPage(@Param("vendorId") vendorId: List<Long>, @Param("from") from: LocalDate?, @Param("to") to: LocalDate?, @Param("amt1") amt1: BigDecimal?, @Param("amt2") amt2: BigDecimal?, pageable: Pageable,@Param("companyId") companyId: Long,@Param("custType") custType: PartnerType,@Param("partnerDetailId") partnerDetailId: Long?): Slice<LedgerData?>

    @Query( "select new com.pharmeasy.model.LedgerData(vl.partnerId, vl.vendorName, '', vl.balance, sum(vl.creditAmount - vl.debitAmount) , v.balance, vl.client, vl.partnerDetailId) from VendorLedger vl JOIN Partner v on v.partnerId = vl.partnerId where vl.companyId = :companyId AND v.companyId = :companyId AND v.type = :custType AND vl.type = :custType AND ((:from = null AND :to = null) OR (vl.transactionDate >= :from AND vl.transactionDate <= :to)) AND ((:amt1 = null AND :amt2 = null) OR v.balance >= :amt1 and v.balance <= :amt2) AND (:partnerDetailId = null OR vl.partnerDetailId = :partnerDetailId) group by vl.partnerId")
    abstract fun getVendorLedgerViewDataWOVendorFilterPage(@Param("from") from: LocalDate?, @Param("to") to: LocalDate?, @Param("amt1") amt1: BigDecimal?, @Param("amt2") amt2: BigDecimal?, pageable: Pageable,@Param("companyId") companyId: Long,@Param("custType") custType: PartnerType,@Param("partnerDetailId") partnerDetailId: Long?): Slice<LedgerData?>

    @Query("SELECT vl.balance FROM VendorLedger vl WHERE vl.companyId = :companyId AND vl.type = :custType and vl.partnerId = :vendorId and vl.transactionDate <= :day order by vl.transactionDate desc, vl.id desc")
    fun findOpeningBalance(@Param("vendorId") vendorId: Long,@Param("day") day: LocalDate, @Param("companyId") companyId: Long,@Param("custType") custType: PartnerType,pageable: Pageable): Page<BigDecimal?>

    @Query("SELECT vl.balance FROM VendorLedger vl WHERE vl.companyId = :companyId and vl.partnerId = :vendorId order by vl.id desc")
    fun findCurrentBalance(@Param("vendorId") vendorId: Long, @Param("companyId") companyId: Long,pageable: Pageable): Page<BigDecimal?>

    @Query( "select new com.pharmeasy.model.LedgerData(vl.partnerId, vl.vendorName, '', vl.balance, sum(vl.creditAmount - vl.debitAmount), v.balance, vl.client) from VendorLedger vl JOIN Partner v on v.partnerId = vl.partnerId where vl.companyId = :companyId AND v.companyId = :companyId AND v.type = :custType AND vl.type = :custType AND vl.partnerId IN :vendorId AND ((:from = null AND :to = null) OR (vl.transactionDate >= :from AND vl.transactionDate <= :to)) AND ((:amt1 = null AND :amt2 = null) OR v.balance >= :amt1 and v.balance <= :amt2) group by vl.partnerId ")
    abstract fun getVendorLedgerViewDataWithVendorId(@Param("vendorId") vendorId: List<Long>, @Param("from") from: LocalDate?, @Param("to") to: LocalDate?, @Param("amt1") amt1: BigDecimal?, @Param("amt2") amt2: BigDecimal?,@Param("companyId") companyId: Long,@Param("custType") custType: PartnerType): MutableList<LedgerData?>?

    @Query( "select new com.pharmeasy.model.LedgerData(vl.partnerId, vl.vendorName, '', vl.balance, sum(vl.creditAmount - vl.debitAmount), v.balance, vl.client) from VendorLedger vl JOIN Partner v on v.partnerId = vl.partnerId where vl.companyId = :companyId AND v.companyId = :companyId AND v.type =:custType AND vl.type =:custType AND ((:from = null AND :to = null) OR (vl.transactionDate >= :from AND vl.transactionDate <= :to)) AND ((:amt1 = null AND :amt2 = null) OR v.balance >= :amt1 and v.balance <= :amt2) group by vl.partnerId")
    abstract fun getVendorLedgerViewData( @Param("from") from: LocalDate?, @Param("to") to: LocalDate?, @Param("amt1") amt1: BigDecimal?, @Param("amt2") amt2: BigDecimal?,@Param("companyId") companyId: Long,@Param("custType") custType: PartnerType): MutableList<LedgerData?>?

    @Query( "SELECT * from bk_vendor_ledger WHERE transaction_date <= :date and company_id = :companyId and partner_id = :partner and type = :custType ORDER BY transaction_date DESC, id DESC LIMIT 1",nativeQuery = true)
    abstract fun getLastVendorLedgerForDay(@Param("date") date: LocalDate,@Param("companyId") companyId: Long,@Param("partner") partner: Long ,@Param("custType") custType: String): VendorLedger?

    @Query( "SELECT vl FROM VendorLedger vl WHERE vl.transactionDate > :date AND companyId = :companyId and partnerId = :partner and type =:custType")
    abstract fun getForUpdateVendorLedger(@Param("date") date: LocalDate,@Param("companyId") companyId: Long,@Param("partner") partner: Long,@Param("custType") custType: PartnerType): List<VendorLedger?>

    @Query(" SELECT bki.partnerDetailId from VendorLedger bki where bki.companyId = :companyId and bki.partnerDetailId is not null and bki.type = :custType group by bki.partnerDetailId")
    fun getAllPartnerDetailId(@Param("companyId") companyId: Long,@Param("custType") custType: PartnerType): List<Long?>

    @Query( "select new com.pharmeasy.model.LedgerData(vl.partnerId, vl.vendorName, '', vl.balance, " +
            "sum(vl.debitAmount - vl.creditAmount), v.balance, vl.client) from VendorLedger vl JOIN Partner v on v.partnerId = vl.partnerId " +
            "where vl.companyId = :companyId AND v.companyId = :companyId AND v.type = :custType AND vl.type = :custType " +
            "AND vl.partnerId IN :vendorId AND ((:from = null AND :to = null) OR (vl.transactionDate >= :from AND vl.transactionDate <= :to)) " +
            "AND ((:amt1 = null AND :amt2 = null) OR v.balance >= :amt1 and v.balance <= :amt2) group by vl.partnerId ")
    abstract fun getCustomerLedgerViewDataWithCustomerId(@Param("vendorId") vendorId: List<Long>,
                                                         @Param("from") from: LocalDate?,
                                                         @Param("to") to: LocalDate?,
                                                         @Param("amt1") amt1: BigDecimal?,
                                                         @Param("amt2") amt2: BigDecimal?,
                                                         @Param("companyId") companyId: Long,
                                                         @Param("custType") custType: PartnerType): MutableList<LedgerData?>?

    @Query( "select new com.pharmeasy.model.LedgerData(vl.partnerId, vl.vendorName, '', vl.balance, " +
            "sum(vl.debitAmount - vl.creditAmount), v.balance, vl.client) from VendorLedger vl JOIN Partner v on v.partnerId = vl.partnerId " +
            "where vl.companyId = :companyId AND v.companyId = :companyId AND v.type =:custType AND vl.type =:custType " +
            "AND ((:from = null AND :to = null) OR (vl.transactionDate >= :from AND vl.transactionDate <= :to)) " +
            "AND ((:amt1 = null AND :amt2 = null) OR v.balance >= :amt1 and v.balance <= :amt2) group by vl.partnerId")
    abstract fun getCustomerLedgerViewData( @Param("from") from: LocalDate?,
                                            @Param("to") to: LocalDate?,
                                            @Param("amt1") amt1: BigDecimal?,
                                            @Param("amt2") amt2: BigDecimal?,
                                            @Param("companyId") companyId: Long,
                                            @Param("custType") custType: PartnerType): MutableList<LedgerData?>?

    @Query( "select new com.pharmeasy.model.LedgerData(vl.partnerId, vl.vendorName, '', sum(vl.balance), " +
            "sum(vl.debitAmount - vl.creditAmount) , v.balance, vl.client, vl.partnerDetailId) from VendorLedger vl " +
            "JOIN Partner v on v.partnerId = vl.partnerId where vl.companyId = :companyId " +
            "AND (:partnerDetailId = null OR vl.partnerDetailId = :partnerDetailId) " +
            "AND v.companyId = :companyId AND vl.partnerId IN :vendorId AND v.type = :custType " +
            "AND vl.type = :custType AND ((:from = null AND :to = null) OR (vl.transactionDate >= :from AND vl.transactionDate <= :to)) " +
            "AND ((:amt1 = null AND :amt2 = null) OR v.balance >= :amt1 and v.balance <= :amt2) group by vl.partnerId")
    abstract fun getCustomerLedgerViewDataCustomerFilterPage(@Param("vendorId") vendorId: List<Long>,
                                                             @Param("from") from: LocalDate?,
                                                             @Param("to") to: LocalDate?,
                                                             @Param("amt1") amt1: BigDecimal?,
                                                             @Param("amt2") amt2: BigDecimal?, pageable: Pageable,
                                                             @Param("companyId") companyId: Long,
                                                             @Param("custType") custType: PartnerType,
                                                             @Param("partnerDetailId") partnerDetailId: Long?): Slice<LedgerData?>

    @Query( "select new com.pharmeasy.model.LedgerData(vl.partnerId, vl.vendorName, '', vl.balance, " +
            "sum(vl.debitAmount - vl.creditAmount) , v.balance, vl.client, vl.partnerDetailId) from VendorLedger vl JOIN Partner v on v.partnerId = vl.partnerId " +
            "where vl.companyId = :companyId AND v.companyId = :companyId AND v.type = :custType AND vl.type = :custType " +
            "AND (:partnerDetailId = null OR vl.partnerDetailId = :partnerDetailId) " +
            "AND ((:from = null AND :to = null) OR (vl.transactionDate >= :from AND vl.transactionDate <= :to)) " +
            "AND ((:amt1 = null AND :amt2 = null) OR v.balance >= :amt1 and v.balance <= :amt2) group by vl.partnerId")
    abstract fun getCustomerLedgerViewDataWOCustomerFilterPage(@Param("from") from: LocalDate?,
                                                               @Param("to") to: LocalDate?,
                                                               @Param("amt1") amt1: BigDecimal?,
                                                               @Param("amt2") amt2: BigDecimal?, pageable: Pageable,
                                                               @Param("companyId") companyId: Long,
                                                               @Param("custType") custType: PartnerType,
                                                               @Param("partnerDetailId") partnerDetailId: Long?): Slice<LedgerData?>

    @Query(" SELECT new com.pharmeasy.model.VendorClientDto (vl.partnerId, vl.client) " +
            "FROM VendorLedger vl WHERE ((:from = null AND :to = null) OR (vl.transactionDate >= :from AND vl.transactionDate <= :to)) " +
            "AND (vl.tenant in :tenants) and vl.type = :custType AND (vl.partnerId in :partnerId)  GROUP BY vl.partnerId , vl.client" )
    fun getVendorClient(@Param("from") from: LocalDate?,
                        @Param("to") to: LocalDate?,
                        @Param("custType") custType: PartnerType,
                        @Param("tenants") tenants: MutableList<String?>,
                        @Param("partnerId") partnerId: MutableList<Long?>): List<VendorClientDto>


    @Query( "select new com.pharmeasy.model.LedgerData(vl.partnerId, vl.vendorName, '', vl.balance, " +
            "sum(vl.debitAmount - vl.creditAmount) , v.balance, vl.client,vl.partnerDetailId) from VendorLedger vl JOIN Partner v on v.partnerId = vl.partnerId " +
            "where vl.companyId = :companyId AND v.companyId = :companyId AND v.type = :custType AND vl.type = :custType " +
            "AND ((:from = null AND :to = null) OR (vl.transactionDate >= :from AND vl.transactionDate <= :to)) " +
            "AND ((:amt1 = null AND :amt2 = null) OR v.balance >= :amt1 and v.balance <= :amt2) group by vl.partnerId")
    abstract fun getCustomerLedgerViewDataWOCustomerFilterPageSummaryUrl(@Param("from") from: LocalDate?,
                                                               @Param("to") to: LocalDate?,
                                                               @Param("amt1") amt1: BigDecimal?,
                                                               @Param("amt2") amt2: BigDecimal?,
                                                               @Param("companyId") companyId: Long,
                                                               @Param("custType") custType: PartnerType): MutableList<LedgerData?>?
    @Query( "select new com.pharmeasy.model.LedgerData(vl.partnerId, vl.vendorName, '', vl.balance, " +
            "sum(vl.debitAmount - vl.creditAmount) , v.balance, vl.client, vl.partnerDetailId) from VendorLedger vl JOIN Partner v on v.partnerId = vl.partnerId " +
            "where vl.companyId = :companyId AND v.companyId = :companyId AND v.type = :custType AND vl.type = :custType " +
            "AND  (vl.transactionDate >= :from AND vl.transactionDate <= :to) " +
            "AND (:partnerDetailId = null OR vl.partnerDetailId = :partnerDetailId) " +
            " group by vl.partnerId")
    abstract fun getCustomerLedgerViewDataWOCustomerFilterPageAndAmount(@Param("from") from: LocalDate?,
                                                                        @Param("to") to: LocalDate?,
                                                                        pageable: Pageable,
                                                                        @Param("companyId") companyId: Long,
                                                                        @Param("custType") custType: PartnerType,
                                                                        @Param("partnerDetailId") partnerDetailId: Long?): Slice<LedgerData?>

    @Query( "select new com.pharmeasy.model.LedgerData(vl.partnerId, vl.vendorName, '', vl.balance,sum(vl.debitAmount - vl.creditAmount) , v.balance, vl.client,vl.partnerDetailId) from VendorLedger vl JOIN Partner v on v.partnerId = vl.partnerId where vl.companyId = :companyId AND v.companyId = :companyId AND v.type = :custType AND vl.type = :custType AND ((:from = null AND :to = null) OR (vl.transactionDate >= :from AND vl.transactionDate <= :to)) AND ((:amt1 = null AND :amt2 = null) OR v.balance >= :amt1 and v.balance <= :amt2) AND vl.client in (:client) group by vl.partnerId")
    abstract fun getCustomerLedgerViewFirmDataWOCustomerFilterPageSummaryUrl(@Param("from") from: LocalDate?,@Param("to") to: LocalDate?,@Param("amt1") amt1: BigDecimal?,@Param("amt2") amt2: BigDecimal?,@Param("companyId") companyId: Long,@Param("custType") custType: PartnerType,@Param("client") client: List<InvoiceType>): MutableList<LedgerData?>?

    @Query( "select new com.pharmeasy.model.LedgerData(vl.partnerId, vl.vendorName, '', vl.balance, sum(vl.creditAmount - vl.debitAmount) , v.balance, vl.client,vl.partnerDetailId) from VendorLedger vl JOIN Partner v on v.partnerId = vl.partnerId where vl.companyId = :companyId AND v.companyId = :companyId AND v.type = :custType AND vl.type = :custType AND ((:from = null AND :to = null) OR (vl.transactionDate >= :from AND vl.transactionDate <= :to)) AND ((:amt1 = null AND :amt2 = null) OR v.balance >= :amt1 and v.balance <= :amt2) group by vl.partnerId")
    abstract fun getVendorLedgerViewDataForVendorFilterSummaryUrl(@Param("from") from: LocalDate?, @Param("to") to: LocalDate?, @Param("amt1") amt1: BigDecimal?, @Param("amt2") amt2: BigDecimal?, @Param("companyId") companyId: Long,@Param("custType") custType: PartnerType): MutableList<LedgerData?>?



    @Query("SELECT new com.pharmeasy.model.LedgerReconDTO(vl.partnerId,vl.documentType,vl.documentNumber,vl.referenceNumber,vl.externalReferenceNumber,vl.creditAmount,vl.client) " +
            "FROM VendorLedger vl WHERE vl.createdOn >= :from  AND vl.createdOn <= :to " +
            "AND (vl.documentType in :docType) " +
            "AND ((vl.documentNumber in :docNo) OR (vl.referenceNumber in :docNo) OR (vl.externalReferenceNumber in :docNo)) AND vl.companyId = :companyId " +
            "AND vl.type = :custType AND vl.client = :client AND vl.partnerId = :partnerId ")
    fun getLedgerDataForReconCredit(@Param("from") from: LocalDateTime,
                                    @Param("to") to: LocalDateTime,
                                    @Param("docType") docType: MutableList<DocumentType?>,
                                    @Param("docNo") docNo: MutableList<String?>, @Param("custType") custType: PartnerType,@Param("client") client: InvoiceType,@Param("partnerId") partnerId: Long,@Param("companyId") companyId: Long ): MutableList<LedgerReconDTO?>

    @Query("SELECT new com.pharmeasy.model.LedgerReconDTO(vl.partnerId,vl.documentType,vl.documentNumber,vl.referenceNumber,vl.externalReferenceNumber,vl.debitAmount,vl.client) " +
            "FROM VendorLedger vl WHERE vl.createdOn >= :from  AND vl.createdOn <= :to " +
            "AND (vl.documentType in :docType) " +
            "AND ((vl.documentNumber in :docNo) OR (vl.referenceNumber in :docNo) OR (vl.externalReferenceNumber in :docNo)) AND vl.companyId = :companyId " +
            "AND vl.type = :custType AND vl.client = :client AND vl.partnerId = :partnerId ")
    fun getLedgerDataForReconDebit(@Param("from") from: LocalDateTime,
                              @Param("to") to: LocalDateTime,
                              @Param("docType") docType: MutableList<DocumentType?>,
                              @Param("docNo") docNo: MutableList<String?>, @Param("custType") custType: PartnerType,@Param("client") client: InvoiceType,@Param("partnerId") partnerId: Long,@Param("companyId") companyId: Long ): MutableList<LedgerReconDTO?>


    @Query("SELECT new com.pharmeasy.model.LedgerReconDTO(vl.partnerId,vl.documentType,vl.documentNumber,vl.referenceNumber,vl.externalReferenceNumber,vl.creditAmount,vl.client) " +
            "FROM VendorLedger vl WHERE vl.createdOn >= :from  AND vl.createdOn <= :to " +
            "AND (vl.documentType in :docType) " +
            "AND (vl.documentNumber not in :docNo) AND (vl.referenceNumber not in :docNo) AND (vl.externalReferenceNumber not in :docNo) AND vl.companyId = :companyId " +
            "AND vl.type = :custType AND vl.client = :client AND vl.partnerId = :partnerId ")
    fun getLedgerVaultNoMatchDataForReconCredit(@Param("from") from: LocalDateTime,
                                    @Param("to") to: LocalDateTime,
                                    @Param("docType") docType: MutableList<DocumentType?>,
                                    @Param("docNo") docNo: MutableList<String?>, @Param("custType") custType: PartnerType,@Param("client") client: InvoiceType,@Param("partnerId") partnerId: Long,@Param("companyId") companyId: Long ): MutableList<LedgerReconDTO?>

    @Query("SELECT new com.pharmeasy.model.LedgerReconDTO(vl.partnerId,vl.documentType,vl.documentNumber,vl.referenceNumber,vl.externalReferenceNumber,vl.debitAmount,vl.client) " +
            "FROM VendorLedger vl WHERE vl.createdOn >= :from  AND vl.createdOn <= :to " +
            "AND (vl.documentType in :docType) " +
            "AND (vl.documentNumber not in :docNo) AND (vl.referenceNumber not in :docNo) AND (vl.externalReferenceNumber not in :docNo) AND vl.companyId = :companyId " +
            "AND vl.type = :custType AND vl.client = :client AND vl.partnerId = :partnerId ")
    fun getLedgerVaultNoMatchDataForReconDebit(@Param("from") from: LocalDateTime,
                                   @Param("to") to: LocalDateTime,
                                   @Param("docType") docType: MutableList<DocumentType?>,
                                   @Param("docNo") docNo: MutableList<String?>, @Param("custType") custType: PartnerType,@Param("client") client: InvoiceType,@Param("partnerId") partnerId: Long,@Param("companyId") companyId: Long ): MutableList<LedgerReconDTO?>


    @Query("SELECT new com.pharmeasy.model.LedgerReconDTO(vl.partnerId,vl.documentType,vl.documentNumber,vl.referenceNumber,vl.externalReferenceNumber,vl.creditAmount,vl.client) " +
            "FROM VendorLedger vl WHERE vl.createdOn >= :from  AND vl.createdOn <= :to " +
            "AND (vl.documentType in :docType) " +
            "AND vl.ledgerEntryType = :entryType " +
            "AND ((vl.documentNumber in :docNo) OR (vl.referenceNumber in :docNo) OR (vl.externalReferenceNumber in :docNo)) AND vl.companyId = :companyId " +
            "AND vl.type = :custType AND vl.client = :client AND vl.partnerId = :partnerId ")
    fun getLedgerDataForReconAdjustmentCredit(@Param("from") from: LocalDateTime,
                                    @Param("to") to: LocalDateTime,
                                    @Param("docType") docType: MutableList<DocumentType?>,
                                    @Param("docNo") docNo: MutableList<String?>, @Param("custType") custType: PartnerType,@Param("client") client: InvoiceType,@Param("partnerId") partnerId: Long,@Param("companyId") companyId: Long, @Param("entryType") entryType: LedgerEntryType = LedgerEntryType.CREDIT  ): MutableList<LedgerReconDTO?>

    @Query("SELECT new com.pharmeasy.model.LedgerReconDTO(vl.partnerId,vl.documentType,vl.documentNumber,vl.referenceNumber,vl.externalReferenceNumber,vl.debitAmount,vl.client) " +
            "FROM VendorLedger vl WHERE vl.createdOn >= :from  AND vl.createdOn <= :to " +
            "AND (vl.documentType in :docType) " +
            "AND vl.ledgerEntryType = :entryType " +
            "AND ((vl.documentNumber in :docNo) OR (vl.referenceNumber in :docNo) OR (vl.externalReferenceNumber in :docNo)) AND vl.companyId = :companyId " +
            "AND vl.type = :custType AND vl.client = :client AND vl.partnerId = :partnerId ")
    fun getLedgerDataForReconAdjustmentDebit(@Param("from") from: LocalDateTime,
                                   @Param("to") to: LocalDateTime,
                                   @Param("docType") docType: MutableList<DocumentType?>,
                                   @Param("docNo") docNo: MutableList<String?>, @Param("custType") custType: PartnerType,@Param("client") client: InvoiceType,@Param("partnerId") partnerId: Long,@Param("companyId") companyId: Long, @Param("entryType") entryType: LedgerEntryType= LedgerEntryType.DEBIT ): MutableList<LedgerReconDTO?>
    @Query("SELECT new com.pharmeasy.model.LedgerReconDTO(vl.partnerId,vl.documentType,vl.documentNumber,vl.referenceNumber,vl.externalReferenceNumber,vl.creditAmount,vl.client) " +
            "FROM VendorLedger vl WHERE vl.createdOn >= :from  AND vl.createdOn <= :to " +
            "AND (vl.documentType in :docType) " +
            "AND vl.ledgerEntryType = :entryType " +
            "AND ((vl.documentNumber not in :docNo) AND (vl.referenceNumber not in :docNo) AND (vl.externalReferenceNumber not in :docNo)) AND vl.companyId = :companyId " +
            "AND vl.type = :custType AND vl.client = :client AND vl.partnerId = :partnerId ")
    fun getLedgerNoMatchDataForReconAdjustmentCredit(@Param("from") from: LocalDateTime,
                                              @Param("to") to: LocalDateTime,
                                              @Param("docType") docType: MutableList<DocumentType?>,
                                              @Param("docNo") docNo: MutableList<String?>, @Param("custType") custType: PartnerType,@Param("client") client: InvoiceType,@Param("partnerId") partnerId: Long,@Param("companyId") companyId: Long, @Param("entryType") entryType: LedgerEntryType = LedgerEntryType.CREDIT ): MutableList<LedgerReconDTO?>

    @Query("SELECT new com.pharmeasy.model.LedgerReconDTO(vl.partnerId,vl.documentType,vl.documentNumber,vl.referenceNumber,vl.externalReferenceNumber,vl.debitAmount,vl.client) " +
            "FROM VendorLedger vl WHERE vl.createdOn >= :from  AND vl.createdOn <= :to " +
            "AND (vl.documentType in :docType) " +
            "AND vl.ledgerEntryType = :entryType " +
            "AND ((vl.documentNumber not in :docNo) AND (vl.referenceNumber not in :docNo) AND (vl.externalReferenceNumber not in :docNo)) AND vl.companyId = :companyId " +
            "AND vl.type = :custType AND vl.client = :client AND vl.partnerId = :partnerId ")
    fun getLedgerNoMatchDataForReconAdjustmentDebit(@Param("from") from: LocalDateTime,
                                             @Param("to") to: LocalDateTime,
                                             @Param("docType") docType: MutableList<DocumentType?>,
                                             @Param("docNo") docNo: MutableList<String?>, @Param("custType") custType: PartnerType,@Param("client") client: InvoiceType,@Param("partnerId") partnerId: Long,@Param("companyId") companyId: Long, @Param("entryType") entryType: LedgerEntryType = LedgerEntryType.DEBIT  ): MutableList<LedgerReconDTO?>


    @Query(" SELECT bki.partnerId from VendorLedger bki where bki.companyId = :companyId and bki.type = 'CUSTOMER' and bki.client = 'RIO' group by bki.partnerId")
    fun getAllRIOPartnerId(@Param("companyId") companyId: Long): List<Long?>

    @Query("SELECT p from VendorLedger p where p.partnerDetailId = :partnerDetailId " +
            "and p.tenant = :tenant and p.documentType = :type and p.type = 'CUSTOMER' and client = :client and companyId = :companyId")
    fun checkMigratedPartner(@Param("partnerDetailId")partnerDetailId: Long,
                             @Param("tenant")tenant: String,
                             @Param("type")type: DocumentType,
                             @Param("client")client: InvoiceType,
                             @Param("companyId")companyId: Long): VendorLedger?
    @Query("select v from VendorLedger v where v.partnerDetailId= :partnerDetailId and v.referenceNumber = :referenceNumber and documentType = :documentType")
    fun getLedgerByPartnerDetailIdAndReferenceNumber(@Param("partnerDetailId") partnerDetailId: Long, @Param("referenceNumber") referenceNumber: String, @Param("documentType")documentType: DocumentType): List<VendorLedger>


    @Query("""
            SELECT v FROM VendorLedger v
            WHERE v.documentNumber = :documentNumber
            AND v.referenceNumber = :referenceNumber
            AND v.ledgerEntryType = :ledgerEntryType
            """)
    fun findDuplicateLedger(
        @Param("documentNumber") documentNumber: String,
        @Param("referenceNumber") referenceNumber: String,
        @Param("ledgerEntryType") ledgerEntryType: LedgerEntryType
    ): VendorLedger?

}
