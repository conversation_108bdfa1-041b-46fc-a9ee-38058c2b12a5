package com.pharmeasy.repo.advancepayment

import com.pharmeasy.data.AdvancePayment
import com.pharmeasy.model.advancepayment.AggregatedAdvancePaymentDto
import com.pharmeasy.model.advancepayment.VendorAdvancePaymentDto
import com.pharmeasy.type.AdvanceType
import com.pharmeasy.type.InvoiceType
import com.pharmeasy.type.PartnerType
import com.pharmeasy.type.PaymentType
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.data.jpa.repository.Lock
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import java.math.BigDecimal
import java.time.LocalDate
import javax.persistence.LockModeType

interface AdvancePaymentRepo: JpaRepository<AdvancePayment, Long>, JpaSpecificationExecutor<AdvancePayment> {

    @Query("SELECT new com.pharmeasy.model.advancepayment.VendorAdvancePaymentDto(adp.partnerId, adp.partnerDetailId, '', COUNT(*), " +
            "SUM(case when adp.amountPending = 0 Then 1 else 0 END), " +
            "SUM(case when adp.amountPending > 0 Then 1 else 0 END), " +
            "SUM(case when adp.amountPending > 0 Then adp.amountPending else 0 END)) " +
            "FROM AdvancePayment adp WHERE adp.status = 'APPROVED' AND adp.client in (:client) " +
            "AND (adp.tenant in :tenants) AND type =:type GROUP BY adp.partnerId ")
    fun getVendorAdvancePayment(@Param("tenants") tenants: MutableList<String?>,
                                @Param("client") client: List<InvoiceType>,
                                @Param("type") type: PartnerType,
                                pageable: Pageable): Page<VendorAdvancePaymentDto>

    @Query("SELECT new com.pharmeasy.model.advancepayment.VendorAdvancePaymentDto(adp.partnerId, adp.partnerDetailId, '', COUNT(*), " +
            "SUM(case when adp.amountPending = 0 Then 1 else 0 END), " +
            "SUM(case when adp.amountPending > 0 Then 1 else 0 END), " +
            "SUM(case when adp.amountPending > 0 Then adp.amountPending else 0 END)) " +
            "FROM AdvancePayment adp WHERE adp.status = 'APPROVED' AND adp.client in (:client) " +
            "AND (adp.partnerDetailId = :partnerDetailId) AND (adp.tenant in :tenants) AND type =:type ")
    fun getVendorAdvancePaymentWithPartnerDetailId(@Param("tenants") tenants: MutableList<String?>,
                                              @Param("partnerDetailId") partnerDetailId: Long,
                                                   @Param("client") client: List<InvoiceType>,
                                                   @Param("type") type: PartnerType,pageable: Pageable): Page<VendorAdvancePaymentDto>


    @Query("SELECT new com.pharmeasy.model.advancepayment.VendorAdvancePaymentDto(adp.partnerId, adp.partnerDetailId, '', COUNT(*), " +
            "SUM(case when adp.amountPending = 0 Then 1 else 0 END), " +
            "SUM(case when adp.amountPending > 0 Then 1 else 0 END), " +
            "SUM(case when adp.amountPending > 0 Then adp.amountPending else 0 END)) " +
            "FROM AdvancePayment adp WHERE adp.status = 'APPROVED' AND adp.client in (:client) " +
            "AND (adp.partnerId IN :partnerIds) AND (adp.tenant in :tenants) AND type =:type GROUP BY adp.partnerId ")
    fun getVendorAdvancePaymentWithPartnerIds(@Param("tenants") tenants: MutableList<String?>,
                                              @Param("partnerIds") partnerIds: List<Long>,
                                              @Param("client") client: List<InvoiceType>,
                                              @Param("type") type: PartnerType,
                                              pageable: Pageable): Page<VendorAdvancePaymentDto>

    @Query("SELECT new com.pharmeasy.model.advancepayment.AggregatedAdvancePaymentDto(SUM(case when adp.amountPending > 0 Then 1 else 0 END), " +
            "SUM(case when adp.amountPending > 0 Then adp.amountPending else 0 END)) " +
            "FROM AdvancePayment adp WHERE adp.status = 'APPROVED' AND adp.client in (:client) " +
            "AND (adp.tenant in :tenants) ")
    fun getAggregatedAdvPayment(@Param("tenants") tenants: MutableList<String?>,@Param("client") client: List<InvoiceType>): AggregatedAdvancePaymentDto

    @Query("SELECT new com.pharmeasy.model.advancepayment.AggregatedAdvancePaymentDto(SUM(case when adp.amountPending > 0 Then 1 else 0 END), " +
            "SUM(case when adp.amountPending > 0 Then adp.amountPending else 0 END)) " +
            "FROM AdvancePayment adp WHERE adp.status = 'APPROVED' AND adp.client  in (:client) " +
            "AND (adp.partnerId IN :partnerIds) AND (adp.tenant in :tenants) ")
    fun getAggregatedAdvPaymentWithPartnerIds(@Param("tenants") tenants: MutableList<String?>,
                                              @Param("partnerIds") partnerIds: List<Long>?,@Param("client") client: List<InvoiceType>): AggregatedAdvancePaymentDto


    @Query("SELECT new com.pharmeasy.model.advancepayment.AggregatedAdvancePaymentDto(SUM(case when adp.amountPending > 0 Then 1 else 0 END), " +
            "SUM(case when adp.amountPending > 0 Then adp.amountPending else 0 END)) " +
            "FROM AdvancePayment adp WHERE adp.status = 'APPROVED' AND adp.client  in (:client) " +
            "AND (adp.partnerDetailId = :partnerDetailId) AND (adp.tenant in :tenants) ")
    fun getAggregatedAdvPaymentWithPartnerDetailId(@Param("tenants") tenants: MutableList<String?>,
                                              @Param("partnerDetailId") partnerDetailId: Long?,@Param("client") client: List<InvoiceType>): AggregatedAdvancePaymentDto


    @Query("SELECT adp FROM AdvancePayment adp JOIN adp.refDocuments rd WHERE adp.partnerId = :id " +
            "AND (adp.tenant in :tenants) AND adp.status = 'APPROVED' " +
            "AND adp.consumed = :consumed " +
            "AND (:referenceNumber = null OR rd.referenceNumber = :referenceNumber) " +
            "AND (:documentId = null OR adp.documentId = :documentId) " +
            "AND (:typeOfAdvance = null OR adp.typeOfAdvance = :typeOfAdvance) " +
            "AND ((:fromDate = null AND :toDate = null) OR (adp.approvalDate >= :fromDate " +
            "AND adp.approvalDate <= :toDate)) AND adp.type = :customerType")
    fun getAdvancePayPendingOrRealized(@Param("id") id: Long,
                                     @Param("documentId") documentId: String?,
                                     @Param("referenceNumber") referenceNumber:  String?,
                                     @Param("fromDate") fromDate: LocalDate?,
                                     @Param("toDate") toDate: LocalDate?,
                                     @Param("consumed") consumed: Boolean?,
                                     @Param("typeOfAdvance") typeOfAdvance : AdvanceType?,
                                     @Param("tenants") tenants: MutableList<String?>,
                                     @Param("customerType")customerType: PartnerType,
                                       pageable: Pageable ) : Page<AdvancePayment>

    @Query("SELECT adp FROM AdvancePayment adp JOIN adp.refDocuments rd WHERE adp.partnerId = :id " +
            "AND (adp.tenant in :tenants) AND adp.status = 'APPROVED' " +
            "AND (:referenceNumber = null OR rd.referenceNumber = :referenceNumber) " +
            "AND (:documentId = null OR adp.documentId = :documentId) " +
            "AND (:typeOfAdvance = null OR adp.typeOfAdvance = :typeOfAdvance) " +
            "AND ((:fromDate = null AND :toDate = null) OR (adp.approvalDate >= :fromDate " +
            "AND adp.approvalDate <= :toDate)) AND adp.type = :customerType")
    fun getAdvancePaySupplierTotal(@Param("id") id: Long,
                                   @Param("documentId") documentId: String?,
                                   @Param("referenceNumber") referenceNumber:  String?,
                                   @Param("fromDate") fromDate: LocalDate?,
                                   @Param("toDate") toDate: LocalDate?,
                                   @Param("typeOfAdvance") typeOfAdvance : AdvanceType?,
                                   @Param("tenants") tenants: MutableList<String?>,
                                   @Param("customerType")customerType: PartnerType,
                                   pageable: Pageable) : Page<AdvancePayment>

    @Query("SELECT new com.pharmeasy.model.advancepayment.VendorAdvancePaymentDto(adp.partnerId, adp.partnerDetailId, '', COUNT(*), " +
            "SUM(case when adp.amountPending = 0 Then 1 else 0 END), " +
            "SUM(case when adp.amountPending > 0 Then 1 else 0 END), " +
            "SUM(case when adp.amountPending > 0 Then adp.amountPending else 0 END)) " +
            "FROM AdvancePayment adp WHERE adp.status = 'APPROVED'  " +
            "AND (adp.tenant in :tenants) GROUP BY adp.partnerId ")
    fun getAdvancePaymentDataForUrl(@Param("tenants") tenants:MutableList<String?>) : MutableList<VendorAdvancePaymentDto?>?

    @Query(" SELECT adv from AdvancePayment adv WHERE adv.id =:id")
    fun get(@Param("id") id:Long): AdvancePayment?

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Query("SELECT adp FROM AdvancePayment adp WHERE adp.status = 'APPROVED' AND adp.client =:client " +
            "AND adp.amountPending > 0 AND (adp.tenant in :tenants) AND adp.partnerDetailId = :pdi ")
    fun getAdvancePaymentData(@Param("tenants") tenants:MutableList<String?>,@Param("pdi") pdi:Long, @Param("client") client: InvoiceType) : MutableList<AdvancePayment?>?

    @Query(" SELECT adv from AdvancePayment adv WHERE adv.documentId =:documentId")
    fun getByDocumentId(@Param("documentId") documentId:String?): AdvancePayment?

    @Query("SELECT adv from AdvancePayment adv where adv.paymentReference = :paymentReferenceNumber and adv.tenant= :tenant")
    fun checkMigrationData(@Param("paymentReferenceNumber")paymentReferenceNumber: String?,
                           @Param("tenant")tenant: String): AdvancePayment?

    @Query("SELECT adv from AdvancePayment adv inner join ChequeHandle ch on adv.id = ch.advancePaymentId where ch.id= :chequeHandlingId")
    fun getAdvancePaymentByChequeHandlingId(@Param("chequeHandlingId") chequeHandlingId:Long):AdvancePayment?

    @Query("SELECT sum(adv.amountPending) from AdvancePayment adv where adv.tenant= :tenant and adv.partnerDetailId= :pdi and adv.status ='APPROVED' and adv.consumed = false")
    fun getAdvancePaymentOutstanding(@Param("tenant") tenant:String,@Param("pdi") pdi:Long):BigDecimal?
}