package com.pharmeasy.repo

import com.pharmeasy.data.AdjustmentEntry
import com.pharmeasy.data.CheckerDetails
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal

interface CheckerRepo: JpaRepository<CheckerDetails, Long> {
    @Query(" SELECT chk from CheckerDetails chk WHERE chk.id =:id")
    fun get(@Param("id") id:Long): CheckerDetails?

    @Query(" SELECT chk from CheckerDetails chk WHERE chk.companyId =:companyId AND chk.isActive = true AND chk.type = 'CHECKER' AND role = 'APPROVER' AND (:checkerId = null OR chk.id = :checkerId) order by chk.priority ASC ")
    fun getByCompany(@Param("companyId") companyId:Long,@Param("checkerId") checkerId:Long? = null): MutableList<CheckerDetails?>

}