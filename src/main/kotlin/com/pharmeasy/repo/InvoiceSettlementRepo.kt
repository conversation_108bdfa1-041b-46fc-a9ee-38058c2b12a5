package com.pharmeasy.repo

import com.pharmeasy.data.BkInvoice
import com.pharmeasy.data.InvoiceSettlement
import com.pharmeasy.data.InvoiceSettlementId
import com.pharmeasy.data.Settlement
import com.pharmeasy.model.InvoiceSettlementDto
import com.pharmeasy.model.InvoiceStatus
import com.pharmeasy.model.PaymentAdviceItem
import com.pharmeasy.type.PartnerType
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param

interface InvoiceSettlementRepo : JpaRepository<InvoiceSettlement, InvoiceSettlementId> {

    @Query(" SELECT i from BkInvoice i, InvoiceSettlement ist, Settlement s WHERE ist.id.settlementId = s.id " +
            "AND i.id = ist.id.invoiceId AND i.partnerId = :partnerId AND i.status in (:statuses) AND s.settlementNumber = :settlementNumber")
    fun getPageableInvoicesForSettlement(@Param("settlementNumber") settlementNumber: String,
                                         @Param("partnerId") partnerId: Long,
                                         @Param("statuses") statuses: MutableList<InvoiceStatus>,
                                         pageable: Pageable): Page<BkInvoice>

    @Query(" SELECT s from Settlement s, InvoiceSettlement ist WHERE ist.id.invoiceId = :invoiceId and s.id = ist.id.settlementId")
    fun getPageableSettlementsForInvoice(@Param("invoiceId") invoiceId: Long,
                                         pageable: Pageable): Page<Settlement>

    @Query(" SELECT s.settlementNumber from Settlement s, InvoiceSettlement ist WHERE ist.id.invoiceId = :invoiceId and s.id = ist.id.settlementId")
    fun getSettlementsForInvoice(@Param("invoiceId") invoiceId: Long
                                         ): String?

    @Query(" SELECT i from BkInvoice i, InvoiceSettlement ist WHERE ist.id.settlementId = :settlementId and i.id = ist.id.invoiceId")
    fun getInvoicesForSettlement(@Param("settlementId") settlementId: Long): List<BkInvoice>

    @Query( "SELECT new com.pharmeasy.model.InvoiceSettlementDto(s.settlementNumber, s.updatedOn, s.paymentDate, ist.amount, s.chequeDate, s.bankName, s.isBounced, s.paymentType, ist.paidAmount, s.paymentReference) " +
            "FROM Settlement s, InvoiceSettlement ist " +
            "WHERE ist.id.invoiceId = :invoiceId AND s.id = ist.id.settlementId AND s.reversed = false ")
    fun getSettlementInfoForInvoice(@Param("invoiceId") invoiceId: Long): List<InvoiceSettlementDto>

    @Query("""SELECT new com.pharmeasy.model.PaymentAdviceItem(
        COALESCE(bi.createdOn, rdn.createdOn),
        COALESCE(bi.invoiceNum, rdn.documentNumber),
        s.paymentType,
        s.paymentReference,
        s.paymentDate,
        COALESCE(bi.amount, rdn.amount),
        COALESCE(ist.paidAmount, rdnsm.paidAmount),
        COALESCE(ist.invoiceStatus, rdn.status),
        COALESCE(ist.id.settlementId, rdnsm.settlementId.id),
        s.partnerId,
        s.partnerDetailId,
        s.createdOn,
        COALESCE(bi.tenant, rdn.tenant),
        s.paidAmount,
        s.settlementNumber,
        s.bankName
    )
    FROM Settlement s
    LEFT JOIN InvoiceSettlement ist ON ist.id.settlementId = s.id
    LEFT JOIN BkInvoice bi ON bi.id = ist.id.invoiceId
    LEFT JOIN RetailerDebitNoteSettlementMapping rdnsm ON rdnsm.settlementId.id = s.id
    LEFT JOIN RetailerDebitNote rdn ON rdn.id = rdnsm.retailerDebitNoteId.id
    WHERE s.id = :settlementId""")
    fun getPaymentAdviceData(@Param("settlementId") settlementId: Long): List<PaymentAdviceItem?>?

    @Query(" SELECT s from Settlement s, InvoiceSettlement ist WHERE ist.id.invoiceId = :invoiceId and s.id = ist.id.settlementId and s.id != :settlementId and s.isBounced = false order by s.id desc")
    fun getSettlementsListForInvoice(@Param("settlementId") settlementId: Long,@Param("invoiceId") invoiceId: Long ): List<Settlement?>


    @Query( "SELECT ist FROM InvoiceSettlement ist " +
            "WHERE ist.id.settlementId = :settlementId ")
    fun getSettlementInvoiceBySettlementId(@Param("settlementId") settlementId: Long): List<InvoiceSettlement>

    @Query( "SELECT i FROM BkInvoice i inner join InvoiceSettlement ist on i.id = ist.id.invoiceId " +
            "WHERE ist.id.settlementId = :settlementId ")
    fun getInvoicesBySettlementId(@Param("settlementId") settlementId: Long): List<BkInvoice>

}