package com.pharmeasy.repo;

import com.pharmeasy.data.BulkCNReasonCodeMapping
import com.pharmeasy.model.BulkCnReasonCodeDto
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Page
import org.springframework.data.repository.query.Param


interface BulkCNReasonCodeMappingRepo : JpaRepository<BulkCNReasonCodeMapping, Long> {
    @Query("SELECT b FROM BulkCNReasonCodeMapping b WHERE b.reasonCode = :reasonCode")
    fun findByReasonCode(@Param("reasonCode")reasonCode: Long): BulkCNReasonCodeMapping?

    @Query("SELECT new com.pharmeasy.model.BulkCnReasonCodeDto(reasonCode, bulkCnCategory, bulkCnCategoryDescription) FROM BulkCNReasonCodeMapping b")
    fun getBulkCNReasonCodeMapping(pagination: Pageable): Page<BulkCnReasonCodeDto>

    @Query("SELECT b FROM BulkCNReasonCodeMapping b WHERE b.reasonCode = :reasonCode or b.bulkCnCategory = :bulkCnCategory")
    fun findByReasonCodeOrReasonCategory(@Param("reasonCode")reasonCode: Long, @Param("bulkCnCategory")bulkCnCategory: String): BulkCNReasonCodeMapping?
}
