package com.pharmeasy.repo

import com.pharmeasy.data.DraftReceipt
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.history.RevisionRepository
import java.time.LocalDate
import java.time.LocalDateTime

interface DraftReceiptRepository : JpaRepository<DraftReceipt, Long>, RevisionRepository<DraftReceipt, Long, Int> {
    @Query(
        """
        SELECT d FROM DraftReceipt d
         WHERE d.txReferenceNumber = :chequeNo
            AND d.chequeDate = :chequeDate
            AND d.partnerDetailId = :partnerDetailId
            and d.tenant = :tenant
            and d.status not in (com.pharmeasy.type.ReceiptStatus.CANCELLED, com.pharmeasy.type.ReceiptStatus.REJECTED, com.pharmeasy.type.ReceiptStatus.REVERSED)
    """
    )
    fun findDuplicateCheque(
        chequeNo: String,
        chequeDate: LocalDate,
        partnerDetailId: Long,
        tenant: String
    ): List<DraftReceipt>

    @Query(
        """
        SELECT d FROM DraftReceipt d
         WHERE d.txReferenceNumber = :neftId
            AND d.partnerDetailId = :partnerDetailId
            and d.tenant = :tenant
            and d.status not in (com.pharmeasy.type.ReceiptStatus.CANCELLED, com.pharmeasy.type.ReceiptStatus.REJECTED, com.pharmeasy.type.ReceiptStatus.REVERSED)
    """
    )
    fun findDuplicateNeftPayments(
        neftId: String,
        partnerDetailId: Long,
        tenant: String
    ): List<DraftReceipt>

    @Query(
        """
        SELECT d FROM DraftReceipt d
         WHERE d.paymentTransactionId = :retailerTxnId
            AND d.partnerDetailId = :partnerDetailId
            AND d.tenant = :tenant
            """
    )
    fun findDuplicatesByPaymentTransactionId(
        retailerTxnId: String,
        partnerDetailId: Long,
        tenant: String
    ): List<DraftReceipt>

    @Query(
        """
        SELECT COALESCE(SUM(d.amount), 0) FROM DraftReceipt d
        WHERE d.paymentType = 'CASH'
        AND d.partnerDetailId = :partnerDetailId
        AND d.createdOn >= :startDate
        AND d.createdOn < :endDate
        and d.status in (com.pharmeasy.type.ReceiptStatus.APPROVED, com.pharmeasy.type.ReceiptStatus.PENDING_CANCELLATION)
    """
    )
    fun findCashTotalForPartner(
        partnerDetailId: Long,
        startDate: LocalDateTime,
        endDate: LocalDateTime
    ): Double

    fun findByPaymentTransactionId(paymentTransactionId: String): DraftReceipt?
}
