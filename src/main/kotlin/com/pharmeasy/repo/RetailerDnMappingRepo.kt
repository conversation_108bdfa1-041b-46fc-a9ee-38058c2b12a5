package com.pharmeasy.repo

import com.pharmeasy.data.ChequeHandle
import com.pharmeasy.data.RetailerDnMapping
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import java.time.LocalDateTime
import java.util.*

interface RetailerDnMappingRepo : JpaRepository<RetailerDnMapping, Long> {

    @Query
        ("SELECT r from RetailerDnMapping r where r.creditNote.id = :creditNoteId")
    fun findByCreditNoteId(@Param("creditNoteId") creditNoteId: Long): RetailerDnMapping?

    fun findByBkChequeHandleAndBounceDate(
        bkChequeHandle: ChequeHandle,
        bounceDate: LocalDateTime
    ): Optional<RetailerDnMapping>
}