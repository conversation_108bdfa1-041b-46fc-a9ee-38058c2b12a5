package com.pharmeasy.repo

import com.pharmeasy.data.DraftReceiptEntityMapping
import com.pharmeasy.type.SettleableType
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query

interface DraftReceiptEntityMappingRepository : JpaRepository<DraftReceiptEntityMapping, Long> {
    @Query(
        """
        SELECT d FROM DraftReceiptEntityMapping d
        WHERE d.receipt.id = :receiptId
        AND d.entityId = :entityId
        AND d.entityType = :settleableType
        AND d.active = true
    """,
        nativeQuery = false
    )
    fun findExistingMapping(
        receiptId: Long,
        entityId: Long,
        settleableType: SettleableType
    ): DraftReceiptEntityMapping?
}
