package com.pharmeasy.repo

import com.pharmeasy.data.RetailerDebitNote
import com.pharmeasy.data.RetailerDebitNoteSettlementMapping
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param

interface RetailerDebitNoteSettlementMappingRepo: JpaRepository<RetailerDebitNoteSettlementMapping, Long> {
    @Query("Select r from RetailerDebitNote r inner join " +
            "RetailerDebitNoteSettlementMapping rsm on r.id=rsm.retailerDebitNoteId.id " +
            "inner join Settlement s on rsm.settlementId.id = s.id " +
            " where s.id= :settlementId")
    fun getSettledRetailerDn(@Param("settlementId")settlementId :Long):MutableList<RetailerDebitNote>

    @Query("Select r from RetailerDebitNoteSettlementMapping r where r.retailerDebitNoteId.id = :retailerDebitNoteId " +
            "and r.settlementId.id = :settlementId")
    fun findByRetailerDNIdAndSettlementId(@Param("retailerDebitNoteId")retailerDebitNoteId :Long,
                                          @Param("settlementId")settlementId :Long):RetailerDebitNoteSettlementMapping

    @Query("Select r from RetailerDebitNoteSettlementMapping r where r.settlementId.id = :settlementId")
    fun findBySettlementId(@Param("settlementId")settlementId :Long): List<RetailerDebitNoteSettlementMapping>
}