package com.pharmeasy.repo

import com.pharmeasy.data.BkInvoice
import com.pharmeasy.model.*
import com.pharmeasy.type.InvoiceType
import com.pharmeasy.type.PartnerType
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate
import java.time.LocalDateTime

interface BkInvoiceRepo: JpaRepository<BkInvoice, Long>, JpaSpecificationExecutor<BkInvoice> {
    @Query(" SELECT bki from BkInvoice bki WHERE bki.id =:id")
    fun get(@Param("id") id:Long): BkInvoice?

    @Query(" SELECT bki from BkInvoice bki WHERE bki.invoiceNum =:invoiceNum and bki.tenant in :tenant")
    fun getByInvoiceNum(@Param("invoiceNum") invoiceNum :String,
                        @Param("tenant") tenant: MutableList<String?>): List<BkInvoice?>

    @Query("SELECT bki FROM BkInvoice bki WHERE bki.id IN :ids")
    fun findByIds(@Param("ids") ids: List<Long>): List<BkInvoice>

    @Query(" SELECT bki FROM BkInvoice bki WHERE bki.invoiceId = :invoice_id and bki.invoiceNum = :invoiceNum and bki.tenant in :tenant")
    fun getByInvoiceId(@Param("invoice_id") invoice_id:String,
                       @Param("invoiceNum") invoiceNum:String,
                       @Param("tenant") tenant: MutableList<String?>): BkInvoice?

    @Query(" SELECT bki FROM BkInvoice bki WHERE bki.invoiceId = :invoiceId ")
    fun getByInvoiceIdV2(@Param("invoiceId") invoiceId:String): BkInvoice?

    @Query(" SELECT DISTINCT bki FROM BkInvoice bki " +
            "WHERE (:invoiceId = null OR bki.invoiceId = :invoiceId) " +
            "AND (:supplierId = null OR bki.partnerId = :supplierId) " +
            "AND (:invoiceNum = null OR bki.invoiceNum = :invoiceNum) " +
            "AND (:status = null OR bki.status = :status) AND bki.tenant in :tenants " +
            "AND ((:fromDate = null AND :toDate = null) OR (bki.createdOn >= :fromDate AND bki.createdOn <= :toDate)) " +
            "AND bki.status !='DELETED' and (bki.purchaseType ='PROCUREMENT' OR bki.purchaseType ='JIT') ORDER BY bki.dueDate ASC")
    fun getInvoices(@Param("invoiceId") invoiceId: Long?,
                    @Param("invoiceNum") invoiceNum: String?,
                    @Param("supplierId") supplierId: Long?,
                    @Param("status") status: InvoiceStatus?,
                    @Param("fromDate") fromDate: LocalDateTime?,
                    @Param("toDate") toDate: LocalDateTime?,
                    @Param("tenants") tenants: MutableList<String?>,
                    pageable: Pageable): Page<BkInvoice>

    @Query(" SELECT new com.pharmeasy.model.BkInvoiceIdNum(bki.id, bki.invoiceNum) from BkInvoice bki" +
            " WHERE bki.supplierId = :supplierId and (:InvoiceNum = null OR bki.invoiceNum like %:InvoiceNum%) " +
            "AND (:status = null OR bki.status != :status ) and bki.tenant in :tenants and bki.type = :custType ORDER BY bki.updatedOn DESC ")
    fun getBkInvoicesForSupplier(@Param("supplierId") supplierId: Long?,
                                 @Param("InvoiceNum") InvoiceNum: String?,
                                 @Param("status") status: InvoiceStatus?,
                                 @Param("custType") custType: PartnerType,
                                 @Param("tenants") tenants: MutableList<String?>): List<BkInvoiceIdNum>

    @Query(" SELECT bki from BkInvoice bki WHERE bki.id in :id")
    fun getAll(@Param("id") id:List<Long>): MutableList<BkInvoice>

    @Query(" SELECT SUM(bki.amount) FROM BkInvoice bki " +
            "WHERE bki.status = :status " +
            "AND (:supplierId = null OR bki.partnerId = :supplierId) AND (bki.tenant in :tenant) and bki.type = :custType")
    fun getPendingInvoiceAmount(@Param("supplierId") supplierId: Long?,
                                @Param("custType") custType: PartnerType,
                                @Param("tenant") tenant: MutableList<String?>,
                                @Param("status") status: InvoiceStatus = InvoiceStatus.PENDING): Double?

    @Query(value = "select binv.id, binv.invoice_id, binv.amount, bi.id bk_item_id, bi.item_id, bi.ucode, SUM(bi.amount) ucodeamount, SUM(bi.quantity) quantity, AVG(bi.epr) epr, AVG(bi.true_epr) true_epr, bi.batch from bk_invoice as binv join bk_item as bi on bi.invoice_id = binv.id where binv.invoice_id=:invoiceId and bi.ucode=:ucode group by id", nativeQuery = true)
    abstract fun getEPRDetails(
            @Param("ucode") ucode: String?,
            @Param("invoiceId") invoiceId: Int?
    ): List<AccountItemDTO>

    @Query(" SELECT b.* FROM bk_invoice b WHERE b.settlement_id = :id", nativeQuery = true)
    fun getBySettlementId(@Param("id") id: Long): List<BkInvoice>

    @Query(" SELECT new com.pharmeasy.model.VendorInvoiceDto (b.partnerId, '', COUNT(*), " +
            "SUM(case when b.status = 'PENDING' or b.status = 'PARTIAL_PAID' Then 1 else 0 END), " +
            "SUM(case when b.status = 'PENDING' or b.status = 'PARTIAL_PAID' Then (b.amount - b.paidAmount) else 0 END), " +
            "SUM(case when b.status in ('PAID', 'WRITE_OFF') then 1 else 0 END), " +
            "SUM(case when b.status not in  ('PAID', 'WRITE_OFF') AND b.dueDate < current_date() then 1 else 0 END ), " +
            "SUM(case when b.status not in  ('PAID', 'WRITE_OFF') AND b.dueDate < current_date() Then (b.amount - b.paidAmount) else 0 END), b.client, b.partnerDetailId) " +
            "FROM BkInvoice b WHERE b.createdOn > :from  AND b.createdOn < :to " +
            "AND (:partnerDetailId = null OR b.partnerDetailId = :partnerDetailId) " +
            "AND (b.tenant in :tenants) and b.type = :custType " +
            "AND (b.client in (:client)) " +
            "AND b.status in :statuses GROUP BY b.partnerId" )
    fun getVendorInvoiceData(@Param("from") from: LocalDateTime,
                             @Param("to") to: LocalDateTime,
                             @Param("custType") custType: PartnerType,
                             @Param("partnerDetailId") partnerDetailId: Long?,
                             @Param("tenants") tenants: MutableList<String?>,
                             @Param("statuses") statuses: MutableList<InvoiceStatus>,
                             @Param("client") client: List<InvoiceType>?,
                             pageable: Pageable): Page<VendorInvoiceDto>

    @Query(" SELECT new com.pharmeasy.model.VendorClientDto (b.partnerId, b.client) " +
            "FROM BkInvoice b WHERE b.createdOn > :from  AND b.createdOn < :to " +
            "AND (b.tenant in :tenants) and b.type = :custType AND (b.partnerId in :partnerId)  GROUP BY b.partnerId , b.client" )
    fun getVendorClient(@Param("from") from: LocalDateTime,
                             @Param("to") to: LocalDateTime,
                             @Param("custType") custType: PartnerType,
                             @Param("tenants") tenants: MutableList<String?>,
                             @Param("partnerId") partnerId: MutableList<Long?>): List<VendorClientDto>

    @Query(" SELECT new com.pharmeasy.model.VendorInvoiceDto (b.partnerId, '', COUNT(*), " +
            "SUM(case when b.status = 'PENDING' or b.status = 'PARTIAL_PAID' Then 1 else 0 END), " +
            "SUM(case when b.status = 'PENDING' or b.status = 'PARTIAL_PAID' Then (b.amount - b.paidAmount) else 0 END), " +
            "SUM(case when b.status in ('PAID', 'WRITE_OFF') then 1 else 0 END), " +
            "SUM(case when b.status not in ('PAID', 'WRITE_OFF') AND b.dueDate < current_date() then 1 else 0 END ), " +
            "SUM(case when b.status not in ('PAID', 'WRITE_OFF') AND b.dueDate < current_date() Then (b.amount - b.paidAmount) else 0 END), b.client, b.partnerDetailId) " +
            "FROM BkInvoice b WHERE b.createdOn > :from  AND b.createdOn < :to " +
            "AND (:partnerDetailId = null OR b.partnerDetailId = :partnerDetailId) " +
            "AND (b.partnerId IN :partnerIds) AND (b.tenant IN :tenants) and b.type = :custType " +
            "AND (b.client in (:client)) " +
            "AND b.status in :statuses GROUP BY b.partnerId" )
    fun getVendorInvoiceDataWithVendorIds(@Param("partnerIds") partnerIds: List<Long?>,
                                          @Param("from") from: LocalDateTime,
                                          @Param("to") to: LocalDateTime,
                                          @Param("custType") custType: PartnerType,
                                          @Param("partnerDetailId") partnerDetailId: Long?,
                                          @Param("tenants") tenants: MutableList<String?>,
                                          @Param("statuses") statuses: MutableList<InvoiceStatus>,
                                          @Param("client") client: List<InvoiceType>?,
                                          pageable: Pageable): Page<VendorInvoiceDto>


    @Query(" SELECT new com.pharmeasy.model.AggregatedInvoiceDataDto(SUM(case when b.status = 'PENDING' or b.status = 'PARTIAL_PAID' Then 1 else 0 END), " +
            "SUM(case when b.status = 'PENDING' or b.status = 'PARTIAL_PAID' Then (b.amount - b.paidAmount) else 0 END), " +
            "SUM(case when b.status not in ('PAID', 'WRITE_OFF') AND b.dueDate < current_date() then 1 else 0 END ),  " +
            "SUM(case when b.status not in ('PAID', 'WRITE_OFF') AND b.dueDate < current_date() Then (b.amount - b.paidAmount) else 0 END )) " +
            "FROM BkInvoice b WHERE b.createdOn > :from  AND b.createdOn < :to "+
            "AND (:partnerDetailId = null OR b.partnerDetailId = :partnerDetailId) " +
            "AND (b.tenant in :tenants) and b.type = :custType ")
    fun getAggregatedInvoiceData(@Param("from") from: LocalDateTime,
                                 @Param("to") to: LocalDateTime,
                                 @Param("custType") custType: PartnerType,
                                 @Param("partnerDetailId") partnerDetailId: Long?,
                                 @Param("tenants") tenants: MutableList<String?>): AggregatedInvoiceDataDto

    @Query(" SELECT new com.pharmeasy.model.AggregatedInvoiceDataDto(SUM(case when b.status = 'PENDING' or b.status = 'PARTIAL_PAID' Then 1 else 0 END), " +
            "SUM(case when b.status = 'PENDING' or b.status = 'PARTIAL_PAID' Then (b.amount - b.paidAmount) else 0 END), " +
            "SUM(case when b.status not in ('PAID', 'WRITE_OFF') AND b.dueDate < current_date() then 1 else 0 END ),  " +
            "SUM(case when b.status not in ('PAID', 'WRITE_OFF') AND b.dueDate < current_date() Then (b.amount - b.paidAmount) else 0 END )) " +
            "FROM BkInvoice b WHERE b.createdOn > :from  AND b.createdOn < :to " +
            "AND (:partnerDetailId = null OR b.partnerDetailId = :partnerDetailId) " +
            "AND (b.partnerId IN :partnerIds) AND (b.tenant in :tenants) and b.type = :custType")
    fun getAggregatedInvoiceDataWithPartnerIds(@Param("partnerIds") partnerIds: List<Long?>,
                                               @Param("partnerDetailId") partnerDetailId: Long?,
                                               @Param("from") from: LocalDateTime,
                                               @Param("to") to: LocalDateTime,
                                               @Param("custType") custType: PartnerType,
                                               @Param("tenants") tenants: MutableList<String?>): AggregatedInvoiceDataDto


    @Query(" SELECT new com.pharmeasy.model.SupplierListDTO(bki.partnerId, bki.supplierName, bki.client) from BkInvoice bki where bki.tenant in :tenant and bki.type =:type and bki.client not IN ('','HIDDEN') group by bki.partnerId")
    fun getAllSupplier(@Param("tenant") tenant: MutableList<String?>,@Param("type") type: PartnerType): List<SupplierListDTO?>

    @Query(" SELECT bki.partnerDetailId from BkInvoice bki where bki.tenant in :tenant and bki.type =:type and bki.client not IN ('','HIDDEN') group by bki.partnerDetailId")
    fun getAllPartnerDetailId(@Param("tenant") tenant: MutableList<String?>,@Param("type") type: PartnerType): List<Long?>

    @Query(" SELECT partner_detail_id FROM bk_invoice WHERE partner_id = :id LIMIT 1", nativeQuery = true)
    fun getPartnerDetailId(@Param("id") id: Long): Long?

    // for invoice url
    @Query(" SELECT new com.pharmeasy.model.VendorInvoiceDto (b.partnerId, '', COUNT(*), " +
            "SUM(case when b.status = 'PENDING' or b.status = 'PARTIAL_PAID' Then 1 else 0 END), " +
            "SUM(case when b.status = 'PENDING' or b.status = 'PARTIAL_PAID' Then (b.amount - b.paidAmount) else 0 END), " +
            "SUM(case when b.status in ('PAID', 'WRITE_OFF') then 1 else 0 END), " +
            "SUM(case when b.status not in ('PAID', 'WRITE_OFF') AND b.dueDate < current_date() then 1 else 0 END ), " +
            "SUM(case when b.status not in ('PAID', 'WRITE_OFF') AND b.dueDate < current_date() Then (b.amount - b.paidAmount) else 0 END), b.client) " +
            "FROM BkInvoice b WHERE (b.tenant in :tenants) and b.type =:custType GROUP BY b.partnerId" )
    fun getVendorInvoiceDataForUrl(@Param("custType") custType: PartnerType,
                                   @Param("tenants") tenants: MutableList<String?>): MutableList<VendorInvoiceDto?>?

    // for invoice url with date filter
    @Query(" SELECT new com.pharmeasy.model.VendorInvoiceDto (b.partnerId, '', COUNT(*), " +
            "SUM(case when b.status = 'PENDING' or b.status = 'PARTIAL_PAID' Then 1 else 0 END), " +
            "SUM(case when b.status = 'PENDING' or b.status = 'PARTIAL_PAID' Then (b.amount - b.paidAmount) else 0 END), " +
            "SUM(case when b.status in ('PAID', 'WRITE_OFF') then 1 else 0 END), " +
            "SUM(case when b.status not in ('PAID', 'WRITE_OFF') AND b.dueDate < current_date() then 1 else 0 END ), " +
            "SUM(case when b.status not in ('PAID', 'WRITE_OFF') AND b.dueDate < current_date() Then (b.amount - b.paidAmount) else 0 END), b.client) " +
            "FROM BkInvoice b WHERE b.createdOn > :fromDateTime  AND b.createdOn < :toDateTime and (b.tenant in :tenants) and b.type =:custType GROUP BY b.partnerId" )
    fun getVendorInvoiceDataWithDateForUrl(@Param("custType") custType: PartnerType,
                                           @Param("tenants") tenants: MutableList<String?>,
                                           @Param("fromDateTime") fromDateTime: LocalDateTime,
                                           @Param("toDateTime") toDateTime: LocalDateTime
                                           ): MutableList<VendorInvoiceDto?>?


    @Query("SELECT DISTINCT b FROM BkInvoice b WHERE b.partnerId = :partnerId AND (b.status = 'PENDING' or b.status = 'PARTIAL_PAID') AND b.type = :type " +
            "ORDER BY b.createdOn ASC")
    fun getOldestInvoicesCreatedOn(@Param("partnerId") partnerId: Long,@Param("type") type: PartnerType, pageable: Pageable): Page<BkInvoice?>


    @Query(" SELECT new com.pharmeasy.model.InvoiceIdCreatedDateDTO(bki.id, bki.createdOn) from BkInvoice bki WHERE bki.partnerId = :partnerId and (bki.status = 'PENDING' or bki.status = 'PARTIAL_PAID') and bki.type = :type ")
    fun getBkInvoicesForPartnerId(@Param("partnerId") supplierId: Long?,@Param("type") type: PartnerType): List<InvoiceIdCreatedDateDTO>

    @Modifying
    @Transactional
    @Query(" UPDATE bk_invoice SET `due_date` = DATE_ADD(`created_on` , INTERVAL :dueDays DAY)" +
            "    WHERE `id` in (:ids);",nativeQuery = true)
    fun updateOverdueOfInvoicesForPartner(@Param("dueDays") dueDays: Long, @Param("ids") ids: List<Long>)


    @Query(" SELECT new com.pharmeasy.model.VendorInvoiceDto (b.partnerId, '', COUNT(*), " +
            "SUM(case when b.status = 'PENDING' or b.status = 'PARTIAL_PAID' Then 1 else 0 END), " +
            "SUM(case when b.status = 'PENDING' or b.status = 'PARTIAL_PAID' Then (b.amount - b.paidAmount) else 0 END), " +
            "SUM(case when b.status in ('PAID', 'WRITE_OFF') then 1 else 0 END), " +
            "SUM(case when b.status not in ('PAID', 'WRITE_OFF') AND b.dueDate < current_date() then 1 else 0 END ), " +
            "SUM(case when b.status not in ('PAID', 'WRITE_OFF') AND b.dueDate < current_date() Then (b.amount - b.paidAmount) else 0 END), b.client) " +
            "FROM BkInvoice b WHERE (b.tenant in :tenants) GROUP BY b.partnerId" )
    fun getVendorInvoiceDataForUrl(@Param("tenants") tenants: MutableList<String?>): MutableList<VendorInvoiceDto?>?

    @Query(" SELECT COUNT(b.id) FROM BkInvoice b WHERE b.tenant in :tenant and b.type =:custType and b.purchaseType in :purchaseType")
    fun getOverallInvoiceCount(@Param("tenant") tenant: MutableList<String?>,@Param("custType") custType: PartnerType,@Param("purchaseType") purchaseType: MutableList<String?>):Long

    @Query(" SELECT COUNT(b.id) FROM BkInvoice b WHERE b.tenant in :tenant and b.type =:custType and b.status in :status and b.purchaseType in :purchaseType ")
    fun getOverallPaidInvoiceCount(@Param("tenant") tenant: MutableList<String?>,@Param("custType") custType: PartnerType,@Param("status") status: MutableList<InvoiceStatus>,@Param("purchaseType") purchaseType: MutableList<String?>):Long


    @Query(" SELECT COUNT(b.id) FROM BkInvoice b WHERE b.createdOn >= '2020-03-01 00:00:00' and b.tenant in :tenant and b.type =:custType ")
    fun getInvoiceCount(@Param("tenant") tenant: MutableList<String?>,@Param("custType") custType: PartnerType):Long

    @Query(" SELECT COUNT(b.id) FROM BkInvoice b WHERE b.createdOn >= '2020-03-01 00:00:00' and b.tenant in :tenant and b.type =:custType and b.status in :status ")
    fun getPaidInvoiceCount(@Param("tenant") tenant: MutableList<String?>,@Param("custType") custType: PartnerType,@Param("status") status: MutableList<InvoiceStatus>):Long

    @Query(" SELECT bki FROM BkInvoice bki WHERE bki.invoiceId = :invoice_id and bki.tenant = :tenant")
    fun checkDuplicateInvoiceId(@Param("invoice_id") invoice_id:String,
                       @Param("tenant") tenant: String): BkInvoice?


    @Query(" SELECT SUM(case when b.status = 'PENDING' or b.status = 'PARTIAL_PAID' Then (b.amount - b.paidAmount) else 0 END) " +
                "FROM BkInvoice b WHERE b.dueDate < current_date() AND (b.tenant in :tenants) and b.type = :custType ")
    fun getAllInvoiceOverDueAmount(@Param("custType") custType: PartnerType,
                                 @Param("tenants") tenants: MutableList<String?>): Double?


    @Query(" SELECT SUM(case when (b.status = 'PENDING' or b.status = 'PARTIAL_PAID') AND b.dueDate > current_date() Then (b.amount - b.paidAmount) else 0 END ) " +
            "FROM BkInvoice b WHERE b.dueDate >=  :from  AND b.dueDate <= :to AND (b.tenant in :tenants) and b.type = :custType ")
    fun getAggregatedInvoiceDueDateAmt(@Param("from") from: LocalDate,
                                 @Param("to") to: LocalDate,
                                 @Param("custType") custType: PartnerType,
                                 @Param("tenants") tenants: MutableList<String?>): Double?



    @Query(" SELECT bki FROM BkInvoice bki WHERE bki.invoiceId = :invoice_id and bki.tenant = :tenant")
    fun getByInvoiceIdForDS(@Param("invoice_id") invoice_id:String,
                       @Param("tenant") tenant: String): BkInvoice?


    @Query(" SELECT bki FROM BkInvoice bki WHERE bki.invoiceId = :invoice_id and bki.type = 'CUSTOMER' and bki.tenant = :tenant")
    fun getDSCustomerInvoice(@Param("invoice_id") invoice_id:String,
                             @Param("tenant") tenant: String): BkInvoice?

    @Query(" SELECT bki FROM BkInvoice bki WHERE bki.invoiceId = :invoiceId and bki.type = 'CUSTOMER' and bki.client = 'RIO' and bki.status in ('PENDING','PARTIAL_PAID') and bki.tenant = :tenant")
    fun getOpenRioInvoice(@Param("invoiceId") invoiceId:String,
                          @Param("tenant") tenant: String): BkInvoice?

    @Query("SELECT DISTINCT b FROM BkInvoice b WHERE b.type = :type and b.client in (:client) and b.partnerDetailId = :partnerDetailId AND b.status in('PENDING','PARTIAL_PAID') and tenant= :tenant ORDER BY b.createdOn ASC")
    fun getOldestInvoicesCreatedOn(@Param("partnerDetailId") partnerDetailId: Long, @Param("type") type:PartnerType, @Param("client") client: List<InvoiceType>, @Param("tenant") tenant: String, pageable: Pageable): Page<BkInvoice?>


    @Query(" SELECT bki FROM BkInvoice bki WHERE bki.invoiceNum = :invoiceNum and bki.type = 'CUSTOMER' and bki.client in ('RIO','EASY_SOL')")
    fun findCustomerInvoiceByInvoiceNum(@Param("invoiceNum") invoiceNum: String): BkInvoice?

    @Query(" SELECT bki FROM BkInvoice bki WHERE bki.invoiceNum = :invoiceNum and bki.type = 'CUSTOMER' and bki.client in ('RIO','EASY_SOL') and tenant = :tenant")
    fun findCustomerInvoiceByInvoiceNumAndTenant(@Param("invoiceNum") invoiceNum: String, @Param("tenant") tenant: String): BkInvoice?

    @Query(" SELECT bki FROM BkInvoice bki WHERE bki.invoiceId = :invoiceId and bki.type = 'CUSTOMER' and bki.client in ('RIO','EASY_SOL') and bki.tenant = :tenant")
    fun getRioInvoice(@Param("invoiceId") invoiceId:String, @Param("tenant") tenant: String): BkInvoice?

    @Query("select b from BkInvoice b where b.invoiceId = :invoiceId")
    fun getDataByInvoiceId(@Param("invoiceId") invoiceId: String): BkInvoice?

    @Query(" SELECT bki FROM BkInvoice bki WHERE bki.invoiceId = :invoiceId and bki.type = 'CUSTOMER' and bki.client = 'EASY_SOL' and bki.tenant = :tenant")
    fun getEasySolInvoice(@Param("invoiceId") invoiceId:String, @Param("tenant") tenant: String): BkInvoice?

    @Query(" SELECT bki FROM BkInvoice bki WHERE bki.invoiceId in :invoice_ids and (bki.status = 'PENDING' or bki.status = 'PARTIAL_PAID') and bki.type = 'CUSTOMER' and bki.client = 'EASY_SOL' and bki.tenant = :tenant")
    fun getEasySolInvoiceByInvoiceIds(@Param("invoice_ids") invoice_ids:List<String>,
                             @Param("tenant") tenant: String): List<BkInvoice?>

    @Query(" SELECT new com.pharmeasy.model.SettlementUpdateEventDto(bki.partnerDetailId, bki.client, bki.tenant, bki.partnerId, bki.amount, " +
            "bki.paidAmount, bki.status, bki.invoiceId, bki.invoiceNum, bki.createdOn, bki.dueDate) " +
            "FROM BkInvoice bki WHERE bki.id = :referenceId")
    fun getInvoiceSettlementData(@Param("referenceId") referenceId: Long): SettlementUpdateEventDto

    @Query(" SELECT bki FROM BkInvoice bki WHERE bki.invoiceId in :invoiceNo and bki.tenant = :tenant and bki.type = 'CUSTOMER' and bki.client = 'EASY_SOL' and bki.partnerDetailId = :partnerDetailId and (bki.status = 'PENDING' or bki.status = 'PARTIAL_PAID') ORDER BY bki.createdOn ASC")
    fun getEasySolInvoiceForSettlement(@Param("invoiceNo") invoiceNos: List<String>,@Param("partnerDetailId") partnerDetailId: Long, @Param("tenant")tenant: String): List<BkInvoice?>

    @Query("SELECT new com.pharmeasy.model.InvoiceMinDueDateDTO(" +
            "case when b.status in('PENDING','PARTIAL_PAID') then min(b.createdOn) else current_timestamp end, " +
            "case when b.status in('PENDING','PARTIAL_PAID') then min(b.dueDate) else current_timestamp end) " +
            "FROM BkInvoice b WHERE b.type = :type and b.client in (:client) " +
            "and b.partnerDetailId = :partnerDetailId " +
            "AND b.status in('PENDING','PARTIAL_PAID') and tenant= :tenant")
    fun findMinDatesByTypeAndClientAndPartnerDetailIdAndTenant(@Param("partnerDetailId") partnerDetailId: Long, @Param("type") type:PartnerType, @Param("client") client: List<InvoiceType>, @Param("tenant") tenant: String, pageable: Pageable): Page<InvoiceMinDueDateDTO?>

}
