package com.pharmeasy.repo.read

import com.pharmeasy.annotation.ReadOnlyRepository
import com.pharmeasy.data.BkInvoice
import com.pharmeasy.model.*
import com.pharmeasy.model.InvoiceAmtDto
import com.pharmeasy.model.SaleAmountDto
import com.pharmeasy.model.VendorInvoiceDto
import com.pharmeasy.model.rioDraftInvoice.RioBkInvoiceData
import com.pharmeasy.model.rioDraftInvoice.PendingInvoiceData
import com.pharmeasy.model.rioInvoice.RioInvoiceDataDTO
import com.pharmeasy.type.InvoiceType
import com.pharmeasy.type.PartnerType
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import java.time.LocalDateTime

@ReadOnlyRepository
interface BkInvoiceReadRepo: JpaRepository<BkInvoice, Long> {

    @Query("select bki from BkInvoice bki where bki.invoiceNum in :invoiceList and bki.tenant in :tenantList and bki.type = :type")
    fun getInvoicesInList(@Param("invoiceList") invoiceList: List<String?>,
                          @Param("tenantList") tenantList: List<String>,
                          @Param("type") type: PartnerType
    ): List<BkInvoice>

    @Query("select new com.pharmeasy.model.SaleAmountDto(coalesce(sum(bki.taxableAmount), 0), bi.partnerId, bi.client) from BkInvoice bi" +
            " join BkItem bki on bki.bkInvoice.id = bi.id where bi.tenant in :tenantList and bi.type = 'CUSTOMER' and" +
            " bi.createdOn between :fromDateTime and :toDateTime group by bi.partnerId, bi.client having bi.partnerId in :partnerIdList")
    fun getTotalSaleAmountByCompany(@Param("tenantList") tenantList: MutableList<String?>,
                                    @Param("fromDateTime") fromDateTime: LocalDateTime,
                                    @Param("toDateTime") toDateTime: LocalDateTime,
                                    @Param("partnerIdList") partnerIdList: Set<Long>): List<SaleAmountDto>


    @Query("select count(id) from BkInvoice where status in ('PENDING', 'PARTIAL_PAID') and partnerDetailId=(:partnerId) and type=('CUSTOMER') and client in('RIO','EASY_SOL') and tenant = (:tenant) and createdOn > '2020-12-31'")
    fun getPendingInvoiceCount(@Param("partnerId") partnerId: Long,@Param("tenant") tenant: String): String

    @Query("select COALESCE(sum(amount-paidAmount),0) from BkInvoice where status in ('PENDING', 'PARTIAL_PAID') and partnerDetailId=(:partnerId) and tenant = (:tenant)  and client in('RIO','EASY_SOL') and createdOn > '2020-12-31'")
    fun getPendingInvoiceAmount(@Param("partnerId") partnerId: Long,@Param("tenant") tenant: String): Double


    @Query("SELECT new com.pharmeasy.model.rioInvoice.RioInvoiceDataDTO('SB', bk.invoiceNum , bk.invoiceId, bk.id , DATE_FORMAT(bk.createdOn, '%Y-%m-%d'), DATE_FORMAT(bk.dueDate, '%Y-%m-%d'), COALESCE(bk.amount,0), case when COALESCE((bk.amount-bk.paidAmount),0) >= 0 then COALESCE((bk.amount-bk.paidAmount),0) else 0 end, datediff(CURDATE(),bk.createdOn) as days) from BkInvoice bk where ((bk.status not in ('PAID','WRITE_OFF','DELETED')) or (bk.status in ('PAID','WRITE_OFF') and bk.updatedOn>=(:updatedOnLimit))) and bk.partnerDetailId=(:partnerId) and tenant=(:tenant) and client in('RIO','EASY_SOL')")
    fun getInvoiceDetails(@Param("partnerId") partnerId: Long,@Param("tenant") tenant: String,@Param("updatedOnLimit") updatedOnLimit:LocalDateTime): List<RioInvoiceDataDTO>

    @Query("select distinct bi.partnerDetailId from BkInvoice bi where bi.status in ('PAID', 'WRITE_OFF') and bi.amount > '0' and bi.tenant=(:tenant) and bi.type=('CUSTOMER') and bi.client='RIO' and bi.tenant like CONCAT(:tenantType,'%')")
    fun getCustomerCode(@Param("tenant") tenant: String, @Param("tenantType")tenantType:String?):MutableList<Long>

    @Query("select distinct bi.partnerDetailId from BkInvoice bi where bi.status not in ('PAID', 'WRITE_OFF') and bi.amount > '0' and bi.tenant=(:tenant) and bi.type=('CUSTOMER') and bi.client='RIO'")
    fun getCustomerCodeWithoutCompanyCode(@Param("tenant") tenant: String):MutableList<Long>

    @Query(" SELECT SUM(case when b.status = 'PENDING' or b.status = 'PARTIAL_PAID' Then (b.amount - b.paidAmount) else 0 END) " +
            "FROM BkInvoice b WHERE b.partnerDetailId =:pdi AND (b.tenant in :tenants) and b.type = :custType ORDER BY b.createdOn ASC ")
    fun getAllPartnerInvoiceOverDueAmount(@Param("custType") custType: PartnerType,
                                   @Param("tenants") tenants: MutableList<String?>,
                                   @Param("pdi") pdi: Long): Double?

    @Query("SELECT new com.pharmeasy.model.InvoiceAmtDto (b.partnerDetailId, b.invoiceNum, " +
            "(b.amount - b.paidAmount)) " +
            "FROM BkInvoice b WHERE b.partnerDetailId =:pdi AND b.status not in ('PAID', 'WRITE_OFF') AND (b.tenant in :tenants) and b.type = :custType " +
            "ORDER BY b.createdOn ASC")
    fun getVendorInvoiceData(@Param("custType") custType: PartnerType,
                             @Param("tenants") tenants: MutableList<String?>,
                             @Param("pdi") pdi: Long,
                             pageable: Pageable): Page<InvoiceAmtDto>
    @Query("select distinct bi.partnerDetailId from BkInvoice bi where bi.partnerDetailId not in (:disabledPdi) and ((bi.status not in ('PAID', 'WRITE_OFF')) or (bi.status in ('PAID', 'WRITE_OFF') and bi.updatedOn>=:updatedOnLimit)) and bi.amount > '0' and bi.tenant=(:tenant) and bi.type=('CUSTOMER') and bi.client in ('RIO','EASY_SOL') and bi.createdOn > '2020-12-31'")
    fun getCustomerCodeWithoutCompanyCode(@Param("tenant") tenant: String,@Param("updatedOnLimit") updatedOnLimit:LocalDateTime, @Param("disabledPdi") disabledPdi: List<Long>):MutableList<Long>

    @Query(" SELECT new com.pharmeasy.model.VendorInvoiceDto (b.partnerId, '', COUNT(*),SUM(case when b.status = 'PENDING' or b.status = 'PARTIAL_PAID' Then 1 else 0 END), SUM(case when b.status = 'PENDING' or b.status = 'PARTIAL_PAID' Then (b.amount - b.paidAmount) else 0 END),SUM(case when b.status in ('PAID', 'WRITE_OFF') then 1 else 0 END), SUM(case when b.status not in ('PAID', 'WRITE_OFF') AND b.dueDate < current_date() then 1 else 0 END ),SUM(case when b.status not in ('PAID', 'WRITE_OFF') AND b.dueDate < current_date() Then (b.amount - b.paidAmount) else 0 END), b.client)FROM BkInvoice b WHERE b.createdOn > :fromDateTime  AND b.createdOn < :toDateTime and (b.tenant in :tenants) and b.type =:custType and b.client in (:client) GROUP BY b.partnerId" )
    fun getVendorInvoiceDataWithDateandPartnerForUrl(@Param("custType") custType: PartnerType,@Param("tenants") tenants: MutableList<String?>,@Param("fromDateTime") fromDateTime: LocalDateTime,@Param("toDateTime") toDateTime: LocalDateTime,@Param("client") client: List<InvoiceType>): MutableList<VendorInvoiceDto?>?

    @Query(" SELECT new com.pharmeasy.model.VendorInvoiceDto (b.partnerId, '', COUNT(*),SUM(case when b.status = 'PENDING' or b.status = 'PARTIAL_PAID' Then 1 else 0 END), SUM(case when b.status = 'PENDING' or b.status = 'PARTIAL_PAID' Then (b.amount - b.paidAmount) else 0 END),SUM(case when b.status in ('PAID', 'WRITE_OFF') then 1 else 0 END), SUM(case when b.status not in ('PAID', 'WRITE_OFF') AND b.dueDate < current_date() then 1 else 0 END ),SUM(case when b.status not in ('PAID', 'WRITE_OFF') AND b.dueDate < current_date() Then (b.amount - b.paidAmount) else 0 END), b.client)FROM BkInvoice b WHERE (b.tenant in :tenants) and b.type =:custType  and b.client in (:client) GROUP BY b.partnerId" )
    fun getVendorInvoiceDataWithPartnerForUrl(@Param("custType") custType: PartnerType,@Param("tenants") tenants: MutableList<String?>,@Param("client") client: List<InvoiceType>): MutableList<VendorInvoiceDto?>?

   @Query("select bi.invoiceId from BkInvoice bi where  bi.tenant= :tenant and bi.status in  ('PENDING', 'PARTIAL_PAID') and bi.amount > '0' and bi.type='CUSTOMER' and bi.client in('RIO','EASY_SOL')")
    fun getOutstandingInvoiceIds(@Param("tenant")tenant: String,page: Pageable): Page<String?>

    @Query("select count(id) from BkInvoice bi where bi.partnerDetailId=(:partnerDetailId) and bi.tenant=(:tenant) and ((bi.status != 'PAID') or (bi.status='PAID' and bi.updatedOn>=:updatedOnLimit)) and bi.amount > '0' and bi.type='CUSTOMER' and bi.client='RIO'")
    fun getPartnerInvoiceCount(@Param("partnerDetailId")partnerDetailId: Long, @Param("tenant")tenant: String,@Param("updatedOnLimit") updatedOnLimit:LocalDateTime): Long

    @Query("select count(id) from BkInvoice bi where bi.partnerDetailId=(:partnerDetailId) and bi.tenant=(:tenant) and (bi.status not in ('PAID','WRITE_OFF')) and bi.amount > '0' and bi.type='CUSTOMER' and bi.client in ('RIO','EASY_SOL') and bi.createdOn > '2020-12-31'")
    fun getPartnerInvoiceCount(@Param("partnerDetailId")partnerDetailId: Long, @Param("tenant")tenant: String): Long


    @Query("select new com.pharmeasy.model.rioDraftInvoice.RioBkInvoiceData(b.id, b.tenant,b.distributorPdi,b.apiVersion) from BkInvoice b where b.invoiceId = :invoiceId and b.partnerDetailId = :partnerDetailId and b.invoiceNum = :invoiceNum and b.tenant=:tenant")
    fun getBkInvoiceData(@Param("invoiceId")invoiceId: String?, @Param("invoiceNum")invoiceNum: String?, @Param("tenant")tenant: String, @Param("partnerDetailId")partnerDetailId: Long?): RioBkInvoiceData?

    @Query("select COALESCE(sum(amount-paidAmount),0) from BkInvoice where id = :invoiceId")
    fun getPendingSettledInvoiceAmount(@Param("invoiceId") invoiceId: Long): Double

    @Query(" SELECT new com.pharmeasy.model.SupplierListDTO(bki.partnerId, bki.supplierName, bki.client) from BkInvoice bki where bki.tenant in :tenant and bki.type =:type and bki.client not IN ('','HIDDEN') group by bki.partnerId")
    fun getAllSupplier(@Param("tenant") tenant: MutableList<String?>,@Param("type") type: PartnerType): List<SupplierListDTO?>

    @Query(" SELECT bki.partnerDetailId from BkInvoice bki where bki.tenant in :tenant and bki.type =:type and bki.client not IN ('','HIDDEN') group by bki.partnerDetailId")
    fun getAllPartnerDetailId(@Param("tenant") tenant: MutableList<String?>,@Param("type") type: PartnerType): List<Long?>

    @Query("select id from BkInvoice where tenant=(:tenant) and client='RIO' and status in ('PENDING','PARTIAL_PAID') and isMigrated = false")
    fun getIdByTenant(@Param("tenant") tenant: String, pageable: Pageable): Page<Long>

    @Query("SELECT count(id) from BkInvoice where invoiceNum = :invoiceNum and type = 'CUSTOMER' and client in ('RIO','EASY_SOL') and tenant= :tenant")
    fun getCountByInvoiceNumber(@Param("invoiceNum") invoiceNum: String, @Param("tenant")tenant: String): Long?

    @Query("SELECT bki FROM BkInvoice bki WHERE bki.invoiceNum = :invoiceNum and bki.type = 'CUSTOMER' and bki.client = 'RIO' and bki.parentTenant is not null and bki.tenant= :tenant")
    fun findCustomerInvoiceByInvoiceNumAndParentTenant(@Param("invoiceNum") invoiceNum: String, @Param("tenant")tenant: String): BkInvoice?

    @Query("select bki from BkInvoice bki where bki.invoiceNum in :invoiceList and bki.tenant = :tenant ")
    fun getByInvoiceNumberList(@Param("invoiceList") invoiceList: List<String?>,
                          @Param("tenant") tenant:String): List<BkInvoice?>

    @Query("SELECT b from BkInvoice b where b.createdOn > :createdOnBuffer and b.status in ('PARTIAL_PAID','PENDING') and b.amount-b.paidAmount < 1 and b.client in ('RIO','VENDOR','EASY_SOL')")
    fun getLessThanOneRupeeOpenInvoices(@Param("createdOnBuffer")createdOnBuffer: LocalDateTime): List<BkInvoice>

    @Query("SELECT b from BkInvoice b where b.id in (:ids)")
    fun getByIds(@Param("ids")ids: MutableList<Long>): List<BkInvoice>

    @Query("SELECT new com.pharmeasy.model.rioDraftInvoice.PendingInvoiceData(b.id, b.invoiceNum, b.amount-b.paidAmount) FROM BkInvoice b LEFT JOIN DigitalReceiptsWriteback r ON b.id = r.bkInvoiceId " +
            "WHERE b.partnerDetailId = :pdi and b.tenant=:tenant and r.bkInvoiceId IS NULL and b.status in ('PENDING','PARTIAL_PAID')")
    fun getRioCollectionInvoicesByPDIAndTenant(@Param("pdi")pdi:Long, @Param("tenant")tenant: String, pageable: Pageable): Page<PendingInvoiceData>

    @Query("select b from BkInvoice b where b.partnerDetailId = :partnerDetailId and b.status in ('PAID','PARTIAL_PAID') and client in ('RIO','VENDOR','EASY_SOL') ORDER BY b.createdOn ASC")
    fun getRecentInvoiceSettledForPartner(@Param("partnerDetailId")partnerDetailId: Long, pageable: Pageable):Page<BkInvoice>
}