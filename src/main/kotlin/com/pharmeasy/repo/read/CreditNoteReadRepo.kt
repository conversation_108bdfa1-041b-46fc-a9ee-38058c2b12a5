package com.pharmeasy.repo.read

import com.pharmeasy.annotation.ReadOnlyRepository
import com.pharmeasy.data.CreditNote
import com.pharmeasy.model.rioInvoice.RioCreditDataDTO
import com.pharmeasy.type.PartnerType
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import java.time.LocalDateTime

@ReadOnlyRepository
interface CreditNoteReadRepo : JpaRepository<CreditNote, Long> {

    @Query("select count(id) from  CreditNote where status not in ('REALIZED','WRITE_OFF') and partnerDetailId=(:partnerId) and tenant = (:tenant) and client='RIO'")
    fun getPendingCreditCount(@Param("partnerId") partnerId: Long,@Param("tenant") tenant: String): String
    @Query("select new com.pharmeasy.model.rioInvoice.RioCreditDataDTO(case when c.tenant like 'ds%' then 'SB' else '1C' end, c.creditNoteNumber, '', c.id , DATE_FORMAT(c.createdOn, '%Y-%m-%d') , COALESCE(c.amount,0), datediff(CURDATE(),c.createdOn) as days) from CreditNote c where (status not in ('REALIZED','WRITE_OFF') or (status in ('REALIZED','WRITE_OFF') and updatedOn>=(:updatedOnLimit))) and c.partnerDetailId=(:partnerId) and tenant = (:tenant) and client='RIO'")
    fun getCreditDetails(@Param("partnerId") partnerId: Long,@Param("tenant") tenant: String,@Param("updatedOnLimit") updatedOnLimit: LocalDateTime):  List<RioCreditDataDTO>

    @Query("select id from CreditNote where (:id = null OR id > :id) and tenant=(:tenant) and client='RIO' and status in ('PENDING','PARTIAL_REALIZED') and isMigrated = false")
    fun getIdByTenant(@Param("tenant") tenant: String,@Param("id") id: Long?, pageable: Pageable): Page<Long>

    @Query(
        "SELECT distinct(cn.partnerDetailId) from CreditNote cn where cn.type = :partnerType " +
                "and cn.qrCode is NULL" +
                " and cn.irn is NULL and cn.createdOn >= :date and client != 'EASY_SOL'"
    )
    fun getUniquePartnerDetailIdForPendingEInvoices(
        @Param("partnerType") partnerType: PartnerType,
        @Param("date") date: LocalDateTime, pageable: Pageable
    ): List<Long>

    //change: add irn = nuLL and qr_code = null, and change function name
    @Query("select cn.creditNoteNumber from CreditNote cn where partnerDetailId = :partnerDetailId and " +
            "irn = NULL and qr_code = NULL and createdOn >= :date and client != 'EASY_SOL'")
    fun getIdsFromPartnerDetailIds(@Param("partnerDetailId") partnerDetailId : Long,
                                   @Param("date") date: LocalDateTime) : List<String>


}