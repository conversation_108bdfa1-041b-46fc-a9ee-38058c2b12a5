package com.pharmeasy.repo

import com.pharmeasy.data.Settlement
import com.pharmeasy.type.PartnerType
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import java.time.LocalDateTime

interface SettlementRepo : JpaRepository<Settlement, Long> {

    @Query(" SELECT DISTINCT srec FROM Settlement srec WHERE srec.type = :custType AND (:supplierId = null OR srec.partnerId = :supplierId) " +
            "AND (:settlementNumber = null OR srec.settlementNumber = :settlementNumber) AND (srec.tenant in (:tenant)) AND (:pdi = null OR srec.partnerDetailId = :pdi) " +
            "AND ((:fromDate = null AND :toDate = null) OR (srec.createdOn >= :fromDate AND srec.createdOn <= :toDate)) ORDER BY srec.createdOn DESC" )
    fun getSettlementRecords(@Param("supplierId") supplierId: Long?,
                             @Param("settlementNumber") settlementNumber: String?,
                             @Param("fromDate") fromDate: LocalDateTime?,
                             @Param("toDate") toDate: LocalDateTime?,
                             @Param("tenant") tenant: MutableList<String?>,
                             @Param("custType") custType: PartnerType,
                             @Param("pdi") pdi: Long?,
                             pageable: Pageable): Page<Settlement>

//    @Query("SELECT DISTINCT srec FROM Settlement srec JOIN srec.invoices inv " +
//            "WHERE inv.invoiceId = :invoiceId " +
//            "AND (:supplierId = null OR srec.partnerId = :supplierId) AND (:tenant = null OR srec.tenant in :tenant)" +
//            "AND (:settlementId = null OR srec.id = :settlementId) " +
//            "AND ((:fromDate = null AND :toDate = null) OR (srec.createdOn >= :fromDate AND srec.createdOn <= :toDate))")
//    fun getSettlementRecordsByInvoice(@Param("supplierId") supplierId: Long?,
//                                      @Param("settlementId") settlementId: Long?,
//                                      @Param("invoiceId") invoiceId: Long?,
//                                      @Param("fromDate") fromDate: LocalDateTime?,
//                                      @Param("toDate") toDate: LocalDateTime?,
//                                      @Param("tenant") tenant: MutableList<String?>,
//                                      pageable: Pageable): Page<Settlement>

    @Query("SELECT DISTINCT srec FROM Settlement srec JOIN CreditNote cn " +
            "WHERE cn.id = :creditNoteId AND srec.type = :custType AND cn.type = :custType " +
            "AND (:supplierId = null OR srec.partnerId = :supplierId) AND (:pdi = null OR srec.partnerDetailId = :pdi) AND (srec.tenant in (:tenant)) " +
            "AND (:settlementNumber = null OR srec.settlementNumber = :settlementNumber) " +
            "AND ((:fromDate = null AND :toDate = null) OR (srec.createdOn >= :fromDate AND srec.createdOn <= :toDate))")
    fun getSettlementRecordsByCreditNote(@Param("supplierId") supplierId: Long?,
                                         @Param("settlementNumber") settlementNumber: String?,
                                         @Param("creditNoteId") creditNoteId: Long?,
                                         @Param("fromDate") fromDate: LocalDateTime?,
                                         @Param("toDate") toDate: LocalDateTime?,
                                         @Param("tenant") tenant: MutableList<String?>,
                                         @Param("custType") custType: PartnerType,
                                         @Param("pdi") pdi: Long?,
                                         pageable: Pageable): Page<Settlement>

    @Query(" SELECT DISTINCT srec FROM Settlement srec " +
            "WHERE srec.partnerId = :supplier " +
            "AND srec.createdBy = :user " +
            "AND srec.amount = :amount AND srec.tenant = :tenant")
    fun findDuplicates(@Param("supplier") supplier: Long,
                       @Param("user") user: String?,
                       @Param("amount") amount: Double,
                       @Param("tenant") tenant: String): List<Settlement>?

    @Query(" SELECT SUM(s.paidAmount) FROM Settlement s " +
            "WHERE (:supplierId = null OR s.supplierId = :supplierId)")
    fun getSettledAmountForSupplier(@Param("supplierId") supplierId: Long?): Double?

    @Query(" SELECT sett FROM Settlement sett WHERE sett.settlementNumber = :settlementNumber")
    fun getSettlementBySettlementNumber(@Param("settlementNumber") settlementNumber: String): Settlement?

    @Query("SELECT cn.creditNoteNumber"+
            " FROM CreditNoteSettlement cns, CreditNote cn"+
            " WHERE cns.settlementId = :settlementId AND cns.creditNoteId = cn.id")
    fun getCreditNoteListBySettlementID(@Param("settlementId") settlementId: Long): MutableList<String>

    @Query("SELECT s FROM Settlement s " +
            "inner join ReceiptSettlement rs on s.id=rs.id.settlementId " +
            "inner join Receipt r on rs.id.receiptId = r.id where r.receiptNumber = :receiptNumber")
    fun getSettlementFromReceipt(@Param("receiptNumber")receiptNumber: String?, pageable: Pageable): Page<Settlement>

    @Query("SELECT s from Settlement s inner join ChequeHandle ch on s.id = ch.settlementId where ch.id = :chequeHandlingId")
    fun getSettlementByChequeHandlingId(@Param("chequeHandlingId")chequeHandlingId: Long): Settlement

    @Query("SELECT s from Settlement s where paymentReference=:paymentReference and  " +
            "partnerDetailId = :partnerDetailId and tenant = :tenant")
    fun getSettlementByReferenceNumberAndPartnerDetailId(@Param("paymentReference")paymentReference: String,
                                                         @Param("partnerDetailId")partnerDetailId: Long,
                                                         @Param("tenant")tenant: String): List<Settlement>

    @Query("SELECT s.id from Settlement s where paymentReference =:paymentReference ")
    fun getSettlementByReferenceNumber(@Param("paymentReference")paymentReference: String): List<Long>?

    @Query("SELECT s from Settlement s " +
            "inner join ReceiptSettlement rs on s.id = rs.id.settlementId " +
            "inner join Receipt r on rs.id.receiptId = r.id " +
            "where r.receiptNumber = :receiptNumber")
    fun getSettlementsByReceiptNumber(@Param("receiptNumber") receiptNumber: String): List<Settlement>

    @Query("SELECT s from Settlement s where uuid = :uuid")
    fun getSettlementByUUID(@Param("uuid") uuid: String): Settlement?
}