package com.pharmeasy.repo

import com.pharmeasy.data.RetailerDebitNote
import com.pharmeasy.model.*
import com.pharmeasy.type.*
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import java.time.LocalDate
import java.time.LocalDateTime

interface RetailerDebitNoteRepo : JpaRepository<RetailerDebitNote, Long> {

    @Query(
        " SELECT new com.pharmeasy.model.VendorDebitNoteDto(rdn.partnerId, rdn.partnerName, COUNT(*), " +
                "SUM(case when rdn.status = 'PENDING' or rdn.status = 'PARTIAL_PAID' Then 1 else 0 END), " +
                "SUM(case when rdn.status = 'PENDING' or rdn.status = 'PARTIAL_PAID' Then (rdn.amount - rdn.amountReceived) else 0 END), " +
                "SUM(case when rdn.status in ('PAID') then 1 else 0 END), " +
                "rdn.partnerDetailId) " +
                "FROM RetailerDebitNote rdn WHERE rdn.createdOn >= :from  AND rdn.createdOn <= :to  " +
                "AND (:partnerDetailIds = null OR rdn.partnerDetailId in :partnerDetailIds) " +
                "AND (rdn.tenant in :tenants) AND (:dnType = null OR rdn.type = :dnType) " +
                "AND (:status = null OR rdn.status = :status) AND status != 'PENDING_APPROVAL'" +
                "GROUP BY rdn.partnerDetailId"
    )
    fun getRetailerDebitNotesGroup(
        @Param("from") from: LocalDateTime,
        @Param("to") to: LocalDateTime,
        @Param("dnType") dnType: DnType?,
        @Param("partnerDetailIds") partnerDetailIds: List<Long>?,
        @Param("status") status: InvoiceStatus?,
        @Param("tenants") tenants: MutableList<String?>,
        pageable: Pageable
    ): Page<VendorDebitNoteDto>

    @Query(
        " SELECT rdn FROM RetailerDebitNote rdn WHERE (rdn.createdOn >= :from  AND rdn.createdOn <= :to)  " +
                "AND (:partnerDetailId = null OR rdn.partnerDetailId = :partnerDetailId) " +
                "AND (rdn.tenant in :tenants) AND (:type = null OR rdn.type = :type) " +
                "AND (:status = null OR rdn.status = :status) AND  " +
                "(:documentNumber = null OR rdn.documentNumber = :documentNumber)"
    )
    fun getRetailerDebitNotes(
        @Param("from") from: LocalDateTime,
        @Param("to") to: LocalDateTime,
        @Param("partnerDetailId") partnerDetailId: Long?,
        @Param("documentNumber") documentNumber: String?,
        @Param("type") type: DnType?,
        @Param("tenants") tenants: MutableList<String?>,
        @Param("status") status: InvoiceStatus?,
        pageable: Pageable
    ): Page<RetailerDebitNote>

    @Query("select rdn from RetailerDebitNote rdn where rdn.refId = :referenceId and partnerDetailId= :partnerDetailId")
    fun getDebitNoteByReferenceId(
        @Param("referenceId") referenceId: String?,
        @Param("partnerDetailId") partnerDetailId: Long?
    ): RetailerDebitNote?

    @Query("select count(id) from RetailerDebitNote where status in ('PENDING', 'PARTIAL_PAID') and partnerDetailId=(:partnerId) and tenant = (:tenant)")
    fun getPendingDebitNoteCount(@Param("partnerId") partnerId: Long, @Param("tenant") tenant: String): String

    @Query(
        "SELECT new com.pharmeasy.model.RetailerDNCreationSyncEventDTO('SB', dn.documentNumber , dn.id, " +
                " DATE_FORMAT(dn.createdOn, '%Y-%m-%d'), COALESCE(dn.amount,0), " +
                "case when COALESCE((dn.amount-dn.amountReceived),0) >= 0 then COALESCE((dn.amount-dn.amountReceived),0) else 0 end, " +
                "dn.type) from RetailerDebitNote dn where ((dn.status in ('PENDING', 'PARTIAL_PAID')) or " +
                "(dn.status in ('PAID') and dn.updatedOn>=(:updatedOnLimit))) and dn.partnerDetailId=(:partnerId) and " +
                "dn.tenant=(:tenant)"
    )
    fun getRetailerDNDetails(
        @Param("partnerId") partnerId: Long,
        @Param("tenant") tenant: String,
        @Param("updatedOnLimit") updatedOnLimit: LocalDateTime
    ): List<RetailerDNCreationSyncEventDTO>

    @Query("select dn from RetailerDebitNote dn where dn.documentNumber = :debitNoteNumber")
    fun getRetailerDebitNoteByDebitNoteNumber(@Param("debitNoteNumber") debitNoteNumber: String?): RetailerDebitNote?

    @Query(
        " SELECT new com.pharmeasy.model.SettlementUpdateEventDto(dn.partnerDetailId, dn.tenant, dn.partnerId, dn.amount, " +
                "dn.amountReceived, dn.status, cast(dn.id as string), dn.documentNumber, dn.createdOn) " +
                "FROM RetailerDebitNote dn WHERE dn.id = :referenceId"
    )
    fun getRetailerDebitNoteSettlementEventData(@Param("referenceId") referenceId: Long): SettlementUpdateEventDto

    @Query("SELECT DISTINCT b FROM RetailerDebitNote b WHERE b.partnerDetailId = :partnerDetailId AND b.status in('PENDING','PARTIAL_PAID') and tenant= :tenant ORDER BY b.createdOn ASC")
    fun getOldestRetailerDebitNoteCreatedOn(
        @Param("partnerDetailId") partnerDetailId: Long,
        @Param("tenant") tenant: String,
        pageable: Pageable
    ): Page<RetailerDebitNote?>

    @Query("select COALESCE(sum(amount-amountReceived),0) from RetailerDebitNote where status in ('PENDING', 'PARTIAL_PAID') and partnerDetailId=(:partnerId) and tenant = (:tenant)")
    fun getPendingDebitNoteAmount(@Param("partnerId") partnerId: Long, @Param("tenant") tenant: String): Double

    @Query(
        "select b from RetailerDebitNote b where b.type = :type and b.tenant in :tenant " +
                "and (:partnerDetailId is null or b.partnerDetailId = :partnerDetailId) " +
                "and ((:fromDate is null and :toDate is null) or (b.createdOn between :fromDate and :toDate)) " +
                "and (:useCase is null or b.useCase = :useCase) " +
                "and b.status in :status"
    )
    fun getAdhocRetailerDebitNote(
        @Param("type") type: DnType,
        @Param("tenant") tenant: MutableList<String?>,
        @Param("partnerDetailId") partnerDetailId: Long?,
        @Param("fromDate") fromDate: LocalDateTime?,
        @Param("toDate") toDate: LocalDateTime?,
        @Param("useCase") useCase: UseCaseType?,
        @Param("status") status: List<InvoiceStatus>,
        pageable: Pageable
    ): Page<RetailerDebitNote>

    @Query("select r from RetailerDebitNote r inner join RetailerDebitNoteSettlementMapping rm on r.id=rm.retailerDebitNoteId where rm.settlementId.id = :settlementId")
    fun getRetailerDnBySettlementId(@Param("settlementId") settlementId: Long): List<RetailerDebitNote>

    @Query("select r from RetailerDebitNote r where r.uuid= :uuid")
    fun getRetailerDebitNoteByUuid(@Param("uuid") uuid: String): RetailerDebitNote?

    @Query("select r from RetailerDebitNote r where r.createdOn > :createdOnBuffer and  r.amount-r.amountReceived < 1 and status in ('PENDING','PARTIAL_PAID')")
    fun getLessThanRupeeOpenDebitNotes(@Param("createdOnBuffer")createdOnBuffer: LocalDateTime): List<RetailerDebitNote>

}

