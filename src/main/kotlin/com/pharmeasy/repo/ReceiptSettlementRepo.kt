package com.pharmeasy.repo

import com.pharmeasy.data.Receipt
import com.pharmeasy.data.ReceiptSettlement
import com.pharmeasy.data.ReceiptSettlementId
import com.pharmeasy.data.Settlement
import com.pharmeasy.type.PaymentType
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import java.time.LocalDate

interface ReceiptSettlementRepo : JpaRepository<ReceiptSettlement, ReceiptSettlementId> {
    @Query("select r from ReceiptSettlement r inner join Settlement s on r.id.settlementId = s.id " +
            "inner join PaymentExcess p on s.paymentReference = p.utr" +
            " where p.creditNoteNumber=:creditNoteNumber")
    fun getReceiptSettlementsByCreditNote(@Param("creditNoteNumber")creditNoteNumber: String?): List<ReceiptSettlement>?

    @Query("select r from ReceiptSettlement r where r.id.settlementId = :settlementId")
    fun getReceiptBySettlementId(@Param("settlementId")settlementId: Long): ReceiptSettlement?

    @Query("select r from ReceiptSettlement r where r.id.receiptId = :receiptId and r.id.settlementId = :settlementId")
    fun getReceiptByReceiptIdAndSettlementId(@Param("receiptId")receiptId: Long, @Param("settlementId")settlementId: Long): ReceiptSettlement?

    @Query("select s from Settlement s inner join ReceiptSettlement r on r.id.settlementId = s.id" +
            " where r.id.receiptId = :receiptId")
    fun getSettlementByReceiptId(@Param("receiptId")receiptId: Long): List<Settlement>

}
