package com.pharmeasy.data

import org.hibernate.envers.Audited
import org.hibernate.envers.RelationTargetAuditMode
import org.springframework.data.annotation.CreatedBy
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedBy
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.format.annotation.DateTimeFormat
import java.time.LocalDateTime
import javax.persistence.*

@Deprecated("Use BaseEntity instead")
@Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
@MappedSuperclass
abstract class BaseAuditEntityDeprecated(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(name = "created_by_userid")
    @CreatedBy
    var createdByUserId: String? = null,

    @Column(name = "updated_by_userid")
    @LastModifiedBy
    var updatedByUserId: String? = null,

    @Column(name = "created_on")
    @CreatedDate
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    var createdOn: LocalDateTime = LocalDateTime.now(),

    @Column(name = "updated_on")
    @LastModifiedDate
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    var updatedOn: LocalDateTime = LocalDateTime.now()
)