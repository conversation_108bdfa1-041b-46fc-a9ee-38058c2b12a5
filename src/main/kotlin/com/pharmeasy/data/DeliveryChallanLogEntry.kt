package com.pharmeasy.data

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.pharmeasy.type.DeliveryChallanLogEntryStatusType
import org.springframework.format.annotation.DateTimeFormat
import java.time.LocalDate
import java.time.LocalDateTime
import javax.persistence.*

@JsonIgnoreProperties(ignoreUnknown = true)
@Entity
@Table(name = "delivery_challan_log_entry")
data class DeliveryChallanLogEntry(
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long?,

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @Column(name = "created_on")
    var createdOn: LocalDateTime = LocalDateTime.now(),

    @Column(name = "created_by")
    var createdBy: String?,

    @Column(name = "total_value")
    var amount: Double?,

    @Column(name = "reference_number")
    var referenceNumber: String?,

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @Column(name = "reference_date")
    var referenceDate: LocalDate? = LocalDate.now(),

    @Column(name = "reference_amount")
    var referenceAmount: Double?,

    @Column(name = "reference_number2")
    var referenceNumber2: String?,

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @Column(name = "reference_date2")
    var referenceDate2: LocalDate? = LocalDate.now(),

    @Column(name = "reference_amount2")
    var referenceAmount2: Double?,

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    var status: DeliveryChallanLogEntryStatusType? = DeliveryChallanLogEntryStatusType.CN_SETOFF,

    @Column(name = "remark")
    var remark: String? = null,

    @Column(name= "round_off_amount")
    var roundOffAmount: Double? = 0.0
)
