package com.pharmeasy.data

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.pharmeasy.model.InvoiceStatus
import com.pharmeasy.type.DnType
import com.pharmeasy.type.UseCaseType
import org.hibernate.annotations.Fetch
import org.hibernate.annotations.FetchMode
import javax.persistence.*

@JsonIgnoreProperties(ignoreUnknown = true)
@Entity
@Table(name = "retailer_debit_note",
    uniqueConstraints = [UniqueConstraint(columnNames = ["uuid"])])
data class RetailerDebitNote(

    @Column(name = "created_by")
    var createdBy: String,

    @Column(name = "updated_by")
    var updatedBy: String,

    @Column(name = "remarks")
    var remarks: String?,

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    var status: InvoiceStatus = InvoiceStatus.PENDING,

    @Enumerated(EnumType.STRING)
    @Column(name = "type")
    var type: DnType = DnType.ADHOC,

    @Column(name = "amount")
    var amount: Double = 0.0,

    @Column(name = "amount_received")
    var amountReceived: Double = 0.0,

    @Column(name = "taxable_value")
    var taxableValue: Double = 0.0,

    @Column(name = "tax_amount")
    var taxAmount: Double = 0.0,

    @Column(name = "interstate")
    var interstate: Boolean? = false,

    @Column(name = "document_number")
    var documentNumber: String,

    @Column(name = "partner_detail_id")
    var partnerDetailId: Long,

    @Column(name = "partner_id")
    var partnerId: Long,

    @Column(name = "partner_name")
    var partnerName: String,

    @Column(name = "qr_code")
    var qrCode: String? = null,

    @Column(name = "irn")
    var irn: String? = null,

    @Column(name = "tenant")
    var tenant: String,

    @Column(name = "company_id")
    var companyId: Long,

    @Enumerated(EnumType.STRING)
    @Column(name = "use_case")
    var useCase: UseCaseType? = UseCaseType.BOUNCE,

    @Column(name = "is_gst_applicable")
    var isGstApplicable: Boolean? = false,

    @OneToMany(mappedBy = "retailerDebitNote", orphanRemoval = false, fetch = FetchType.EAGER)
    var retailerDebitItems: MutableList<RetailerDebitNoteItems> = mutableListOf(),

    @Column(name = "reference_id")
    var refId: String? = "",

    @Column(name = "is_migrated")
    var isMigrated: Boolean? = false,

    @Column(name = "narration")
    var narration: String? = null,

    @Column(name = "assigned_to")
    var assignedTo: String? = null,

    @Column(name = "assigned_to_id")
    var assignedToId: String? = null,

    @Column(name = "uuid")
    var uuid: String? = null,

    @Version
    @Column(name = "version", columnDefinition = "integer DEFAULT 0", nullable = false)
    var version: Int = 0

) : BaseAuditEntityDeprecated()
