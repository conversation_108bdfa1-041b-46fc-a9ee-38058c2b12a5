package com.pharmeasy.data

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.pharmeasy.type.AdvancePaymentSource
import com.pharmeasy.type.PartnerType
import com.pharmeasy.type.PaymentType
import com.pharmeasy.util.UUIDUtil
import org.springframework.format.annotation.DateTimeFormat
import java.time.LocalDate
import java.time.LocalDateTime
import javax.persistence.*
import kotlin.jvm.Transient

@JsonIgnoreProperties(ignoreUnknown = true)
@Entity
@Table(name = "settlement",
        uniqueConstraints = [UniqueConstraint(columnNames = ["uuid"])])
data class Settlement(
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long,
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @Column(name = "created_on")
    var createdOn: LocalDateTime?,
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @Column(name = "updated_on")
    var updatedOn: LocalDateTime?,
    @Column(name = "created_by")
    var createdBy: String?,
    @Column(name = "supplier_id")
    var supplierId: Long,
    @Column(name = "supplier_name")
    var supplierName: String?,
    @Column(name = "amount")
    var amount: Double,
    @Column(name = "paid_amount")
    var paidAmount: Double,
    @Column(name = "remarks")
    var remarks: String?,
    @Column(name = "settlement_number")
    var settlementNumber: String?,
    @Transient
    var invoices: MutableList<BkInvoice> = mutableListOf(),
    @Transient
    var creditNotes: MutableList<CreditNote> = mutableListOf(),
    @Enumerated(EnumType.STRING)
    @Column(name = "payment_type")
    var paymentType: PaymentType,
    @Column(name = "payment_reference")
    var paymentReference: String?,
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    @Column(name = "payment_date")
    var paymentDate: LocalDate?,
    @Column(name = "partner_id")
    var partnerId: Long? = null,
    @Column(name = "partner_detail_id")
    var partnerDetailId: Long? = null,
    @Enumerated(EnumType.STRING)
    @Column(name = "type")
    var type: PartnerType = PartnerType.VENDOR,
    @Column(name = "tenant")
    var tenant: String,
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    @Column(name = "cheque_date")
    var chequeDate: LocalDate? = null,
    @Column(name = "bank_id")
    var bankId: Long? = null,
    @Column(name = "bank_name")
    var bankName: String? = null,
    @Column(name = "is_bounced")
    var isBounced: Boolean? = false,
    @Column(name = "is_reversed")
    var reversed: Boolean? = false,
    @Transient
    var advancePayment: MutableList<AdvancePayment> = mutableListOf(),
    @Transient
    var chargeInvoice: MutableList<Charges> = mutableListOf(),
    @Transient
    var charge: Boolean = false,
    @Transient
    var receipt: List<Receipt>? = mutableListOf(),
    @Transient
    var paymentSource: AdvancePaymentSource? = AdvancePaymentSource.SYSTEM,
    @Transient
    var retailerDebitNotes: MutableList<RetailerDebitNote> = mutableListOf(),
    @Column(name="uuid")
    var uuid: String,
    var settlementGroupNumber: String? = null
) {
    constructor(
        user: String?,
        supplierId: Long,
        supplierName: String?,
        amount: Double,
        paidAmount: Double,
        remarks: String,
        settlementNumber: String?,
        invoices: MutableList<BkInvoice>,
        creditNotes: MutableList<CreditNote>,
        paymentType: PaymentType,
        paymentReference: String,
        paymentDate: LocalDate,
        partnerId: Long?,
        partnerDetailId: Long?,
        type: PartnerType,
        tenant: String,
        chequeDate: LocalDate?,
        bankId: Long?,
        bankName: String?,
        advancePayment: MutableList<AdvancePayment>,
        chargeInvoice: MutableList<Charges>,
        paymentSource: AdvancePaymentSource?,
        debitNotes: MutableList<RetailerDebitNote> = mutableListOf<RetailerDebitNote>()
    ) : this(
        id = 0,
        createdOn = LocalDateTime.now(),
        updatedOn = LocalDateTime.now(),
        createdBy = user,
        supplierId = supplierId,
        supplierName = supplierName,
        amount = amount,
        paidAmount = paidAmount,
        remarks = remarks,
        settlementNumber = settlementNumber,
        invoices = invoices,
        creditNotes = creditNotes,
        paymentType = paymentType,
        paymentReference = paymentReference,
        paymentDate = paymentDate,
        partnerId = partnerId,
        partnerDetailId = partnerDetailId,
        type = type,
        tenant = tenant,
        chequeDate = chequeDate,
        bankId = bankId,
        bankName = bankName,
        isBounced = false,
        reversed = false,
        advancePayment = advancePayment,
        chargeInvoice = chargeInvoice,
        charge = false,
        receipt = null,
        paymentSource = paymentSource,
        retailerDebitNotes = debitNotes,
        uuid = UUIDUtil.generateUuid()
    )
}
