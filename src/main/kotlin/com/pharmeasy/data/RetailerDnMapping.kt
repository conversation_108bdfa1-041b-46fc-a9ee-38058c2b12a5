package com.pharmeasy.data

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import java.time.LocalDateTime
import javax.persistence.*

@JsonIgnoreProperties(ignoreUnknown = true)
@Entity
@Table(name = "retailer_dn_mapping")
data class RetailerDnMapping(
    // Add one to one mapping here for RetailerDebitNote
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "retailer_debit_note_id", referencedColumnName = "id")
    var retailerDebitNote: RetailerDebitNote? = null,

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "credit_note_id", referencedColumnName = "id")
    var creditNote: CreditNote? = null,

    @OneToOne
    @JoinColumn(name = "bk_cheque_handle_id", referencedColumnName = "id")
    var bkChequeHandle: ChequeHandle? = null,

    @Column(name = "cheque_bounce_date")
    var bounceDate: LocalDateTime? = null
) : BaseEntityDeprecated()
