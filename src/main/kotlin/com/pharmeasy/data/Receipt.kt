package com.pharmeasy.data

import com.pharmeasy.type.AdvancePaymentSource
import com.pharmeasy.type.PaymentType
import com.pharmeasy.type.ReceiptStatus
import com.pharmeasy.type.ReceiptType
import org.hibernate.annotations.DynamicUpdate
import org.hibernate.envers.AuditTable
import org.hibernate.envers.Audited
import org.hibernate.envers.NotAudited
import java.time.LocalDate
import java.time.LocalDateTime
import javax.persistence.Column
import javax.persistence.Entity
import javax.persistence.EnumType
import javax.persistence.Enumerated
import javax.persistence.FetchType
import javax.persistence.GeneratedValue
import javax.persistence.GenerationType
import javax.persistence.Id
import javax.persistence.JoinColumn
import javax.persistence.OneToOne
import javax.persistence.Table
import javax.persistence.Version

@Entity
@Table(name = "receipt")
@Audited
@AuditTable(value = "receipt_history")
@DynamicUpdate
data class Receipt(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long?,
    @Column(name = "receipt_number")
    var receiptNumber: String,
    @Column(name = "created_at")
    var createdAt: LocalDateTime,
    @Column(name = "created_by")
    var createdBy: String,
    @Column(name = "updated_at")
    var updatedAt: LocalDateTime? = LocalDateTime.now(),
    @Column(name = "updated_by")
    var updatedBy: String? = null,
    @Column(name = "payment_transaction_id")
    var paymentTransactionId: String?,
    @Column(name = "remarks")
    var remarks: String? = null,
    @Column(name = "amount")
    var amount: Double? = null,
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    var status: ReceiptStatus = ReceiptStatus.GENERATED,
    @Version
    @Column(name = "version", columnDefinition = "integer DEFAULT 0", nullable = false)
    var version: Int = 0,
    @Column(name = "iteration", columnDefinition = "integer DEFAULT 0", nullable = false)
    var iteration: Int = 0,
    @Column(name = "advance_amount")
    var advanceAmount: Double?,
    @Enumerated(EnumType.STRING)
    @Column(name = "source")
    var source: AdvancePaymentSource = AdvancePaymentSource.SYSTEM,
    @Column(name = "advance_id")
    var advanceId: Long? = null,
    var tenant: String? = null,
    var partnerId: Long? = null,
    var partnerDetailId: Long? = null,
    var unUtilizedAmount: Double? = null,
    var salesmanId: Long? = null,
    var salesmanName: String? = null,
    @Enumerated(EnumType.STRING)
    var paymentType: PaymentType? = null,
    var txReferenceNumber: String? = null,
    var transactionDate: LocalDate? = null,
    var bankName: String? = null,
    var retailerTxnDate: LocalDate? = null,
    var retailerName: String? = null,
    var isBankDeposit: Boolean? = false,
    var bankDepositSlipNo: String? = null,
    @Enumerated(EnumType.STRING)
    var receiptType: ReceiptType? = null,
    @OneToOne(targetEntity = DraftReceipt::class, fetch = FetchType.LAZY)
    @JoinColumn(name = "draft_receipt_id")
    @NotAudited
    var draftReceipt: DraftReceipt? = null
) {
    constructor(draftReceipt: DraftReceipt, user: String) : this(
        null, draftReceipt.receiptNumber, LocalDateTime.now(), user, LocalDateTime.now(), user,
        draftReceipt.paymentTransactionId, draftReceipt.remarks, draftReceipt.amount, draftReceipt.status,
        0, 0, null, draftReceipt.source, null,
        draftReceipt.tenant, draftReceipt.partnerId, draftReceipt.partnerDetailId, draftReceipt.unUtilizedAmount,
        draftReceipt.salesmanId, draftReceipt.salesmanName, draftReceipt.paymentType, draftReceipt.txReferenceNumber,
        draftReceipt.transactionDate, draftReceipt.bankName, draftReceipt.retailerTxnDate,
        draftReceipt.retailerName, draftReceipt.isBankDeposit, draftReceipt.bankDepositSlipNo, draftReceipt.receiptType,
        draftReceipt
    )
}
