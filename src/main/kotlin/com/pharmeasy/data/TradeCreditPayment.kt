package com.pharmeasy.data

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.pharmeasy.type.PaymentStatus
import java.time.LocalDateTime
import javax.persistence.*

@JsonIgnoreProperties(ignoreUnknown = true)
@Entity
@Table(name = "trade_credit_payment")
data class TradeCreditPayment(
    @Column(name = "partner_detail_id")
    var partnerDetailId: Long,
    @Column(name = "transaction_id")
    var transactionId: String,
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    var status: PaymentStatus = PaymentStatus.PENDING,
    @Column(name = "amount")
    var amount: Double?,
    @Column(name = "paid_amount")
    var paidAmount: Double?,
    @Column(name = "due_date")
    var dueDate: LocalDateTime?,
    @Version
    @Column(name = "iteration", columnDefinition = "integer DEFAULT 0", nullable = false)
    var iteration: Int = 0
) : BaseEntity()
