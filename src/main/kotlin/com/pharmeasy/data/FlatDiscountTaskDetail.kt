package com.pharmeasy.data

import com.pharmeasy.type.InvoiceType
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.math.BigDecimal
import java.time.LocalDateTime
import javax.persistence.*

@Entity
@Table(name = "flat_discount_task_detail")
data class FlatDiscountTaskDetail(
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @CreationTimestamp
    @Column(name = "created_on")
    val createdOn: LocalDateTime = LocalDateTime.now(),

    @UpdateTimestamp
    @Column(name = "updated_on")
    val updatedOn: LocalDateTime = LocalDateTime.now(),

    @Column(name = "partner_id")
    val partnerId: Long,

    @Column(name = "partner_detail_id")
    val partnerDetailId: Long,

    @Column(name = "partner_name")
    val partnerName: String,

    @Column(name = "discount_amount")
    val discountAmount: BigDecimal,

    @Column(name = "taxable_amount")
    val taxableAmount: BigDecimal,

    @Column(name = "tax_amount")
    val taxAmount: BigDecimal,

    @Column(name = "sgst")
    val sgst: Int,

    @Column(name = "cgst")
    val cgst: Int,

    @Column(name = "igst")
    val igst: Int,

    @Column(name = "state_id")
    val stateId: Long,

    @Column(name = "client")
    @Enumerated(EnumType.STRING)
    val client: InvoiceType,

    @ManyToOne
    @JoinColumn(name = "flat_discount_task_id", referencedColumnName = "id")
    val flatDiscountTask: FlatDiscountTask,

    @Column(name = "raised_by")
    val raisedBy: String?,

    @Column(name = "remark")
    val remark: String?,

    @Column(name="reason_code")
    val reasonCode: Long? = 13L // Default value for reason category other is reason code (13L) is used if user doesn't enter any.
)
