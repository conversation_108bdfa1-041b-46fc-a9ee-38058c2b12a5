package com.pharmeasy.data

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import org.springframework.format.annotation.DateTimeFormat
import java.time.LocalDateTime
import javax.persistence.*


@JsonIgnoreProperties(ignoreUnknown = true)
@Entity
@Table(name = "retailer_debit_note_items")
data class RetailerDebitNoteItems(
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long?,

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @Column(name = "created_on")
    var createdOn: LocalDateTime?,

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @Column(name = "updated_on")
    var updatedOn: LocalDateTime?,

    @Column(name = "ucode")
    var ucode: String?,

    @Column(name = "batch")
    var batch: String?,

    @Column(name = "quantity")
    var quantity: Int?,

    @Column(name = "cgst")
    var cgst: Double = 0.0,

    @Column(name = "sgst")
    var sgst: Double = 0.0,

    @Column(name = "igst")
    var igst: Double = 0.0,

    @Column(name = "hsn")
    var hsn: String? = null,

    @Column(name = "taxable_amount")
    var taxableAmount: Double?,

    @Column(name = "net_gst_amt")
    var netGstAmt: Double = 0.0,

    @Column(name = "total_amount")
    var totalAmount: Double? = null,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "retailer_debit_note_id", nullable = true)
    @JsonIgnore
    var retailerDebitNote: RetailerDebitNote?

)
