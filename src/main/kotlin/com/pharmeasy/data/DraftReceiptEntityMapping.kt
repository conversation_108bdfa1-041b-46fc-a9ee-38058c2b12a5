package com.pharmeasy.data

import com.pharmeasy.type.SettleableType
import javax.persistence.Entity
import javax.persistence.EnumType
import javax.persistence.Enumerated
import javax.persistence.FetchType
import javax.persistence.JoinColumn
import javax.persistence.ManyToOne

@Entity
data class DraftReceiptEntityMapping(
    @ManyToOne(targetEntity = Receipt::class, fetch = FetchType.LAZY)
    @JoinColumn(name = "draft_receipt_id", referencedColumnName = "id")
    var receipt: DraftReceipt?,
    @Enumerated(EnumType.STRING)
    var entityType: SettleableType,
    var entityId: Long,
    var entityTxAmount: Double,
    var active: Boolean = true
) : BaseEntity()
