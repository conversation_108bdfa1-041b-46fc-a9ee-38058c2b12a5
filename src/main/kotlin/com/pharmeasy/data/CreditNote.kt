package com.pharmeasy.data

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import org.springframework.format.annotation.DateTimeFormat
import com.pharmeasy.model.CreditNoteClosureType
import com.pharmeasy.model.FlatDiscountCreditNoteDto
import com.pharmeasy.model.SlabDiscountCreditNoteDto
import com.pharmeasy.type.*
import org.hibernate.annotations.Fetch
import org.hibernate.annotations.FetchMode
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.Objects
import javax.persistence.*
import kotlin.jvm.Transient

@Entity
@JsonIgnoreProperties(ignoreUnknown = true)
@Table(name = "creditnote")
data class CreditNote (
        @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
        var id: Long,

        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
        @Column(name = "created_on")
        var createdOn: LocalDateTime?,

        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
        @Column(name = "updated_on")
        var updatedOn: LocalDateTime?,

        @Column(name = "created_by")
        var createdBy: String?,

        @Column(name = "updated_by")
        var updatedBy: String?,

        @Enumerated(EnumType.STRING)
        @Column(name = "status")
        var status: NoteStatus = NoteStatus.PENDING,

        @Enumerated(EnumType.STRING)
        @Column(name= "note_type")
        var noteType: NoteTypes = NoteTypes.PURCHASE_RETURN,

        @Column(name = "amount")
        var amount: BigDecimal,

        @Column(name = "supplier_id")
        var supplierId: Long,

        @Column(name = "supplier_name")
        var supplierName: String,

        @Column(name = "remarks")
        var remarks: String?,

        @Column(name = "settlement_id")
        var settlementId: Long?,

        @Column(name = "invoice_id")
        var invoiceId: Long?,

        @Transient
        var debitNotes: MutableList<DebitNote>? = mutableListOf(),

        @Enumerated(EnumType.STRING)
        @Column(name = "closure_type")
        var closureType: CreditNoteClosureType? = CreditNoteClosureType.SETTLEMENT,

        @Column(name = "partner_id")
        var partnerId: Long? = null,

        @Column(name = "partner_detail_id")
        var partnerDetailId: Long? = null,

        @Column(name = "tenant")
        var tenant: String? = null,

        @Column(name = "credit_note_number")
        var creditNoteNumber: String? = null,

        @Enumerated(EnumType.STRING)
        @Column(name = "type")
        var type: PartnerType = PartnerType.VENDOR,

        @Enumerated(EnumType.STRING)
        @Column(name = "client")
        var client: InvoiceType = InvoiceType.VENDOR,

        @Enumerated(EnumType.STRING)
        @Column(name = "receipt_status")
        var receiptStatus: ReceiptStatus? = ReceiptStatus.NOT_RECEIVED,

        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
        @Column(name = "expected_date")
        var expectedDate: LocalDate?,

        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
        @Column(name = "vendor_creditnote_date")
        var vendorCreditNoteDate: LocalDate?,

        @Column(name = "vendor_creditnote_amount")
        var vendorCreditNoteAmount: BigDecimal?,

        @Column(name = "vendor_creditnote_number")
        var vendorCreditNoteNumber: String?,

        @Transient
        var conversion :Double? = 0.0,

        @OneToMany(orphanRemoval = false, fetch = FetchType.EAGER)
        @JoinColumn(name = "credit_note_id")
        var debitItems: MutableList<DebitNoteDetails> = mutableListOf(),

        @Column(name = "qr_code")
        var qrCode: String?=null,

        @Column(name = "irn")
        var irn: String?= null,

        @Column(name = "remaining_amount")
        var remainingAmount: BigDecimal = amount,

        @Transient
        var amountUsed: BigDecimal? = BigDecimal.ZERO,

        @OneToOne
        @JoinColumn(name = "slab_task_detail_id", referencedColumnName = "id")
        var slabTaskDetail: SlabTaskDetail? = null,

        @OneToOne
        @JoinColumn(name = "flat_task_detail_id", referencedColumnName = "id")
        var flatTaskDetail: FlatDiscountTaskDetail? = null,

        @Transient
        var slabDiscountDetail: SlabDiscountCreditNoteDto? = null,

        @Transient
        var deductRemainingAmount: Boolean? = true,

        @Transient
        var settlementNumbers: String? = "",

        @Transient
        var flatDiscountCnDetail: FlatDiscountCreditNoteDto? = null,

        @Column(name="parent_tenant")
        var parentTenant: String? = null,

        @Column(name="is_migrated")
        var isMigrated: Boolean? = false,

        @Column(name="reference_id")
        var referenceId: String? = null,

        @Column(name = "base_cn_amount")
        var baseCNAmount: Double? = null,

        @Column(name = "round_off_amount")
        var roundOffAmount: Double? = null,

        @Column(name = "reference_dn_id")
        var referenceDnId: Long? = null,

        @Transient
        var dummyCnAmt: BigDecimal? = BigDecimal.ZERO,

        @OneToMany(fetch = FetchType.EAGER, mappedBy = "creditNoteId")
        var dummyCreditNoteItems: MutableSet<DummyCreditNoteDetails> = mutableSetOf(),

        @Version
        @Column(name = "version", columnDefinition = "integer DEFAULT 0", nullable = false)
        var version: Int = 0,

        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
        @Column(name = "document_date")
        var documentDate: LocalDateTime? = LocalDateTime.now(),

        @OneToOne
        @JoinColumn(name = "bulk_cn_reason_code_mapping_id", referencedColumnName = "id")
        var reasonCode: BulkCNReasonCodeMapping? = null


        ) {
        override fun hashCode(): Int {
                return Objects.hash(
                        id,
                        createdOn,
                        updatedOn,
                        createdBy,
                        updatedBy,
                        status,
                        noteType,
                        amount,
                        supplierId,
                        supplierName,
                        remarks,
                        settlementId,
                        invoiceId,
                        closureType,
                        partnerId,
                        partnerDetailId,
                        tenant,
                        creditNoteNumber,
                        type,
                        client,
                        receiptStatus,
                        expectedDate,
                        vendorCreditNoteDate,
                        vendorCreditNoteAmount,
                        vendorCreditNoteNumber,
                        baseCNAmount,
                        roundOffAmount,
                        referenceDnId
                )
        }
}
