package com.pharmeasy.data

import com.fasterxml.jackson.databind.PropertyNamingStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming

@JsonNaming(value = PropertyNamingStrategy.UpperCamelCaseStrategy::class)
data class NonIRNEwayBillGenerationDto(
    var DocumentNumber: String,
    var DocumentType: String,
    var DocumentDate: String,
    var SupplyType: String,
    var SubSupplyType: String,
    var SubSupplyTypeDesc: String,
    var TransactionType: String,
    var BuyerDtls: EntityDetailsDto,
    var SellerDtls: EntityDetailsDto,
    var ItemList: List<EwayItemDto>,
    var TotalInvoiceAmount: Double,
    var TotalCgstAmount: Double=0.0,
    var TotalSgstAmount: Double=0.0,
    var TotalIgstAmount: Double=0.0,
    var TotalCessAmount: Double=0.0,
    var TotalCessNonAdvolAmount: Double=0.0,
    var TotalAssessableAmount: Double,
    var OtherAmount: Double = 0.0,
    var OtherTcsAmount: Double = 0.0,
    var TransId: String="",
    var TransName: String?=null,
    var TransMode: String?=null,
    var Distance: Long = 100,
    var VehNo: String? = null,
    var VehType: String? = null,
    var SellerId: Int?,
    var SellerType: SellerType?,
    var debitNoteNumber: String
)

@JsonNaming(value = PropertyNamingStrategy.UpperCamelCaseStrategy::class)
data class EwayItemDto(
    var ProdName: String?,
    var ProdDesc: String = "",
    var HsnCd: String?,
    var Qty: String?,
    var Unit: String?,
    var AssAmt: Double?,
    var IgstRt: Double ?=0.0,
    var CgstRt: Double ?=0.0,
    var SgstRt: Double ?=0.0,
    var IgstAmt: Double ?=0.0,
    var CgstAmt: Double ?=0.0,
    var SgstAmt: Double ?=0.0,
    var CesRt: Double ?= 0.0,
    var CesAmt: Double ?= 0.0,
    var OthChrg: Double ?= 0.0
)

@JsonNaming(value = PropertyNamingStrategy.UpperCamelCaseStrategy::class)
data class EntityDetailsDto(
    var Gstin: String,
    var LglNm: String,
    var TrdNm: String,
    var Addr1: String,
    var Addr2: String="",
    var Loc: String="",
    var Pin: Int,
    var Stcd: String=""
)

enum class SellerType(val value: String) {
    THEA("THEA"), ARSENAL("ARSENAL"), STORE("STORE"),DARK_STORE("DARK_STORE");
    companion object {
        fun fromString(value: String): SellerType =
            values().first { it.value == value }
    }
}