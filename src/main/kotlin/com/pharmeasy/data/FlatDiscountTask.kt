package com.pharmeasy.data

import com.pharmeasy.type.NoteTypes
import com.pharmeasy.type.SlabTaskStatus
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.time.LocalDate
import java.time.LocalDateTime
import javax.persistence.*

@Entity
@Table(name ="flat_discount_task")
data class FlatDiscountTask(
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @CreationTimestamp
    @Column(name = "created_on")
    val createdOn: LocalDateTime = LocalDateTime.now(),

    @UpdateTimestamp
    @Column(name = "updated_on")
    val updatedOn: LocalDateTime = LocalDateTime.now(),

    @Column(name = "start_date")
    val startDate: LocalDate,

    @Column(name = "end_date")
    val endDate: LocalDate,

    @Column(name = "created_by")
    val createdBy: String,

    @Column(name = "created_by_id")
    var createdById: String? ,

    @Column(name = "assigned_to")
    var assignedTo: String? = null,

    @Column(name = "assigned_to_id")
    var assignedToId: String? = null,

    @Column(name = "approved_by")
    var approvedBy: String? = null,

    @Column(name = "approved_on")
    var approvedOn: LocalDateTime? = null,

    @Column(name = "partner_file")
    var partnerFile: String,

    @Column(name = "draft_file")
    var draftFile: String? = null,

    @Column(name = "tenant")
    val tenant: String,
    
    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    val type: NoteTypes,

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    var status: SlabTaskStatus,

    @ManyToOne
    @JoinColumn(name = "company_id", referencedColumnName = "id")
    val company: CompanyTenantMapping,

    @Column(name = "uuid")
    val uuid: String? = null
)
