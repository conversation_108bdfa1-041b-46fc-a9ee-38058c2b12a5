package com.pharmeasy.data

import com.pharmeasy.dto.SettleableDetails
import com.pharmeasy.type.AdvancePaymentSource
import com.pharmeasy.type.PaymentType
import com.pharmeasy.type.ReceiptStatus
import com.pharmeasy.type.ReceiptType
import com.pharmeasy.type.SettleableType
import org.hibernate.envers.AuditTable
import org.hibernate.envers.Audited
import java.time.LocalDate
import javax.persistence.Column
import javax.persistence.Entity
import javax.persistence.EnumType
import javax.persistence.Enumerated
import javax.persistence.FetchType
import javax.persistence.OneToMany
import javax.persistence.Table
import javax.persistence.Version

@Entity
@Table(name = "receipt_draft")
@Audited
@AuditTable(value = "receipt_draft_history")
data class DraftReceipt(
    @Column(name = "receipt_number")
    var receiptNumber: String,
    @Column(name = "payment_transaction_id")
    var paymentTransactionId: String?,
    @Column(name = "remarks")
    var remarks: String? = null,
    @Column(name = "amount")
    var amount: Double? = null,
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    var status: ReceiptStatus = ReceiptStatus.DRAFT,
    @Enumerated(EnumType.STRING)
    @Column(name = "source")
    var source: AdvancePaymentSource = AdvancePaymentSource.SYSTEM,
    var tenant: String? = null,
    var partnerId: Long? = null,
    var partnerDetailId: Long? = null,
    var unUtilizedAmount: Double? = null,
    var salesmanId: Long? = null,
    var salesmanName: String? = null,
    @Enumerated(EnumType.STRING)
    var paymentType: PaymentType? = null,
    var txReferenceNumber: String? = null,
    var transactionDate: LocalDate? = null,
    var bankName: String?,
    var retailerTxnDate: LocalDate?,
    var retailerName: String?,
    var isBankDeposit: Boolean? = false,
    var bankDepositSlipNo: String? = null,
    @Enumerated(EnumType.STRING)
    var receiptType: ReceiptType? = null,
    @OneToMany(targetEntity = DraftReceiptEntityMapping::class, fetch = FetchType.LAZY)
    var settleableMappings: MutableList<DraftReceiptEntityMapping> = mutableListOf(),
    @Version
    @Column(name = "version", columnDefinition = "integer DEFAULT 0", nullable = false)
    var version: Int = 0
) : BaseEntity() {
    /* * Adds a mapping for a settleable entity to the receipt.
     * If the entity already exists and is inactive, it will be reactivated with the new amount.
     * If the entity does not exist, it will be created.
     * The settled amount will reduce the unutilized amount of the receipt.
     * Throws an exception if the amount to be settled exceeds the pending amount for the entity.
     */
    fun addSettleableMapping(
        settleableDetails: SettleableDetails,
        amountToBeSettled: Double
    ): Boolean {
        require(amountToBeSettled > 0) { "Amount to be settled must be greater than zero" }
        require(settleableDetails.pendingAmount >= amountToBeSettled) {
            "Amount to be settled exceeds pending amount " +
                "for entity: ${settleableDetails.id} of type: ${settleableDetails.type}"
        }
        val exMapping =
            settleableMappings
                .find { it.entityId == settleableDetails.id && it.entityType == settleableDetails.type }
        if (exMapping != null) {
            exMapping.active = true
            exMapping.entityTxAmount = amountToBeSettled

            // Update unUtilizedAmount: if mapping is inactive, subtract the amountToBeSettled from it
            // Otherwise, subtract the difference between the amountToBeSettled and the current mapping amount
            unUtilizedAmount =
                if (!exMapping.active) {
                    unUtilizedAmount!! - amountToBeSettled
                } else {
                    unUtilizedAmount!! - (amountToBeSettled - exMapping.entityTxAmount)
                }
        } else {
            settleableMappings.add(
                DraftReceiptEntityMapping(
                    receipt = this,
                    entityId = settleableDetails.id,
                    entityType = settleableDetails.type,
                    entityTxAmount = amountToBeSettled
                )
            )
            unUtilizedAmount = unUtilizedAmount!! - amountToBeSettled
        }
        val computedTotal =
            unUtilizedAmount!! + settleableMappings.sumOf { it.entityTxAmount }
        check(computedTotal == amount) {
            "Unutilized amount does not match total transaction amount " +
                "for payment: $paymentTransactionId receipt: $receiptNumber"
        }
        return true
    }

    fun getActiveSettlements(): List<DraftReceiptEntityMapping> {
        return settleableMappings.filter { it.active }
    }

    fun findSettleableMappingByIdAndType(
        entityId: Long,
        entityType: SettleableType
    ) = settleableMappings
        .find { it.entityId == entityId && it.entityType == entityType }
}
