package com.pharmeasy.data

import org.hibernate.annotations.Fetch
import org.hibernate.annotations.FetchMode
import java.math.BigDecimal
import javax.persistence.*

@Entity
@Table(name = "settlement_debit_note_distribution_mapping")

data class SettlementDebitNoteDistributionMapping (
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "credit_note_id")
    var creditNote: CreditNote? = null,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "advance_payment_id")
    var advancePayment: AdvancePayment? = null,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "settlement_id")
    var settlement: Settlement? = null,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "retailer_debit_note_id")
    var retailerDebitNote: RetailerDebitNote?,

    @Column(name = "amount")
    var amount: BigDecimal,

    ): BaseEntity()