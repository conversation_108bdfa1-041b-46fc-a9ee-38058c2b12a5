package com.pharmeasy.data

import com.pharmeasy.type.Status
import javax.persistence.*

@Entity
@Table(name="maker_checker_requests_detail")
class MakerCheckerRequestsDetails(
    @Column(name="maker_checker_requests_id")
    var makerCheckerRequestsId: Long,

    @Column(name = "partner_detail_id")
    var partnerDetailId: Long,

    @Column(name = "partner_name")
    var partnerName: String,

    @Column(name = "request_details", columnDefinition = "TEXT")
    var requestDetails: String?,

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    var status: Status = Status.PENDING_APPROVAL
): BaseEntity()
