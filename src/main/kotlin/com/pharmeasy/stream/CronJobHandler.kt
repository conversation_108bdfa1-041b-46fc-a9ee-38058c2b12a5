package com.pharmeasy.stream

import com.pharmeasy.service.CronJobService
import com.pharmeasy.type.CronType
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.cloud.stream.annotation.StreamListener
import org.springframework.stereotype.Service

@Service
class CronJobHandler {

    @Autowired
    private lateinit var cronJobService: CronJobService

    @StreamListener(CronJobSink.CACHE_CRON_CONSUMER)
    fun cacheVendorLedgerCornConsumer(cronType: CronType) {
        when(cronType) {
            CronType.CACHE_VENDOR_LEDGER -> cronJobService.cacheVendorLedgerCorn(cronType)
            CronType.CACHE_SUPPLIER_DATA -> cronJobService.cacheSupplierDataCron(cronType)
        }
    }
}