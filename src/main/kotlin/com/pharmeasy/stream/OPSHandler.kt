package com.pharmeasy.stream

import com.fasterxml.jackson.databind.ObjectMapper
import com.pharmeasy.model.ops.RIODisbursementListDTO
import com.pharmeasy.model.ops.RIOPaymentDTO
import com.pharmeasy.service.DigitalReceiptsWritebackService
import com.pharmeasy.service.ops.PaymentService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.cloud.stream.annotation.StreamListener
import org.springframework.stereotype.Service

@Service
class OPSHandler {
    companion object {
        private val log = LoggerFactory.getLogger(OPSHandler::class.java)
    }

    @Autowired
    private lateinit var paymentService: PaymentService

    @Autowired
    private lateinit var rioDraftInvoiceService: DigitalReceiptsWritebackService

    @StreamListener(OPSSink.RIO_PAY_PAYMENT_SYNC)
    fun rioPayPaymentSync(rioPaymentDTOList: List<RIOPaymentDTO>) {
        log.info("Received message in rioPayPaymentSync ${ObjectMapper().writeValueAsString(rioPaymentDTOList)}")
        paymentService.processRioPaymentV2(rioPaymentDTOList)
        log.info("Completed message in rioPayPaymentSync")

    }

    @StreamListener(OPSSink.RIO_PAY_DISBURSEMENT_SYNC)
    fun rioPayDisbursementSync(rioDisbursementListDTO: RIODisbursementListDTO) {
        log.info("Received message in rioPayDisbursementSync ${ObjectMapper().writeValueAsString(rioDisbursementListDTO)}")
        try {
            rioDisbursementListDTO.data.forEach { rioDisbursementDTO ->
                try {
                    paymentService.processRioDisbursement(rioDisbursementDTO)
                } catch (e: Exception) {
                    log.error("Error processing rio payment for ${ObjectMapper().writeValueAsString(rioDisbursementDTO)}, error: ${e.printStackTrace()}")
                }

            }
        } catch (e: Exception) {
            log.error("Failed processing message in rioPayDisbursementSync ${e}", e)
            throw e
        }

        log.info("Completed message in rioPayDisbursementSync")
    }


}
