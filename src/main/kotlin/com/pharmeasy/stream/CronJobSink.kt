package com.pharmeasy.stream

import org.springframework.cloud.stream.annotation.Input
import org.springframework.cloud.stream.annotation.Output
import org.springframework.messaging.SubscribableChannel

interface CronJobSink {
    companion object {
        const val CACHE_CRON_PRODUCER = "cacheCornProducer"
        const val CACHE_CRON_CONSUMER = "cacheCornConsumer"
    }

    @Output
    fun cacheCornProducer(): SubscribableChannel

    @Input
    fun cacheCornConsumer(): SubscribableChannel
}