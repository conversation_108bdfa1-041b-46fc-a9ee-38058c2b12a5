package com.pharmeasy.stream

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.pharmeasy.exception.ErrorUtils
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.*
import com.pharmeasy.service.CompanyService
import com.pharmeasy.service.DebitNoteService
import com.pharmeasy.service.EmailService
import com.pharmeasy.service.InvoiceService
import com.pharmeasy.type.InvoiceType
import com.pharmeasy.type.NoteTypes
import com.pharmeasy.type.ReturnReason
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.cloud.stream.annotation.StreamListener
import org.springframework.stereotype.Service
import java.time.LocalDate

@Service
class DebitNoteHandler(@Value("\${app.alertEmailEnabled}") val alertEmailEnabled: Boolean) {

    @Autowired
    private lateinit var debitNoteService: DebitNoteService

    @Autowired
    private lateinit var emailService: EmailService

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @Autowired
    private lateinit var companyService: CompanyService

    companion object {
        private val log = LoggerFactory.getLogger(DebitNoteHandler::class.java)
    }

    @StreamListener(DebitNoteSink.CHANNEL)
    fun createDebitNotes(purchaseReturnDebitNoteDto: PurchaseReturnDebitNoteDto) {
        log.info("Received create debit note event for purchase return: ${objectMapper.writeValueAsString(purchaseReturnDebitNoteDto)}")
        try {
            log.debug("Received Purchase issue ids for debit note creation ${purchaseReturnDebitNoteDto}")
            debitNoteService.createDebitNotes(purchaseReturnDebitNoteDto)
        } catch (e: Exception) {
            log.error("DebitNoteHandler - ERROR : ${e.message}", e)

            if (!InvoiceService.INVALID_PROC_TYPE_MSG.equals(e.message)) {
                if (alertEmailEnabled) emailService.sendMessage("Alert: Vendor DN event failed : invoiceNumber : ${purchaseReturnDebitNoteDto.purchaseReturns.get(0).invoiceNo} !", "PurchaseReturnDebitNote for invoiceNumber : ${purchaseReturnDebitNoteDto.purchaseReturns.get(0).invoiceNo} failed,  ERROR :\n  ${ErrorUtils.stackTraceToString(e)}", "<EMAIL>", "<EMAIL>,<EMAIL>", false)
                throw RequestException("DebitNoteHandler ERROR : ${e.message}")
            }
        }
        log.info("Completed processing for event createDebitNotes -  ")
    }

    @StreamListener(CustomerDebitNoteSink.CHANNEL)
    fun createDebitNotes(saleReturnDebitNoteDto: SaleReturnDebitNoteDto) {
        try {
            log.info("Received sale return ids for debit note creation ${saleReturnDebitNoteDto.data.id}, ${saleReturnDebitNoteDto.data.transactionId}, ${saleReturnDebitNoteDto.data.externalOrderId},${saleReturnDebitNoteDto.data.client}, ${saleReturnDebitNoteDto.data.tenant} ")
            if(InvoiceType.valueOf(saleReturnDebitNoteDto.data.client) == InvoiceType.RIO || saleReturnDebitNoteDto.data.consolidatedStoreInvoiceInvoiceId != null) {
                var acceptedDebit = mutableListOf<SaleIssueItem>()
                var expiredDebit = mutableListOf<SaleIssueItem>()
                saleReturnDebitNoteDto.data.items.forEach {
                    if ((it.returnReason.equals(ReturnReason.NEAR_EXPIRY) || it.returnReason.equals(ReturnReason.EXPIRED)) && saleReturnDebitNoteDto.data.client !in listOf(InvoiceType.PE.name, InvoiceType.ML.name, InvoiceType.B2C.name) ) {
                        expiredDebit.add(it)
                    } else {
                        acceptedDebit.add(it)
                    }

                }

                if (acceptedDebit.isNotEmpty()) {
                    var saleReturnDebitNote = saleReturnDebitNoteDto
                    saleReturnDebitNote.data.items = acceptedDebit
                    debitNoteService.createCustomerDebitNotes(saleReturnDebitNote, NoteTypes.SR_ACCEPTED, false)
                }
                if (expiredDebit.isNotEmpty()) {
                    var saleReturnDebitNote = saleReturnDebitNoteDto
                    saleReturnDebitNote.data.items = expiredDebit
                    debitNoteService.createCustomerDebitNotes(saleReturnDebitNote, NoteTypes.SR_EXPIRED, false)
                }
            }
        } catch (e: Exception) {
            log.error("createDebitNotes sale returns- ERROR : ${e.message}", e)
            if ( !e.message.equals("Duplicate DN")) {
                if (alertEmailEnabled) emailService.sendMessage("Alert: Customer DN event failed : invoice : ${saleReturnDebitNoteDto.data.invoiceNo} !", "SaleReturnDN for invoiceNumber : ${saleReturnDebitNoteDto.data.invoiceNo} failed,  ERROR :\n  ${ErrorUtils.stackTraceToString(e)}", "<EMAIL>", "<EMAIL>,<EMAIL>", false)
                throw RequestException("DebitNoteHandler ERROR : ${e.message}")
            }
        }
        log.info("Completed processing for event createDebitNotes SR")
    }


    @StreamListener(B3DebitNoteSink.CHANNEL)
    fun createDsDebitNotes(saleReturnDebitNoteDto: SaleReturnDebitNoteDto) {
        try {
            log.info("Received DS sale return ids for debit note creation ${saleReturnDebitNoteDto.data.id}, ${saleReturnDebitNoteDto.data.externalOrderId},${saleReturnDebitNoteDto.data.client}, ${saleReturnDebitNoteDto.data.tenant} ")
            var acceptedDebit = mutableListOf<SaleIssueItem>()
            var expiredDebit = mutableListOf<SaleIssueItem>()

            saleReturnDebitNoteDto.data.items.forEach {

                if(it.returnReason.equals(ReturnReason.NEAR_EXPIRY) || it.returnReason.equals(ReturnReason.EXPIRED)) {
                    expiredDebit.add(it)
                } else {
                    acceptedDebit.add(it)
                }

            }
            if(acceptedDebit.isNotEmpty()){
                var saleReturnDebitNote = saleReturnDebitNoteDto
                saleReturnDebitNote.data.items = acceptedDebit
                debitNoteService.createCustomerDebitNotes(saleReturnDebitNote, NoteTypes.SR_ACCEPTED,true)
            }
            if(expiredDebit.isNotEmpty()) {
                var saleReturnDebitNote = saleReturnDebitNoteDto
                saleReturnDebitNote.data.items = expiredDebit
                debitNoteService.createCustomerDebitNotes(saleReturnDebitNote, NoteTypes.SR_EXPIRED,true)
            }
            // create purchase return for dark store
        } catch (e: Exception) {
            log.error("createDsDebitNotes ERROR : ${e.message}", e)
            if ( !e.message.equals("Duplicate DN")) {
                if (alertEmailEnabled) emailService.sendMessage("Alert: DS Customer DN event failed : invoice : ${saleReturnDebitNoteDto.data.invoiceNo} !", "SaleReturnDN for invoiceNumber : ${saleReturnDebitNoteDto.data.invoiceNo} failed,  ERROR :\n  ${ErrorUtils.stackTraceToString(e)}", "<EMAIL>", "<EMAIL>,<EMAIL>", false)
                throw RequestException("DebitNoteHandler ERROR : ${e.message}")
            }
        }
        log.info("Completed processing for event createDsDebitNotes")
    }


    @StreamListener(RioDebitNoteSink.RIO_CHANNEL)
    fun createRioDebitNotes(rioReturnEvent: RIOReturnEvent) {
        try {
            log.info("Received event for rio debit note event ${rioReturnEvent}")
            val mapper = ObjectMapper()
            val actualObj: JsonNode = mapper.readTree(rioReturnEvent.msg)
            val invoiceDateString = actualObj["invoiceDate"].asText()
            var invoiceDate: LocalDate? = null
            if (invoiceDateString != null) {
                invoiceDate = LocalDate.parse(invoiceDateString)
            }
            var rioDebitNoteDTO = RioDebitNoteEventDto(
                retailerId = actualObj.get("retailerId").asLong(),
                distributorId = actualObj.get("distributorId").asLong(),
                invoiceNumber = actualObj.get("invoiceNumber").asText(),
                orderId = actualObj.get("orderId").asText(),
                creditNoteAmount = actualObj.get("creditNoteAmount").asDouble().toBigDecimal(),
                type = actualObj.get("type").asText(),
                client = actualObj.get("client").asText(),
                returnReferenceId = actualObj.get("returnReferenceId").asLong(),
                returnEventType = actualObj.get("returnEventType").asText(),
                debitNoteAmount = actualObj.get("debitNoteAmount").asDouble().toBigDecimal(),
                returnReferenceType = actualObj.get("returnReferenceType").asText(),
                noteType = actualObj.get("noteType").asText(),
                logisticsPackageId = actualObj.get("logisticsPackageId").asText(),
                invoiceDate = invoiceDate,
                baseCreditNoteAmount = actualObj.get("baseCreditNoteAmount").asDouble(),
                roundOffAmount = actualObj.get("roundOffAmount").asDouble()
            )
            debitNoteService.createDebitNoteForRio(rioDebitNoteDTO)
        } catch (e: Exception) {
            log.error("DebitNoteHandler ERROR 1: ${e.message}", e)
            throw Exception("DebitNoteHandler ERROR : ${e.message}")

        }
        log.info("Completed processing for event createRioDebitNotes 1")

    }
    @StreamListener(RioDebitNoteSink.CHANNEL)
    fun createRioDebitNotes(rioReturnDebitNoteDto: RioReturnEventDataDto) {
        try {
            //calling createRioDebitNotes method --update made on 14/12/2022
            //passing only data to service
            log.info("inside createRioDebitNotes")
            debitNoteService.createRioDebitNotes(rioReturnDebitNoteDto.data)
        }catch (e: Exception) {
            if ( !e.message.equals("Duplicate DN")) {
                log.error("RioDebitNoteHandler ERROR  2: ${e.message}", e)
                if (alertEmailEnabled) emailService.sendMessage("Alert: RIO DN event failed : invoice : ${rioReturnDebitNoteDto.data.items[0].invoiceNumber } !", "SaleReturnDN for invoiceNumber : ${rioReturnDebitNoteDto.data.invoiceNo} failed,  ERROR :\n  ${ErrorUtils.stackTraceToString(e)}", "<EMAIL>", "<EMAIL>,<EMAIL>", false)
                throw RequestException("DebitNoteHandler ERROR : ${e.message}")
            }else{
                log.info("Duplicate DN for packageId : ${rioReturnDebitNoteDto.data.packageId}")
            }
        }
        log.info("Completed processing for event createRioDebitNotes 2")
    }

}
