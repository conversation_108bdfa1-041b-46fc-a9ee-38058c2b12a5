package com.pharmeasy.resource

import com.pharmeasy.model.*
import com.pharmeasy.service.*
import com.pharmeasy.type.CronType
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("cron/task")
class CronResource {

    companion object {
        private val log = LoggerFactory.getLogger(CronResource::class.java)
    }

    @Autowired
    private lateinit var cronJobService: CronJobService


    @PostMapping("cache-aggregated-data")
    fun cacheAggregatedDataCron(): Result {
        log.debug("inside cacheAggregatedDataCron")
        return Result(200, "success")
    }

    @PostMapping("cache-vendor-ledger")
    fun cacheVendorLedgerCorn(): Result {
        log.debug("inside cacheVendorLedgerCorn")
        cronJobService.cacheCornTrigger(CronType.CACHE_VENDOR_LEDGER)
        return Result(200, "success")
    }

    @GetMapping("cache-supplier-data")
    fun cacheSupplierDataCron(): Result {
        cronJobService.cacheCornTrigger(CronType.CACHE_SUPPLIER_DATA)
        return Result(200, "success")
    }

    @GetMapping("invoice-status-update")
    fun updateInvoiceWithAmountLessThanOneRupee(): Result {
        cronJobService.updateInvoiceWithAmountLessThanOneRupee()
        return Result(200, "success")
    }

    //change: auto retry
    @PostMapping("/einvoice/auto-retry")
    fun autoRetry(@RequestParam(required = true) eInvoiceType: String) : Result {
        cronJobService.retryEInvoices(eInvoiceType);
        return Result(200, "success");
    }

}