package com.pharmeasy.resource

import com.pharmeasy.annotation.Get
import com.pharmeasy.annotation.Post
import com.pharmeasy.annotation.Put
import com.pharmeasy.data.CreditNote
import com.pharmeasy.data.DraftCreditNote
import com.pharmeasy.data.nsr.DraftNsr
import com.pharmeasy.exception.BookkeeperErrors
import com.pharmeasy.exception.DetailedRequestException
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.*
import com.pharmeasy.model.advancepayment.FlatDiscountCreditNotePrintDto
import com.pharmeasy.model.creditnote.DiscountCreditNoteDto
import com.pharmeasy.model.creditnote.DraftNsrCreditNoteDto
import com.pharmeasy.model.creditnote.NsrCreditNotePrintDto
import com.pharmeasy.repo.CreditNoteReservationRepo
import com.pharmeasy.repo.DebitNoteRepo
import com.pharmeasy.service.CreditNoteService
import com.pharmeasy.service.SlabCreditNoteService
import com.pharmeasy.service.WriteoffService
import com.pharmeasy.type.*
import com.pharmeasy.util.tenant_header
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.integration.redis.util.RedisLockRegistry
import org.springframework.web.bind.annotation.*
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.concurrent.TimeUnit

@RestController
@RequestMapping("/creditnotes")
class CreditNoteResource {

    companion object {
        private val log = LoggerFactory.getLogger(CreditNoteResource::class.java)
    }

    @Autowired
    private lateinit var creditNoteService: CreditNoteService

    @Autowired
    private lateinit var slabCreditNoteService: SlabCreditNoteService

    @Autowired
    private lateinit var creditNoteReservationRepo: CreditNoteReservationRepo

    @Autowired
    private lateinit var debitNoteRepo: DebitNoteRepo

    @Autowired
    private lateinit var writeoffService: WriteoffService

    @Autowired
    private lateinit var redisLockRegistry: RedisLockRegistry


    @Get("/getBySupplier")
    fun getCreditNotesBySupplierId(@RequestParam(required = true) supplierId: Long,
                                   @RequestParam(required = false) status: NoteStatus?,
                                   @RequestParam(required = false) type: NoteTypes?,
                                   @RequestParam(required = false) customerType: Boolean?,
                                   @RequestParam(required = true) tenant: String,
                                   @RequestParam(required = false) ds: String? = null): ResponseEntity<List<CreditNoteWithDebitNoteIds> > {


        val cn = creditNoteService.getCreditNotesForSupplier(supplierId, status, type,tenant,customerType?:false,ds) ?: throw RequestException("CreditNotes not found")
        return ResponseEntity.ok(cn)
    }

    @Get
    fun getCreditNotes(@RequestParam(required = false) page: Int?,
                       @RequestParam(required = false) size: Int?,
                       @RequestParam(required = false) supplierId: Long?,
                       @RequestParam(required = false) invoiceId: Long?,
                       @RequestParam(required = false) debitNoteId: Long?,
                       @RequestParam(required = false) creditNoteId: Long?,
                       @RequestParam(required = false) status: NoteStatus?,
                       @RequestParam(required = false) type: NoteTypes?,
                       @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) from: LocalDate?,
                       @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) to: LocalDate?,
                       @RequestParam(required = false) customerType: Boolean?,
                       @RequestParam(required = false) tenant: String?,
                       @RequestParam(required = false) ds: String? = null):
            PageableCreditnoteWithDebitNoteid {

        var tenantNew = tenant
        if (tenant.isNullOrEmpty()) tenantNew = null
        return creditNoteService.getCreditNotes(page, size, supplierId, invoiceId, debitNoteId, creditNoteId, status, type, from, to, tenantNew,customerType?:false,ds)
    }

    @Get("/vendor/{id}")
    fun getVendorCreditNotes(@RequestParam("page", required = false) page: Int?,
                             @RequestParam(required = false) size: Int?,
                             @PathVariable("id") id: Long,
                             @RequestParam(required = false) debitNoteNumber: String?,
                             @RequestParam(required = false) creditNoteNumber: String?,
                             @RequestParam(required = false) receiptStatus: ReceiptStatus?,
                             @RequestParam(required = false) status: NoteStatus?,
                             @RequestParam(required = false) type: NoteTypes?,
                             @RequestParam(required = false) source: String?,
                             @RequestParam(required = false) customerType: Boolean?,
                             @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) from: LocalDate?,
                             @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) to: LocalDate?,
                             @RequestParam(required = false) tenant: String,
                             @RequestParam(required = false) ds: String? = null,
                             @RequestParam(required = false) returnEventType: DebitNoteReturnEventType?,
                             @RequestParam(required = false) distributorId: Long?): PaginationDto {

        var sourceNew: String? = source
        if (source.isNullOrEmpty()) sourceNew = null
        var dnNum: String? = debitNoteNumber
        if (debitNoteNumber.isNullOrEmpty()) dnNum = null
        var cnNum: String? = creditNoteNumber
        if (creditNoteNumber.isNullOrEmpty()) cnNum = null
        return creditNoteService.getVendorCreditNotes(page, size, id, dnNum, cnNum, receiptStatus, status, type, sourceNew, from, to, tenant,customerType?:false,ds,returnEventType,distributorId)
    }

    @Post
    fun save(@RequestHeader("x-user") user: String,
             @RequestHeader(tenant_header) tenant: String,
             @RequestBody creditNote: CreditNote,
             @RequestParam(required = false) ds: String? = null): ResponseEntity<Any> {
        val key = "creditNote_"+ creditNote.debitNotes?.get(0)?.debitNoteNumber
        val lock = redisLockRegistry.obtain(key)
        if(lock.tryLock(1, TimeUnit.SECONDS)) {
            try{
                validate(creditNote)
                creditNote.remainingAmount = creditNote.amount
                return ResponseEntity.ok(creditNoteService.save(user, creditNote, tenant, PartnerType.VENDOR, ds))
            }finally {
                lock.unlock()
            }
        }else {
            throw RequestException("NOT_ABLE_TO_ACQUIRE_LOCK for debitnote number ${creditNote.debitNotes?.get(0)?.debitNoteNumber}")
        }
    }

    @Put("/{id}")
    fun update(@RequestHeader("x-user") user: String,
               @PathVariable("id") id: Long,
               @RequestBody creditNote: CreditNote): ResponseEntity<Any> {

        validate(creditNote)
        return ResponseEntity.ok(creditNoteService.update(user, id, creditNote))
    }

    fun validate(creditNote: CreditNote) {

        if (creditNote.supplierId == 0L)
            throw DetailedRequestException(BookkeeperErrors.MISSING_INPUT, arrayOf("Supplier Id"))

        if (creditNote.amount < BigDecimal(0))
            throw DetailedRequestException(BookkeeperErrors.INVALID_INPUT, arrayOf("Amount Receivable"))

        var amt = BigDecimal(0)
        creditNote.debitNotes?.forEach { debitNote ->
            amt += debitNote.amountReceivable
        }

        if(amt < creditNote.amount) {
            throw DetailedRequestException(BookkeeperErrors.INVALID_INPUT, arrayOf("Credit note amount more than debit notes amount"))
        }
        val debitNoteIds = creditNote.debitNotes?.map { it.id }
        if(debitNoteIds.isNullOrEmpty()) return
        val debitNotes = debitNoteRepo.findAllById(debitNoteIds).filter { it.creditNoteId != null }

        if(debitNotes.isNotEmpty()) {
            throw DetailedRequestException(BookkeeperErrors.ERROR, arrayOf("Credit Note for selected debit notes are already created"))
        }

    }

    @Get("/vendor/all")
    fun getVendorCreditNoteData(@RequestParam("page", required = false) page: Int?,
                                @RequestParam(required = false) size: Int?,
                                @RequestParam("partnerIds") partnerIds: List<Long>?,
                                @RequestParam(required = false) partnerId: Long?,
                                @RequestParam(required = false) partnerDetailId: Long?,
                                @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) startAt: LocalDate?,
                                @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) endAt: LocalDate?,
                                @RequestParam("noteType") noteType: NoteTypes?,
                                @RequestParam(required = false) customerType: Boolean?,
                                @RequestParam(required = false) onClick: Boolean?,
                                @RequestParam("tenant") tenant: String,
                                @RequestParam(required = false) ds: String? = null,
                                @RequestParam(required = false) firmType: List<Int>?,
                                @RequestParam("client") client: List<InvoiceType>): PaginationDto {
        return creditNoteService.getVendorCreditNotesData(partnerIds, partnerId,partnerDetailId, startAt, endAt, noteType, tenant, page, size,customerType?:false,ds,onClick,firmType,client)
    }

    @Get("/vendor/all/aggregated")
    fun getAggregatedCreditNoteData(@RequestParam("partnerIds") partnerIds: List<Long>?,
                                    @RequestParam(required = false) partnerId: Long?,
                                    @RequestParam(required = false) partnerDetailId: Long?,
                                    @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) startAt: LocalDate?,
                                    @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) endAt: LocalDate?,
                                    @RequestParam("noteType") noteType: NoteTypes?,
                                    @RequestParam(required = false) customerType: Boolean?,
                                    @RequestParam("tenant") tenant: String,
                                    @RequestParam(required = false) ds: String? = null,
                                    @RequestParam(required = false) firmType: List<Int>?,
                                    @RequestParam("client") client: List<InvoiceType>): AggregatedCreditNoteDataDto {
        return creditNoteService.getAggregatedCreditNoteData(partnerIds, partnerId,partnerDetailId, startAt, endAt, noteType, tenant,customerType?:false,ds,firmType,client)
    }



    @RequestMapping("/url", method = arrayOf(RequestMethod.GET), produces = arrayOf(MediaType.APPLICATION_JSON_VALUE))
    fun getCreditNoteSummary(
            @RequestParam(required = true) tenant: String,
            @RequestParam(required = false) customerType: Boolean?,
            @RequestParam(required = true) createdBy: String,
            @RequestParam(required = false) ds: String? = null,
            @RequestParam(required = false) firmType: List<Int>?,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) startAt: LocalDate?,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) endAt: LocalDate?,
            @RequestParam("client") client: List<InvoiceType>
            ): CreateResultData {
        return creditNoteService.getCreditNoteURL(tenant,createdBy,customerType?:false,ds,firmType,startAt,endAt,client)
    }

    @RequestMapping("/download", method = arrayOf(RequestMethod.GET), produces = arrayOf(MediaType.APPLICATION_JSON_VALUE))
    fun creditNoteSummaryDownload(@RequestParam(required = true) createdBy: String,
                                  @RequestParam(required = false) customerType: Boolean?,
                                  @RequestParam(required = true) tenant: String,
                                  @RequestParam(required = false) ds: String? = null): CreateResultData {
    return creditNoteService.getCreditNoteDownload(createdBy,tenant, customerType?:false,ds)
    }


    @RequestMapping("/detail/url", method = arrayOf(RequestMethod.GET), produces = arrayOf(MediaType.APPLICATION_JSON_VALUE))
    fun getCreditNoteDetailSummary(
            @RequestParam(required = true) supplierId: Long,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) from: LocalDate?,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) to: LocalDate?,
            @RequestParam(required = true) tenant: String,
            @RequestParam(required = false) customerType: Boolean?,
            @RequestParam(required = false) status: NoteStatus?,
            @RequestParam(required = true) createdBy: String,
            @RequestParam(required = false) ds: String? = null): CreateResultData {
        return creditNoteService.getCreditNoteDetailURL(supplierId, tenant, createdBy, from, to,status,customerType?:false,ds)
    }

    @RequestMapping("/detail/download", method = arrayOf(RequestMethod.GET), produces = arrayOf(MediaType.APPLICATION_JSON_VALUE))
    fun creditNoteDetailSummaryDownload(@RequestParam(required = true) createdBy: String,
                                        @RequestParam(required = false) customerType: Boolean?,
                                        @RequestParam(required = true) tenant: String,
                                        @RequestParam(required = false) ds: String? = null): CreateResultData {
        return creditNoteService.getCreditNoteDetailDownload(createdBy,tenant, customerType?:false,ds)
    }

    @Post("/{id}/receipt")
    fun updateCreditNoteReceiptStatus(@RequestHeader("x-user") user: String,
                                      @PathVariable("id") id: Long,
                                      @RequestBody creditNoteReceiptDto: CreditNoteReceiptDto) =
            creditNoteService.updateCreditNoteReceiptStatus(user, id, creditNoteReceiptDto)

    @Post("/discount")
    fun createDiscountCreditNoteEntry(@RequestParam("user") user: String,
                                      @RequestBody discountCreditNoteDto: DiscountCreditNoteDto,
                                      @RequestParam(required = false) ds: String? = null): DraftCreditNote {

        return creditNoteService.createDiscountCreditNoteEntry(user, discountCreditNoteDto,ds)
    }

    @Put("/discount/{id}")
    fun updateDraftCreditNote(@PathVariable("id") id: Long,
                              @RequestParam("user") user: String,
                              @RequestParam("userName") userName: String,
                              @RequestParam("tenant") tenant: String,
                              @RequestBody draftCreditNote: DraftCreditNote,
                              @RequestParam(required = false) ds: String? = null)
            = creditNoteService.updateDraftCreditNote(draftCreditNote, user, userName, tenant,ds)

    @Get("/direct")
    fun getDirectCreditNotes(@RequestParam("page", required = false) page: Int?,
                             @RequestParam(required = false) size: Int?,
                             @RequestParam(required = false) creditNoteNumber: String?,
                             @RequestParam(required = false) partnerId: Long?,
                             @RequestParam(required = false) draftId: Long?,
                             @RequestParam(required = false) documentType: DocumentType?,
                             @RequestParam(required = false) partnerType: PartnerType?,
                             @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) transactionFrom: LocalDate?,
                             @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) transactionTo: LocalDate?,
                             @RequestParam(required = false) createdBy: String?,
                             @RequestParam(required = false) pendingFor: String?,
                             @RequestParam(required = false) updatedBy: String?,
                             @RequestParam(required = false) statuses: MutableList<String?>?,
                             @RequestParam tenant: String,
                             @RequestParam(required = false) ds: String? = null,
                             @RequestParam(required = false)partnerDetailId: Long?): PaginationDto {

        var cnNum: String? = creditNoteNumber
        if (creditNoteNumber.isNullOrEmpty()) cnNum = null

        var updBy: String? = updatedBy
        if (updatedBy.isNullOrEmpty()) updBy = null
//        var pName: String? = partnerName
//        if (partnerName.isNullOrEmpty()) pName = null

        var crBy: String? = createdBy
        if (createdBy.isNullOrEmpty()) crBy = null

        var pFor: String? = pendingFor
        if (pendingFor.isNullOrEmpty()) pFor = null



        return creditNoteService.getDirectCreditNotes(page, size, cnNum, partnerId,  documentType, draftId, partnerType,
                transactionFrom, transactionTo, crBy, pFor, updBy, tenant, statuses,ds,partnerDetailId)
    }

    @Put("/discount/{id}/status")
    fun updateDraftCreditNoteStatus(@RequestParam("user") user: String,
                                    @PathVariable("id") id: Long,
                                    @RequestParam("tenant") tenant: String,
                                    @RequestParam("status") status: Status,
                                    @RequestParam("userName") userName: String,
                                    @RequestParam("checkerRemarks") checkerRemarks: String?,
                                    @RequestParam(required = false) ds: String? = null,
                                    @RequestParam(required = false) cnWithSettleInvoice: Boolean?)
            = creditNoteService.updateDraftCreditNoteStatus(user, id, tenant, status, userName, checkerRemarks, ds,cnWithSettleInvoice?:true )


    @Get("/discount/item/{id}/count")
    fun getDiscountQuantity(@PathVariable("id") id: Long)
            = creditNoteService.getDiscountQuantity(id)

    @Get("/discount")
    fun getCreditNoteInfo(@RequestParam("creditNoteNumber") creditNoteNumber: String,
                          @RequestParam("tenant") tenant: String) = creditNoteService.getCreditNoteInfo(creditNoteNumber,tenant)

    @Get("/printCreditNote")
    fun printCreditNotePdf(@RequestParam("creditnoteNo") creditnoteNo: String,
                           @RequestParam("tenant") tenant: String,
                           @RequestParam("ds") ds: String? = null): SaleReturnCreditnoteDto = creditNoteService.getCreditNoteDataForPdf(creditnoteNo,tenant,ds)

    @Post("/nsr")
    fun createNsrCreditNoteEntry(@RequestParam("user") user: String,
                                 @RequestBody draftNsrCreditNoteDto: DraftNsrCreditNoteDto,
                                 @RequestParam("tenant") tenant: String,
                                 @RequestParam("oldId") oldId: Long?,
                                 @RequestParam(required = false) ds: String? = null)
            = creditNoteService.createNSRCreditNoteEntry(user, draftNsrCreditNoteDto, tenant, oldId,ds)

    @Get("/nsr")
    fun getNsrEntries(@RequestParam("page", required = false) page: Int?,
                      @RequestParam(required = false) size: Int?,
                      @RequestParam(required = false) creditNoteNumber: String?,
                      @RequestParam(required = false) partnerId: Long?,
                      @RequestParam(required = false) draftId: Long?,
                      @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) approvedFrom: LocalDate?,
                      @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) approvedTo: LocalDate?,
                      @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) createdOnFrom: LocalDate?,
                      @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) createdOnTo: LocalDate?,
                      @RequestParam(required = false) createdBy: String?,
                      @RequestParam(required = false) pendingFor: String?,
                      @RequestParam(required = false) updatedBy: String?,
                      @RequestParam(required = false) statuses: MutableList<String?>?,
                      @RequestParam tenant: String,
                      @RequestParam(required = false) ds: String? = null): PaginationDto {

        log.debug("inside getNsrEntries: $statuses")

        var cnNum: String? = creditNoteNumber
        if (creditNoteNumber.isNullOrEmpty()) cnNum = null

        var updBy: String? = updatedBy
        if (updatedBy.isNullOrEmpty()) updBy = null

        var crBy: String? = createdBy
        if (createdBy.isNullOrEmpty()) crBy = null

        var pFor: String? = pendingFor
        if (pendingFor.isNullOrEmpty()) pFor = null

        return creditNoteService.getNsrEntries(page, size, cnNum, partnerId, draftId, approvedFrom, approvedTo,
                createdOnFrom, createdOnTo, crBy, pFor, updBy, tenant, statuses,ds)
    }

    @Put("/nsr")
    fun updateDraftNsr(@RequestParam("user") user: String,
                       @RequestParam("tenant") tenant: String,
                       @RequestParam("userName") userName: String,
                       @RequestBody draftNsr: DraftNsr,
                       @RequestParam(required = false) ds: String? = null)
            = creditNoteService.updateDraftNsr(user, draftNsr, tenant, userName,ds)

    @Post("/nsr/{id}/status")
    fun updateDraftNsrStatus(@PathVariable("id") id: Long,
                             @RequestParam("user") user: String,
                             @RequestParam("userName") userName: String,
                             @RequestParam("status") status: Status,@RequestParam(required = false) ds: String? = null)
            = creditNoteService.updateDraftNsrEntryStatusInternal(id, status, user, userName,ds)

    @Post("/nsr/external/{draftId}/status")
    fun updateDraftNsrStatusExternal(@PathVariable("draftId") draftId: Long,
                                     @RequestParam("user") user: String,
                                     @RequestParam("status") status: Status,
                                     @RequestParam("tenant") tenant: String,
                                     @RequestParam(required = false) ds: String? = null)
            = creditNoteService.updateDraftNsrEntryStatusExternal(draftId, status, user, tenant,ds)

    @Get("/nsr/pdf")
    fun getNsrCreditNotePdf(@RequestParam("creditNoteNumber") creditNoteNumber: String) = creditNoteService.getNsrCreditNotePdf(creditNoteNumber)

    @Put("/discount/change/checker/{id}")
    fun changeDiscountCNChecker(@PathVariable("id") id: Long,
                      @RequestParam(required = true) tenant: String,
                      @RequestParam(required = true ) checkerId: Long,
                      @RequestParam(required = true)  userId: String,
                      @RequestParam(required = true)  userName: String,
                      @RequestParam(required = false) ds: String? = null): Result {
        return creditNoteService.updateDiscountCNChecker(id, checkerId, tenant,userId,userName,ds)
    }

    @Put("/nsr/change/checker/{id}")
    fun changeNSRChecker(@PathVariable("id") id: Long,
                      @RequestParam(required = true) tenant: String,
                      @RequestParam(required = true ) checkerId: Long,
                      @RequestParam(required = true)  userId: String,
                      @RequestParam(required = true)  userName: String,
                      @RequestParam(required = false) ds: String? = null): Result {
        return creditNoteService.updateNSRChecker(id, checkerId, tenant,userId,userName,ds)
    }

    @PostMapping("/einvoice/generate")
    fun createEinvoiceForCN(@RequestParam(required = true) creditNoteNumber: String,@RequestParam(required = true) tenant: String): Result {
        return creditNoteService.generateEinvoice(creditNoteNumber,tenant)
    }

    @Get("sdcn")
    fun getSlabDiscountCreditNoteInfo(@RequestParam creditNoteNumber: String,
                                      @RequestParam tenant: String,
                                      @RequestParam(required = false) ds: String? = null): SlabDiscountCreditNoteDto {
        return slabCreditNoteService.getSlabDiscountCreditNoteInfo(creditNoteNumber, tenant, ds)
    }

    @Deprecated("Use reserveCNsAdvancePayments instead")
    @Post("/reserve")
    fun reserveCreditNotes(@RequestBody creditNoteReservationDTO: CreditNoteReservationPayloadDTO): HashMap<String, List<CreditNoteReservationDTO>?> {
        return hashMapOf("creditNotes" to creditNoteService.reserveCreditNoteForExternalOrderId(creditNoteReservationDTO))
    }

    @Get("/{externalOrderId}/reservedCNs")
    fun getReservedCreditNotes(@PathVariable("externalOrderId") externalOrderId: String, @RequestParam("orderType") orderType: OrderType): HashMap<String, List<CreditNoteReservationDTO>?> {
        return hashMapOf("creditNotes" to creditNoteReservationRepo.findByExternalOrderIdAndStatuses(externalOrderId, orderType, mutableListOf(CreditNoteReservationStatus.RESERVED, CreditNoteReservationStatus.COMPLETED)))
    }

    @Post("/releaseCNs")
    fun releaseUnusedCNs() : Result{
        creditNoteService.releaseReservedCNs()
        return success
    }

    @Get("fdcn")
    fun getFlatDiscountCreditNoteInfo(@RequestParam creditNoteNumber: String,
                                      @RequestParam tenant: String,
                                      @RequestParam(required = false) ds: String? = null): FlatDiscountCreditNotePrintDto {
        return creditNoteService.getFlatDiscountCreditNoteInfo(creditNoteNumber, tenant, ds)
    }

    @PostMapping("/cancelCNs")
    fun cancelCreditNote(@RequestBody creditNoteWritOffDataDto: CreditNoteWritOffDataDto, @RequestParam updatedBy: String, @RequestParam tenant: String,@RequestParam(required = false) ds: String? = null): Result {
        writeoffService.cancelCreditNote(creditNoteWritOffDataDto, updatedBy, tenant,ds)
        return success
    }

    @GetMapping("/cancelCNsReport")
    fun getCancelCreditReport(@RequestParam("page", required = false) page: Int?, @RequestParam("size", required = false)size: Int?, @RequestParam tenant: String, @RequestParam from: LocalDateTime?, @RequestParam to: LocalDateTime?, @RequestParam creditNoteNumber: String?, @RequestParam partnerId: Long?, @RequestParam partnerDetailId: Long?): MutableList<CancelCreditReportDto>{
        return writeoffService.getCancelCreditReport(tenant, from, to , creditNoteNumber, partnerId, partnerDetailId)
    }

    // For RIO platform one roof
    @Post("/reserveCNAdvPayment")
    fun reserveCNsAdvancePayments(@RequestBody creditNoteReservationV2DTO: CreditNoteReservationV2PayloadDTO): CNAdvPaymentReservationDTO {
        var cnReservedAmount = BigDecimal.ZERO
        val cnReservationInfoDTO = mutableListOf<ReservationEntityInfoDTO>()
        val advPaymentReservedAmount = BigDecimal.ZERO
        val advPaymentReservationInfoDTO = mutableListOf<ReservationEntityInfoDTO>()

        val cnReservationInfo: List<CreditNoteReservationDTO>?
        try {
            cnReservationInfo =  creditNoteService.reserveCreditNoteV2ForExternalOrderId(creditNoteReservationV2DTO)
        }
        catch (e: Exception) {
            log.error("reserveCNsAdvancePayments : Error wit: ${e}", e)
            throw RequestException("reserveCNsAdvancePayments : Error wit: ${e.message}")
        }

        cnReservationInfo?.forEach { cnInfo ->
            cnReservedAmount += cnInfo.amountUsed
            cnReservationInfoDTO.add(
                ReservationEntityInfoDTO(
                    cnInfo.creditNoteNumber,
                    cnInfo.amountUsed,
                    cnInfo.creditNoteAmount,
                    cnInfo.creditNoteType.toString()
                )
            )
        }
        // deduct the CN reserved amount and pass the remaining amount to advance payment process
        return CNAdvPaymentReservationDTO(
            cnReservedAmount, cnReservationInfoDTO, advPaymentReservedAmount, advPaymentReservationInfoDTO
        )
    }

    @Get("/v2/{externalOrderId}/reservedCNs")
    fun getReservedCreditNotesV2(@PathVariable("externalOrderId") externalOrderId: String, @RequestParam("distributorPdi") distributorPdi: Long): CNAdvPaymentReservationDTO {

        var cnReservedAmount = BigDecimal.ZERO
        var cnReservationInfoDTO = mutableListOf<ReservationEntityInfoDTO>()
        var advPaymentReservedAmount = BigDecimal.ZERO
        var advPaymentReservationInfoDTO = mutableListOf<ReservationEntityInfoDTO>()
        var cnReservationInfo = creditNoteReservationRepo.findByExternalOrderIdAndStatuses(externalOrderId, distributorPdi, mutableListOf(CreditNoteReservationStatus.RESERVED, CreditNoteReservationStatus.COMPLETED))
        cnReservationInfo?.forEach { cnInfo ->
            cnReservedAmount += cnInfo.amountUsed
            cnReservationInfoDTO.add(
                ReservationEntityInfoDTO(
                    cnInfo.creditNoteNumber,
                    cnInfo.amountUsed,
                    cnInfo.creditNoteAmount,
                    cnInfo.creditNoteType.toString()
                )
            )
        }
        return CNAdvPaymentReservationDTO(
            cnReservedAmount, cnReservationInfoDTO, advPaymentReservedAmount, advPaymentReservationInfoDTO)
//        return hashMapOf("creditNotes" to creditNoteReservationRepo.findByExternalOrderIdAndStatuses(externalOrderId, orderType, mutableListOf(CreditNoteReservationStatus.RESERVED, CreditNoteReservationStatus.COMPLETED)))
    }

    @PostMapping("/retry/rio/pdf")
    fun createRioPdf(@RequestBody(required = true) creditNoteNumbers: MutableList<String>): Result {
        return creditNoteService.generateRioCnPdf(creditNoteNumbers)
    }

    @GetMapping("/orders")
    fun getDataforPaperServicePdf(@RequestParam(required = true) orderId: String): List<CreditNoteNumberAndTypeMappingDTO>{
        return creditNoteService.getCreditNoteByOrderId(orderId)
    }

    @GetMapping("/nsrPdfData")
    fun getNsrCreditNoteData(@RequestParam creditNoteNumber: String): NsrCreditNotePrintDto {
        return creditNoteService.getNsrCreditNoteData(creditNoteNumber)
    }
}
