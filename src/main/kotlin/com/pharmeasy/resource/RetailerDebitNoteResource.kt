package com.pharmeasy.resource

import com.pharmeasy.annotation.Get
import com.pharmeasy.model.*
import com.pharmeasy.service.RetailerDebitNoteService
import com.pharmeasy.type.DnType
import com.pharmeasy.type.Status
import com.pharmeasy.type.UseCaseType
import com.pharmeasy.util.name_attr
import com.pharmeasy.util.tenant_attr
import com.pharmeasy.util.user_attr
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import java.time.LocalDate
import java.time.LocalDateTime


@RestController
@RequestMapping("/debitnote/retailer")
class RetailerDebitNoteResource {
    companion object {
        private val log = LoggerFactory.getLogger(RetailerDebitNoteResource::class.java)
    }

    @Autowired
    private lateinit var retailerDebitNoteService: RetailerDebitNoteService

    @Get("/{pdi}")
    fun getRetailerDebitNotes(@RequestParam("page", required = false) page: Int?,
                            @RequestParam(required = false) size: Int?,
                            @PathVariable("pdi") pdi: Long,
                            @RequestParam(required = false) documentNumber: String?,
                            @RequestParam(required = false) status: InvoiceStatus?,
                            @RequestParam(required = false) dnType: DnType?,
                            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) from: LocalDate,
                            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) to: LocalDate,
                            @RequestParam(required = true) tenant: String,
                            @RequestParam(required = false) ds: String?,
                            @RequestParam(required = false) distributorId: Long?): PaginationDto {

        return retailerDebitNoteService.getRetailerDebitNotesByPdi(page, size, pdi, documentNumber,from,to, dnType, tenant,ds,status,distributorId)
    }


    @Get("/all")
    fun getDebitNoteData(@RequestParam("page", required = false) page: Int?,
                               @RequestParam(required = false) size: Int?,
                               @RequestParam("partnerDetailIds") partnerDetailIds: List<Long>?,
                               @RequestParam(required = false) partnerDetailId: Long?,
                               @RequestParam(required = true) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) from: LocalDate,
                               @RequestParam(required = true) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) to: LocalDate,
                               @RequestParam("dnType") dnType: DnType?,
                               @RequestParam("status") status: InvoiceStatus?,
                               @RequestParam("tenant") tenant: String,
                               @RequestParam(required = false) ds: String?,
                               @RequestParam(required = false) distributorId: Long?): PaginationDto {
        return retailerDebitNoteService.getAllRetailerDebitNotesData(partnerDetailIds, partnerDetailId,from, to, dnType, tenant, page, size,status,ds,distributorId)
    }

    @Get("/{id}/url")
    fun getDebitNotePdf( @PathVariable("id") id: Long): FileUrl{
        return retailerDebitNoteService.generateRetailerDebitUrl(id)
    }

    @PostMapping
    fun createRetailerDebitNote(@RequestAttribute(name_attr) userName: String,
                                @RequestAttribute(tenant_attr) tenant: String,
                                @RequestBody retailerDebitNoteDto: List<RetailerDebitNoteDto>,
                                @RequestParam(required = false) ds: String?=null): List<RetailerDebitNoteDto>{
        return retailerDebitNoteService.createRetailerDebitNotes(userName,
            retailerDebitNoteDto, ds?:tenant)
    }

    @PostMapping("/{id}/checker-request")
    fun processCheckerRequest(@PathVariable("id")id: Long, @RequestParam user:String,
                              @RequestParam action: Status, @RequestAttribute(user_attr) userId: String){
        retailerDebitNoteService.processCheckerRequest(id, user, action, userId)
    }

    @GetMapping("/get-adhoc-dn")
    fun getAdhocDnRequests(@RequestParam tenant: String,
                           @RequestParam(required = false) partnerDetailId: Long?,
                           @RequestParam(required = false) fromDate: LocalDate?,
                           @RequestParam(required = false) toDate: LocalDate?,
                           @RequestParam(required = false) useCaseType: UseCaseType?,
                           @RequestParam(required = false) status: List<InvoiceStatus>,
                           @RequestParam(required = false) page: Int?,
                           @RequestParam(required = false) size: Int?,
                           @RequestParam(required = false) ds: String?,
                           @RequestParam(required = false) distributorId: Long?): PaginationDto{
        return retailerDebitNoteService.getAdhocDnRequests(tenant, partnerDetailId, fromDate,toDate, useCaseType, status, page, size, ds, distributorId)
    }

    @Get("/debitNoteNo/{logisticsPackageId}")
    fun getDebitNoteNuber(@PathVariable("logisticsPackageId") logisticsPackageId: String): List<String>{
        return retailerDebitNoteService.getDebitNoteNumber(logisticsPackageId)
    }

    @Get("/listOfDebitNoteNo")
    fun getListOfDebitNo(@RequestBody packageIdList : List<String>): Map<String, List<String>>{
        return retailerDebitNoteService.getListOfDebitNo(packageIdList)
    }

    @Get("/debitNoteUrls/{logisticsPackageId}")
    fun getDebitNoteUrls(@PathVariable("logisticsPackageId") logisticsPackageId: String): UrlsDTO?{
        return retailerDebitNoteService.getDebitNoteUrls(logisticsPackageId)
    }

    @PutMapping("/retryEWayBill")
    fun retryEWayBill(@RequestBody eWayBillGenericDto: EWayBillGenericDto): EwayBillV2?{
        return retailerDebitNoteService.createEWayBill(eWayBillGenericDto)
    }

    @Get("/details")
    fun getAllRetailerDebitNotesByPdi(@RequestParam("page", required = false) page: Int?,
                              @RequestParam(required = false) size: Int?,
                              @RequestParam(required = false) pdi: Long,
                              @RequestParam(required = false) documentNumber: String?,
                              @RequestParam(required = false) status: InvoiceStatus?,
                              @RequestParam(required = false) dnType: DnType?,
                              @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) from: LocalDate,
                              @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) to: LocalDate,
                              @RequestParam(required = true) tenant: String,
                              @RequestParam(required = false) ds: String?,
                              @RequestParam(required = false) distributorId: Long?): PaginationDto {



        return retailerDebitNoteService.getRetailerDebitNotesByPdi(page, size, pdi, documentNumber,from,to, dnType, tenant,ds,status,distributorId)
    }

}