package com.pharmeasy.resource

import com.pharmeasy.annotation.Put
import com.pharmeasy.data.FlatDiscountTask
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.*
import com.pharmeasy.repo.FlatDiscountTaskRepo
import com.pharmeasy.service.*
import com.pharmeasy.type.*
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.web.bind.annotation.*
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.concurrent.Executors

@RestController
@RequestMapping("creditnote/bulk")
class FlatDiscountTaskResource {

    companion object {
        private val log = LoggerFactory.getLogger(FlatDiscountTaskResource::class.java)
    }

    @Value("\${app.aws.s3.bucket}")
    private lateinit var bucket: String

    @Autowired
    private lateinit var flatDiscountService: FlatDiscountService

    @Autowired
    private lateinit var s3Uploader: S3Uploader

    @GetMapping("uploadUrl")
    fun getFileUploadUrl(): UrlDataDTO {
        val filename = "creditnote/${LocalDate.now().format(DateTimeFormatter.ISO_DATE)}/discountcreditnote_${System.currentTimeMillis()}.csv"
        return UrlDataDTO(s3Uploader.preSignedURL(bucket,filename), filename)
    }


    @PostMapping("upload")
    fun uploadCreditNoteFile(@RequestBody slabFileDto: SlabFileDto, @RequestParam tenant: String,
                             @RequestParam user: String,@RequestParam userId: String,
                             @RequestParam cnType: NoteTypes,@RequestParam(required = false) ds: String? = null): PaginationDto {
        return flatDiscountService.uploadCreditNoteFile(slabFileDto, tenant, user, userId, cnType, ds)
    }


    @GetMapping("detail/{taskId}")
    fun getTaskItems(@PathVariable taskId: Long,
                               @RequestParam(required = false) page: Int?,
                               @RequestParam(required = false) size: Int?): PaginationDto {
        return flatDiscountService.getTaskDetails(taskId,page,size)
    }

    @PostMapping("cancel/{taskId}")
    fun cancelCreditNoteTask(@PathVariable taskId: Long, @RequestParam tenant: String): PaginationDto {
        return flatDiscountService.cancelTask(taskId, tenant)
    }

    @GetMapping("task")
    fun getDiscountTasks(@RequestParam(required = false) from: LocalDate? = null,
                           @RequestParam(required = false) to: LocalDate? = null,
                           @RequestParam(required = false) status: SlabTaskStatus? = null,
                           @RequestParam tenant: String,
                           @RequestParam(required = false) ds: String? = null,
                           @RequestParam(required = false) page: Int?,
                           @RequestParam(required = false) size: Int?): PaginationDto {
        val fromDateTime = from?.atTime(0,0,0)
        val toDateTime = to?.atTime(23,59,59)

        return flatDiscountService.getTasks(fromDateTime, toDateTime, status, ds, tenant, page, size)
    }
    @PostMapping("{taskId}/approve")
    fun approveTask(@PathVariable taskId: Long, @RequestParam user: String, @RequestParam userId: String, @RequestParam(required = false) ds: String? = null): Result {
        flatDiscountService.approveTaskByTaskId(taskId, user, userId, ds)
        return Result(200, "success")
    }

    @PostMapping("{taskId}/reject")
    fun rejectTask(@PathVariable taskId: Long, @RequestParam userId: String): Result {
        flatDiscountService.rejectTask(taskId, userId)
        return Result(200, "success")
    }


    @Put("/change/checker/{id}")
    fun changeChecker(@PathVariable("id") id: Long,
                      @RequestParam(required = true) tenant: String,
                      @RequestParam(required = true ) checkerId: Long,
                      @RequestParam(required = true)  userId: String,
                      @RequestParam(required = false) ds: String? = null): Result {
        return flatDiscountService.updateCreditNoteChecker(id, checkerId, tenant,userId,ds)
    }
}