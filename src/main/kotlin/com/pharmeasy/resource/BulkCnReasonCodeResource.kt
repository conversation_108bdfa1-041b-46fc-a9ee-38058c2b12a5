package com.pharmeasy.resource

import com.pharmeasy.model.BulkCnReasonCodeDto
import com.pharmeasy.model.PaginationDto
import com.pharmeasy.service.BulkCnReasonCodeMappingService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController


@RestController
@RequestMapping("/bulkCnReasonCode")
class BulkCnReasonCodeResource {
    @Autowired
    private lateinit var bulkCnReasonCodeMappingService: BulkCnReasonCodeMappingService
    @PostMapping
    fun createBulkCNReasonCodeMapping(@RequestBody bulkCnReasonCodeDto: BulkCnReasonCodeDto,
                                      @RequestParam user: String){
        bulkCnReasonCodeMappingService.createBulkCNReasonCodeMapping(bulkCnReasonCodeDto, user)
    }

    @GetMapping
    fun getAllBulkCNReasonCodeMapping(@RequestParam page: Int?, @RequestParam size: Int?): PaginationDto{
        return bulkCnReasonCodeMappingService.getAllBulkCNReasonCodeMapping(page, size)
    }
}