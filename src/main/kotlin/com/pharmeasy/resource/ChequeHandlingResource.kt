package com.pharmeasy.resource

import com.pharmeasy.annotation.Get
import com.pharmeasy.annotation.Post
import com.pharmeasy.annotation.Put
import com.pharmeasy.data.ChequeHandle
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.*
import com.pharmeasy.proxy.SupplierProxy
import com.pharmeasy.repo.ChequeHandlingRepo
import com.pharmeasy.service.ChequeHandlingService
import com.pharmeasy.service.CompanyService
import com.pharmeasy.type.ChequeHandleType
import com.pharmeasy.util.email_attr
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.web.bind.annotation.*
import java.time.LocalDate

@RestController
@RequestMapping("/cheque")
class ChequeHandlingResource{

    companion object {
        private val log = LoggerFactory.getLogger(ChequeHandlingResource::class.java)
    }

    @Autowired
    private lateinit var chequeHandlingRepo: ChequeHandlingRepo

    @Autowired
    private lateinit var chequeHandlingService: ChequeHandlingService

    @Autowired
    private lateinit var companyService: CompanyService

    @Autowired
    private lateinit var supplierProxy: SupplierProxy

    @Get("/handle/all")
    fun getChequeHandling(
            @RequestParam("page", required = false) page: Int?,
            @RequestParam(required = false) size: Int?,
            @RequestParam(required = false) chequeNum: String?,
            @RequestParam(required = false) settlementNum: String?,
            @RequestParam(required = false) advancePaymentNumber: String?,
            @RequestParam(required = false) status: ChequeHandleType?,
            @RequestParam(required = false) bankName: String?,
            @RequestParam("partnerIds", required = false) partnerIds: List<Long>?,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) from: LocalDate?,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) to: LocalDate?,
            @RequestParam(required = false) ds: String? = null,
            @RequestParam partnerDetailId: Long?,
            @RequestParam(required = true) tenant: String,
            @RequestParam(required = false) distributorId: Long?) : PaginationDto {

        return chequeHandlingService.getChequeData(page, size, tenant, ds, partnerIds, chequeNum, settlementNum, status, bankName, from, to, partnerDetailId,advancePaymentNumber,distributorId)

    }

    @Put("/update/status")
    fun updateChequeStatus(@RequestBody chequeUpdateDto: ChequeUpdateDto,
                           @RequestParam(required = true) tenant: String,
                           @RequestParam(required = false) ds: String? = null,
                           @RequestParam email: String): ChequeHandle?{

        // update cheque status
        chequeUpdateDto.validate()
        return chequeHandlingService.updateChequeStatus(chequeUpdateDto,tenant,email,ds)
    }

    @Get("/handle/all/aggregated")
    fun getAggregatedChequeData(@RequestParam(required = true) tenant: String,
                                @RequestParam(required = false) ds: String? = null,
                                @RequestParam(required = false) partnerDetailId: Long?): AggregatedChequeHandleDto {
        return chequeHandlingService.getAggregatedChequeData(tenant,ds,partnerDetailId)
    }
    @Put("/approve/bounce/{id}")
    fun acceptBouncedStatus(@PathVariable("id") id: Long,
                            @RequestParam(required = true) tenant: String,
                            @RequestParam(required = true) userId: String,
                            @RequestParam(required = true) change: Boolean,
                            @RequestParam(required = false) ds: String? = null): Any?{
        return chequeHandlingService.checkerStatusChange(userId, change,id,tenant,ds)
    }

    @Put("/change/checker/{id}")
    fun changeChecker(@PathVariable("id") id: Long,
                      @RequestParam(required = true) tenant: String,
                      @RequestParam(required = true ) checkerId: Long,
                      @RequestParam(required = true)  userId: String,
                      @RequestParam(required = false) ds: String? = null):Result {
        return chequeHandlingService.updateChequeChecker(id, checkerId, tenant,userId,ds)
    }

    @Get("/bounce/all/aggregated")
    fun getAllPastBounceDate(@RequestParam(required = true) tenant: String,
                                @RequestParam(required = true) partnerId: Long ): Long {
        return chequeHandlingService.getTotalBounceNumber(tenant,partnerId)
    }

    @Get("/maxdatelimit")
    fun getPostDatedChequeMaxLimit(@RequestParam(required = true) pdi: Long): Int {

        var partnerInfo = supplierProxy.partnerGenericDto(mutableListOf(pdi))?:
        throw RequestException("No partner details found for pdi $pdi !")
        return partnerInfo.data?.get(0)?.accounts?.distributor?.postDatedChequeMaxLimit ?:
            throw RequestException("No maxDateLimit found for pdi $pdi !")
    }


    @Get("/download")
    fun getChequeDownload(@RequestParam(required = true) tenant: String,
                          @RequestParam fromDate:LocalDate,
                          @RequestParam toDate:LocalDate,
                          @RequestParam status: ChequeHandleType?,
                          @RequestParam(required = false) ds: String? = null) : CreateResultData{
        return chequeHandlingService.getChequeUrl(tenant,ds,fromDate,toDate,status)
    }
    @Post("/update/status/bulk")
    fun updateBulkChequeStatus(@RequestBody chequeUpdateDto: ChequeHandleStatusChangeDto): Result{
        return chequeHandlingService.updateBulkChequeStatus(chequeUpdateDto)
    }
}