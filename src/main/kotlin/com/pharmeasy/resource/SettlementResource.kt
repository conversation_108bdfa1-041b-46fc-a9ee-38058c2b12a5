package com.pharmeasy.resource

import com.pharmeasy.annotation.Get
import com.pharmeasy.annotation.Post
import com.pharmeasy.annotation.Put
import com.pharmeasy.data.BkBank
import com.pharmeasy.data.BkInvoice
import com.pharmeasy.data.Settlement
import com.pharmeasy.exception.BookkeeperErrors
import com.pharmeasy.exception.DetailedRequestException
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.*
import com.pharmeasy.repo.BkInvoiceRepo
import com.pharmeasy.service.InvoiceService
import com.pharmeasy.service.SettlementService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.http.ResponseEntity
import org.springframework.integration.redis.util.RedisLockRegistry
import org.springframework.web.bind.annotation.*
import java.time.LocalDate
import java.util.concurrent.TimeUnit


@RestController
@RequestMapping("/settlement")
class SettlementResource {
    companion object {
        private val log = LoggerFactory.getLogger(CreditNoteResource::class.java)
    }

    @Autowired
    private lateinit var settlementService: SettlementService

    @Autowired
    private lateinit var bkInvoiceRepo: BkInvoiceRepo

    @Autowired
    private lateinit var invoiceService: InvoiceService

    @Autowired
    private lateinit var redisLockRegistry: RedisLockRegistry

    @Get("/getBkInvoices")
    fun getBkInvoices(@RequestParam("page", required = false) page: Int?,
                      @RequestParam(required = false) size: Int?,
                      @RequestParam(required = false) invoiceId: String?,
                      @RequestParam(required = false) supplierId: Long?,
                      @RequestParam(required = false) invoiceNum: String?,
                      @RequestParam(required = false) customerType: Boolean?,
                      @RequestParam(required = false) status: InvoiceStatus?,
                      @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) from: LocalDate?,
                      @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) to: LocalDate?,
                      @RequestParam(required = false) tenant: String?,
                      @RequestParam(required = false) ds: String? = null): PageableBkInvoice {

        var tenantNew: String? = tenant
        if (tenant.isNullOrEmpty()) tenantNew = null
        var invNum = invoiceNum
        if (invoiceNum.isNullOrEmpty()) invNum = null
        return invoiceService.getInvoices(page, size, supplierId, invoiceId, invNum, status, from, to, tenantNew,customerType?:false,ds)
     //   return settlementService.getInvoices(page, size, supplierId, invoiceId, invNum, status, from, to, tenantNew)
    }

    @Get
    fun getSettlementRecords(@RequestParam(required = false) page: Int?,
                             @RequestParam(required = false) size: Int?,
                             @RequestParam(required = false) supplierId: Long?,
                             @RequestParam(required = false) pdi: Long?,
                             @RequestParam(required = false) invoiceId: Long?,
                             @RequestParam(required = false) creditNoteId: Long?,
                             @RequestParam(required = false) settlementNumber: String?,
                             @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) from: LocalDate?,
                             @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) to: LocalDate?,
                             @RequestParam(required = false) customerType: Boolean?,
                             @RequestParam(required = false) tenant: String?,
                             @RequestParam(required = false) ds: String? = null,
                             @RequestParam(required = false) receiptNumber: String?): PageableSettlement {

        var tenantNew: String? = tenant
        if (tenantNew.isNullOrEmpty()) tenantNew = null

        var settlementNumberNew: String? = settlementNumber
        if (settlementNumberNew.isNullOrEmpty()) settlementNumberNew = null

        return settlementService.getSettlementRecords(page, size, supplierId, invoiceId, creditNoteId, settlementNumberNew, from, to, tenantNew,customerType?:false,ds,pdi,receiptNumber)
    }


    @Get("/{id}")
    fun getBkInvoiceById(@PathVariable("id") id: Long):
            ResponseEntity<BkInvoice> {

        val bi = bkInvoiceRepo.get(id) ?: throw RequestException("BkInvoice not found")
        return ResponseEntity.ok(bi)
    }


    @Post
    fun save(@RequestHeader("x-user") user: String, @RequestBody settlement: Settlement,
             @RequestParam(required = false) ds: String? = null): ResponseEntity<Any> {
        val key = "settlement_${settlement.tenant}_${settlement.partnerDetailId}_${settlement.paymentReference}_${settlement.paymentType}"
        val lock = redisLockRegistry.obtain(key)
        if(lock.tryLock(3, TimeUnit.SECONDS)) {
            try {
                return ResponseEntity.ok(settlementService.save(user, settlement, ds))
            }finally {
                lock.unlock()
            }
        }else {
            throw RequestException("Error processing settlement for reference number: ${settlement.paymentReference}. Please refresh and try again.")
        }
    }

    @Get("/getBkInvoicesForSupplier")
    fun getBkInvoicesForSupplier(@RequestParam("supplierId", required = true) supplierId: Long,
                                 @RequestParam(required = false) invoiceNum: String?,
                                 @RequestParam(required = false) status: InvoiceStatus?,
                                 @RequestParam(required = false) customerType: Boolean?,
                                 @RequestParam(required = true) tenant: String,
                                 @RequestParam(required = false) ds: String? = null): List<BkInvoiceIdNum> {

        var inNo: String? = invoiceNum
        if (invoiceNum.isNullOrEmpty()) inNo = null
        return settlementService.getBkInvoicesForSupplier(supplierId,inNo,status,tenant,customerType?:false,ds)
    }

    @Get("/getPendingInvoiceAmount")
    fun getPendingInvoiceAmount(@RequestParam("supplierId", required = false) supplierId: Long?,
                                @RequestParam(required = false) customerType: Boolean?,
                                @RequestParam(required = true) tenant: String,
                                @RequestParam(required = false) ds: String? = null): Double {
        return settlementService.getPendingInvoiceAmount(supplierId, tenant,customerType?:false,ds)
    }

    @Get("/getSettlementAmountForSupplier")
    fun getSettlementAmountForSupplier(@RequestParam("supplierId", required = false) supplierId: Long?)
            = settlementService.getSettlementAmountForSupplier(supplierId)


    @Get("/getById")
    fun getSettlementById(@RequestParam("settlementId") settlementId: Long) = settlementService.getSettlementById(settlementId)

    @Get("/{id}/paymentAdvice")
    fun getPaymentAdvice(@PathVariable("id") id: Long) = settlementService.getPaymentAdviceData(id)

    @Get("/bank/all")
    fun getAllbanks() : List<BkBank>?{
        return settlementService.getAllbanks()
    }

    @Post("/auto/pe")
    fun settleAllPendingCustomerPEInv() : Result{
        settlementService.autoSettlementCustomerPE()
        return success
    }

    @Post("/auto/rio_advance_cns")
    fun settleAllPendingCustomerRIOInvsWithAdvanceCNs() : Result{
        settlementService.autoSettlementRIOInvoicesWithAdvanceCN()
        return success
    }

    @Post("/change/paymentMode")
    fun changeSettlementPaymentMode(@RequestHeader("x-user") user: String, @RequestBody settlement: SettlementPaymentChangeDto): ResponseEntity<Any> {
        return ResponseEntity.ok(settlementService.changePaymentMode(user, settlement))
    }

    @PutMapping("/reverse/{settlementId}")
    fun reverseSettlement(@PathVariable settlementId: Long,@RequestParam(required = true) user: String): Result {
        return settlementService.reversePaymentMode(user, settlementId)
    }

    @Get("/ids")
    fun getAllSettlementIdsByReferenceNumber(@RequestParam(required = true) referenceNumber: String) : List<Long>{
        return settlementService.getAllSettlementIdsByReferenceNumber(referenceNumber)
    }

}