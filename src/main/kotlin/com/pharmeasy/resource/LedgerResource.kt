package com.pharmeasy.resource

import com.pharmeasy.annotation.Get
import com.pharmeasy.annotation.Post
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.*
import com.pharmeasy.model.creditnote.LedgerDiscountCreditNoteDto
import com.pharmeasy.model.debitnote.LedgerDiscountDebitNoteDto
import com.pharmeasy.model.item.DiscountItemDto
import com.pharmeasy.service.*
import com.pharmeasy.type.*
import com.pharmeasy.util.email_attr
import com.pharmeasy.util.user_attr
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.*
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDate

@RestController
@RequestMapping("/ledgers")
class LedgerResource {

    companion object {
        private val log = LoggerFactory.getLogger(LedgerResource::class.java)
    }

    @Autowired
    private lateinit var partnerService: PartnerService

    @Autowired
    private lateinit var settlementService: SettlementService

    @Autowired
    private lateinit var debitNoteService: DebitNoteService

    @Autowired
    private lateinit var creditNoteService: CreditNoteService

    @Autowired
    private lateinit var cashBankLedgerService: CashBankLedgerService

    @Autowired
    private lateinit var rioOutstandingService: RioOutstandingService

    @Get
    fun getVendorLedger(@RequestParam(required = false) page: Int?,
                        @RequestParam(required = false) size: Int?,
                        @RequestParam(required = false) amt1: BigDecimal?,
                        @RequestParam(required = false) amt2: BigDecimal?,
                        @RequestParam(required = false) supplierId: List<Long>?,
                        @RequestParam(required = false) partnerId: Long?,
                        @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) from: LocalDate?,
                        @RequestParam(required = false) customerType: Boolean?,
                        @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) to: LocalDate?,
                        @RequestParam(required = true) tenant: String,
                        @RequestParam(required = false) ds: String? = null,
                        @RequestParam(required = false) firmType: List<Int>?,
                        @RequestParam(required = false) partnerDetailId: Long?,
                        @RequestParam("client") client: List<InvoiceType>): Any {
        return partnerService.getVendorLedgerData(supplierId, partnerId, from, to, amt1, amt2, tenant, page, size,customerType?:false,ds,true,firmType,partnerDetailId,client)
    }

    @RequestMapping("/vendorledger/url", method = arrayOf(RequestMethod.GET), produces = arrayOf(MediaType.APPLICATION_JSON_VALUE))
    fun getVendorInfoSummary(
                             @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) from: LocalDate?,
                             @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) to: LocalDate?,
                             @RequestParam(required = true) tenant: String,
                             @RequestParam(required = false) customerType: Boolean?,
                             @RequestParam(required = true) createdBy: String,
                             @RequestParam(required = false) ds: String? = null,
                            @RequestParam(required = false)email: String?,
                             @RequestParam(required = false) firmType: List<Int>?,
                             @RequestParam(required = false) client: List<InvoiceType>?): CreateResultData {
        return partnerService.getVendorLedgerURL(from?.atStartOfDay(),to?.atTime(23,59,59),tenant,createdBy,customerType?:false,ds,email,firmType,client)

    }



    @RequestMapping("/vendorledger/download", method = arrayOf(RequestMethod.GET), produces = arrayOf(MediaType.APPLICATION_JSON_VALUE))
    fun getVendorInfoSummuryDownload(@RequestParam(required = true) createdBy: String,
                                     @RequestParam(required = false) customerType: Boolean?,
                                     @RequestParam(required = true) tenant: String,
                                     @RequestParam(required = false) ds: String? = null): CreateResultData {
        return partnerService.getVendorLedgerDownload(createdBy, customerType?:false, tenant,ds)
    }



    @Get("/{vendorId}")
    fun getVendorLedger(
                        @PathVariable("vendorId") vendorId: Long,
                        @RequestParam("page", required = false) page: Int?,
                        @RequestParam(required = false) size: Int?,
                        @RequestParam(required = false) invoiceId: Long?,
                        @RequestParam(required = false) docNumber: String?,
                        @RequestParam(required = false) externalRefNo: String?,
                        @RequestParam(required = false) docType: DocumentType?,
                        @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) from: LocalDate?,
                        @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) to: LocalDate?,
                        @RequestParam(required = false) customerType: Boolean?,
                        @RequestParam(required = true) tenant: String,
                        @RequestParam(required = false) distributorId:Long?,
                        @RequestParam(required = false) ds: String? = null): Any {

        return partnerService.getLedgerByVendorId(page, size, docNumber, docType, vendorId, externalRefNo, from, to, tenant,customerType?:false,distributorId,ds)
    }

    @Get("/payment")
    fun getPaymentDetails(@RequestParam("settlementNumber") settlementNumber: String): List<LedgerPaymentDto> {
        return partnerService.getPaymentDetails(settlementNumber)
    }

    @Get("/creditnote/details")
    fun getCreditNoteDetails(@RequestParam("creditNoteNumber") creditNoteNumber: String): List<LedgerCreditNoteDto> {
        log.debug("Inside get credit note details for credit note id: $creditNoteNumber")

      //  val debitNotes = debitNoteService.getDebitNotesForCreditNote(creditNoteNumber)
        val debitNotes = debitNoteService.getDebitNotesForCreditNoteNumber(creditNoteNumber)
        val ledgerCreditNoteDtoList: MutableList<LedgerCreditNoteDto> = mutableListOf()
        val creditNote = creditNoteService.getById(debitNotes[0].creditNoteId!!) ?: throw RequestException("credit note with id ${debitNotes[0].creditNoteId} not found")

        debitNotes.forEach { debitNote ->
            val ledgerCreditNoteDto = LedgerCreditNoteDto(creditNote.createdOn, creditNote.createdBy, creditNote.id, debitNote.debitNoteNumber, creditNote.vendorCreditNoteNumber ?: "", debitNote.createdOn!!.toLocalDate(),
                    debitNote.amountReceivable, creditNote.amount)

            ledgerCreditNoteDtoList.add(ledgerCreditNoteDto)
        }

        return ledgerCreditNoteDtoList
    }


    @RequestMapping("/ledgerdetail/url", method = arrayOf(RequestMethod.GET), produces = arrayOf(MediaType.APPLICATION_JSON_VALUE))
    fun getVendorLedgerDetailUrl(@RequestParam(required = true) vendorId: Long,
                             @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) from: LocalDate?,
                             @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) to: LocalDate?,
                             @RequestParam(required = true) tenant: String,
                             @RequestParam(required = false) customerType: Boolean?,
                             @RequestParam(required = true) createdBy: String,
                                 @RequestParam(required = false) ds: String? = null,
                             @RequestParam(required = false) downloadType: DownloadFileType? = null,
                                 @RequestParam userEmail: String): CreateResultData {
        return partnerService.getLedgerDetailURL(vendorId,from,to,tenant,createdBy,customerType?:false,ds?:null, downloadType,userEmail)

    }

    @RequestMapping("/ledgerdetail/download", method = arrayOf(RequestMethod.GET), produces = arrayOf(MediaType.APPLICATION_JSON_VALUE))
    fun getVendorLedgerDetailDownload(@RequestParam(required = true) tenant: String,
                                      @RequestParam(required = true) createdBy: String,
                                      @RequestParam(required = false) customerType: Boolean?,
                                      @RequestParam(required = false) ds: String? = null): CreateResultData {
    return partnerService.getVendorLedgerDetailDownloadLink(tenant, createdBy, customerType?:false,ds)
    }

    @Get("/all-suppliers")
    fun getAllSupplier(@RequestParam(required = true) tenant: String,
                       @RequestParam(required = false) customerType: Boolean?,
                       @RequestParam(required = false) ds: String? = null): List<SupplierListDTO?> {

        return partnerService.getAllUpdated(tenant,customerType?:false,ds)
    }

    @Get("/creditnote/discount/details")
    fun getDiscountCreditNoteDetails(@RequestParam("creditNoteNumber") creditNoteNumber: String,
                                     @RequestParam("partnerId") partnerId: Long,
                                     @RequestParam("partnerType") partnerType: PartnerType): LedgerDiscountCreditNoteDto {
        log.debug("Inside get Discount Credit Note Details for $creditNoteNumber")

        val items = creditNoteService.getDiscountedItemsByCreditNoteNumber(creditNoteNumber)
        val cn = creditNoteService.getCreditNoteByCreditNoteNumber(creditNoteNumber, partnerId, partnerType)
                ?: throw RequestException("Credit note not found for : $creditNoteNumber")

        val discItemList = mutableListOf<DiscountItemDto>()
        items.forEach { item ->
            val discItem = DiscountItemDto(item.id, item.name!!, item.ucode, item.batch, item.invoiceNumber, item.mrp!!,
                    item.discountPercentage!!, item.discountQuantity!!, item.discountAmount!!)
            discItemList.add(discItem)
        }

        return LedgerDiscountCreditNoteDto(cn.createdOn, cn.createdBy, cn.id, items[0].invoiceNumber, discItemList)
    }

    @Get("/debitnote/discount/details")
    fun getDiscountDebitNoteDetails(@RequestParam("debitNoteNumber") debitNoteNumber: String,
                                    @RequestParam("partnerId") partnerId: Long): LedgerDiscountDebitNoteDto {
        log.debug("Inside get Discount Debit Note Details for $debitNoteNumber")

        val debitNote = debitNoteService.getDebitNoteByDebitNoteNumber(debitNoteNumber, partnerId)
                ?: throw RequestException("Debit note not found for : $debitNoteNumber")

        val items = creditNoteService.getDiscountedItemsByCreditNoteNumber(debitNote.creditNoteNumber!!)

        val discItemList = mutableListOf<DiscountItemDto>()
        items.forEach { item ->
            val discItem = DiscountItemDto(item.id, item.invoiceNumber!!, item.name!!, item.ucode, item.batch, item.mrp!!,
                    item.discountPercentage!!, item.discountQuantity!!, item.discountAmount!!)

            discItemList.add(discItem)
        }
        return LedgerDiscountDebitNoteDto(debitNote.createdOn, debitNote.createdBy, debitNote.id, items[0].invoiceNumber!!, discItemList)
    }

    @Get("/company/{companyId}")
    fun getCompanyLedger(
            @PathVariable("companyId") companyId: Long,
            @RequestParam("page", required = false) page: Int?,
            @RequestParam(required = false) size: Int?,
            @RequestParam(required = false) docNumber: String?,
            @RequestParam(required = false) externalRefNo: String?,
            @RequestParam(required = false) docType: SettlementType?,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) from: LocalDate?,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) to: LocalDate?,
            @RequestParam(required = true) paymentMode: PaymentMode): PaginationDto {

        return cashBankLedgerService.getCompanyLedgerData(paymentMode,companyId,from,to,docType,docNumber,externalRefNo,page,size)
    }

    @RequestMapping("/company/{companyId}/url", method = arrayOf(RequestMethod.GET), produces = arrayOf(MediaType.APPLICATION_JSON_VALUE))
    fun getCompanyLedgerUrl( @PathVariable("companyId") companyId: Long,
                             @RequestParam(required = true) tenant: String,
                             @RequestParam(required = true) createdBy: String,
                             @RequestParam("page", required = false) page: Int?,
                             @RequestParam(required = false) size: Int?,
                             @RequestParam(required = false) docNumber: String?,
                             @RequestParam(required = false) externalRefNo: String?,
                             @RequestParam(required = false) docType: SettlementType?,
                             @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) from: LocalDate?,
                             @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) to: LocalDate?,
                             @RequestParam(required = true) paymentMode: PaymentMode): CreateResultData {

        return cashBankLedgerService.getCompanyLedgerURL(paymentMode,companyId,tenant,createdBy,from,to,docType,docNumber,externalRefNo)

    }

    @RequestMapping("/company/download/{fileId}", method = arrayOf(RequestMethod.GET), produces = arrayOf(MediaType.APPLICATION_JSON_VALUE))
    fun getCompanyLedgerDownload(@PathVariable("fileId") fileId: Long): CreateResultData {
        return cashBankLedgerService.getCompanyLedgerDownload(fileId)
    }

    @Get("/outstanding")
    fun getRetailerOutstanding(
        @RequestParam("distributorId") distributorId: Long,
        @RequestParam("retailerId") retailerId: Long): RetailerOutstandingDTO {

        return partnerService.getRetailerOutstanding(distributorId,retailerId)
    }


    @Get("/outstanding/all")
    fun getRetailerOutstandingV2(@RequestParam(required = false) cnTypes: List<NoteTypes>? = null,
                                 @RequestParam(required = true) retailerPdi: Long,
                                 @RequestParam(required = true) distributorPdi: Long,
                                 @RequestParam(required = false) openAdvanceAmount: Boolean?=false,
                                 @RequestParam(required = false) openPDCAgainstOverdueInvoicesAmount: Boolean?=false,
                                 @RequestParam(required = false) openPDCAmount: Boolean?=false,
                                 @RequestParam(required = false) openCNAmount: Boolean?=false,
                                 @RequestParam(required = false) openDNAmount: Boolean?=false):RioOutstandingDataDto {

        return rioOutstandingService.getRioOutstandingData(cnTypes,retailerPdi,distributorPdi,openAdvanceAmount?:false,openPDCAgainstOverdueInvoicesAmount?:false,openPDCAmount?:false,openCNAmount?:false,openDNAmount?:false)

    }
}