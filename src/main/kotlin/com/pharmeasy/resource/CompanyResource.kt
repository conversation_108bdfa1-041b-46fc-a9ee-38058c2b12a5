package com.pharmeasy.resource

import com.pharmeasy.annotation.Get
import com.pharmeasy.data.CompanyTenantMapping
import com.pharmeasy.model.*
import com.pharmeasy.repo.CompanyTenantMappingRepo
import com.pharmeasy.service.CompanyService
import com.pharmeasy.util.name_attr
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.cache.annotation.CacheEvict
import org.springframework.cache.annotation.Cacheable
import org.springframework.web.bind.annotation.*


@RestController
@RequestMapping("/company")
class CompanyResource {
    companion object {
        private val log = LoggerFactory.getLogger(CompanyResource::class.java)
    }

    @Autowired
    private lateinit var companyTenantMappingRepo: CompanyTenantMappingRepo

    @Autowired
    private lateinit var companyService: CompanyService

    @Cacheable("companyAllInfo")
    @Get("/all")
    fun getallCompanyInfo(@RequestParam(required = false) ds: Boolean? = false): List<ProfileCompanyDTO?> {
        return if(ds==true) companyTenantMappingRepo.findAllCompanyAndDarkStoreForProfile() else companyTenantMappingRepo.findAllCompanyAndTheaForProfile()
    }

    @Get("/all/thea")
    fun getalltheaInfo(@RequestParam(required = false) tenant: String): MutableList<CompanyTenantMapping?> {
        return companyTenantMappingRepo.getAllTenantNameForCompany(tenant)
    }

    @Get("/{id}")
    fun getCompanyInfo(@PathVariable("id") id: Long,@RequestParam(required = false) ds: Boolean? = false): ProfileCompanyDTO? {

        var company = if(ds==true) companyTenantMappingRepo.findCompanyForDarkStore(id) else companyTenantMappingRepo.findCompanyForThea(id)
        var res = if (company.isNullOrEmpty()) null else company.get(0)
        return  res
    }

    @Get("/tenants/all")
    fun getAllTenantsForCompany(@RequestParam("tenant") tenant: String,
                                @RequestParam(required = false) ds: Boolean? = false) = companyTenantMappingRepo.getAllTenantByCompany(tenant)

    @PutMapping("/auto-mail")
    fun enableOrDisableAutomail(@RequestBody tenant: List<String>,
                                @RequestParam status: Boolean,
                                @RequestAttribute(name_attr) userName: String){
        companyService.enableOrDisableAutomail(tenant, status, userName)
    }
    @DeleteMapping("/cache/evict")
    @CacheEvict(value = arrayOf("companyAllInfo"), allEntries = true)
    fun evictCompanyInfoCache(): Result {
        return success
    }

    @Get("/getCompanyByTenant/{tenant}")
    fun getCompanyByTenant(@PathVariable("tenant")tenant: String): CompanyObjectDto{
        return companyService.getCompanyObjectByTenant(tenant)
    }
}