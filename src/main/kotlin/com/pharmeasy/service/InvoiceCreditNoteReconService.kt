package com.pharmeasy.service

import com.pharmeasy.data.CreditNoteSettlement
import com.pharmeasy.data.InvoiceSettlement
import com.pharmeasy.data.Settlement
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.InvoiceCreditNoteReconDto
import com.pharmeasy.repo.*
import com.pharmeasy.repo.read.BkInvoiceReadRepo
import com.pharmeasy.repo.read.CreditNoteReadRepo
import com.pharmeasy.type.*
import com.pharmeasy.util.UUIDUtil
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import java.time.LocalDateTime.now

@Service
class InvoiceCreditNoteReconService {

    @Autowired
    private lateinit var creditNoteReservationMappingRepo: CreditNoteReservationMappingRepo

    @Autowired
    private lateinit var creditNoteReservationRepo: CreditNoteReservationRepo

    @Autowired
    private lateinit var creditNoteReadRepo: CreditNoteReadRepo

    @Autowired
    private lateinit var bkInvoiceRepo: BkInvoiceRepo

    @Autowired
    private lateinit var settlementService: SettlementService

    @Autowired
    private lateinit var settlementRepo: SettlementRepo

    @Autowired
    private lateinit var documentMasterService: DocumentMasterService

    @Autowired
    private lateinit var companyRepo: CompanyRepo

    @Autowired
    private lateinit var creditNoteSettlementRepo: CreditNoteSettlementRepo

    @Autowired
    private lateinit var settlementDistributionMapping:SettlementInvoiceDistributionMappingRepo

    @Autowired
    private lateinit var invoiceService: InvoiceService

    @Transactional
    fun getReservedInvoiceAndCreditNoteDetails(creditNoteReservationIds: List<Long>) {
        creditNoteReservationIds.distinct().forEach {
            var reservedInvoiceDetails = creditNoteReservationRepo.getOne(it)
            var orderId = reservedInvoiceDetails.externalOrderId
            if (reservedInvoiceDetails.status == CreditNoteReservationStatus.RESERVED) {
                var reservedCreditNoteDetails = creditNoteReservationMappingRepo.getByReservationId(it)
                reservedCreditNoteDetails.forEach { resCn ->
                    var cnId = resCn.creditNoteId.id
                    var amount = resCn.amount
                    settleInvoice(InvoiceCreditNoteReconDto(orderId, cnId, amount.toDouble()))
                }
                reservedInvoiceDetails.completedOn = now()
                reservedInvoiceDetails.status = CreditNoteReservationStatus.COMPLETED
                creditNoteReservationRepo.save(reservedInvoiceDetails)
            }else{
                throw RequestException("Reservation Id $it is not in RESERVED State")
            }
        }
    }

    fun settleInvoice(invoiceCreditNoteReconDto: InvoiceCreditNoteReconDto){
        var creditNoteDetails = creditNoteReadRepo.getOne(invoiceCreditNoteReconDto.cnId!!)
        var invoiceDetails  = bkInvoiceRepo.getDataByInvoiceId(invoiceCreditNoteReconDto.orderId!!)

        invoiceDetails?.paidAmount  = invoiceDetails?.paidAmount!! + invoiceCreditNoteReconDto.reservedAmount!!
        bkInvoiceRepo.save(invoiceDetails!!)

        var settlement = Settlement(
            0, now(), now(), "SYSTEM", invoiceDetails!!.supplierId!!,
            invoiceDetails!!.supplierName, invoiceDetails.amount, invoiceCreditNoteReconDto.reservedAmount!!,
            "SETTLED BY RECON API", "settlementNumber", mutableListOf(invoiceDetails!!),
            mutableListOf(creditNoteDetails), PaymentType.CREDITNOTE, creditNoteDetails.creditNoteNumber,
            creditNoteDetails.updatedOn!!.toLocalDate(), invoiceDetails.partnerId, invoiceDetails.partnerDetailId,
            invoiceDetails.type, invoiceDetails.tenant!!, null, null, null, false, false,
            mutableListOf(), mutableListOf(), false,
            receipt = null,
            paymentSource = AdvancePaymentSource.SYSTEM,
            retailerDebitNotes = mutableListOf(),
            uuid = UUIDUtil.generateUuid()
        )

        val company = companyRepo.getCompanyByTenant(settlement.tenant)
            ?: throw RequestException("Company mapping not found for ${settlement.tenant}")

        settlement.settlementNumber = if (settlement.type == PartnerType.CUSTOMER) documentMasterService.getDocumentNumber("SYSTEM", company.companyCode, DocumentType.CX_RECEIPT) else documentMasterService.getDocumentNumber("SYSTEM", company.companyCode, DocumentType.VENDOR_PAYMENT)

        var sr  = settlementRepo.save(settlement)
        var invoiceSettlementList = mutableListOf<InvoiceSettlement>()
        invoiceService.updateInvoices("SYSTEM", sr,invoiceSettlementList)
        var list = mutableListOf<CreditNoteSettlement>()

        var cnsMapping = CreditNoteSettlement(creditNoteId=creditNoteDetails.id, settlementId=sr.id, paidAmount=invoiceCreditNoteReconDto.reservedAmount!!.toBigDecimal(), createdOn= now())
        list.add(cnsMapping)
        creditNoteSettlementRepo.saveAll(list)
        var splitValues = settlementService.splitInvoiceSettlementAmount(null, list, invoiceSettlementList)
        settlementDistributionMapping.saveAll(splitValues)
    }

}