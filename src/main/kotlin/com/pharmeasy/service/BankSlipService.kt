package com.pharmeasy.service

import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.BankSlipDataDTO
import com.pharmeasy.model.ExcelDataDTO
import com.pharmeasy.model.FileUrl
import com.pharmeasy.proxy.PaperServiceProxy
import com.pharmeasy.proxy.SupplierProxy
import com.pharmeasy.type.DownloadFileType
import com.pharmeasy.util.DateUtils
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.io.InputStream
import java.net.HttpURLConnection
import java.net.URL
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import javax.persistence.EntityNotFoundException


@Service
class BankSlipService {

    @Autowired
    private lateinit var companyService: CompanyService

    @Autowired
    private lateinit var paperServiceProxy: PaperServiceProxy

    @Autowired
    private lateinit var supplierProxy: SupplierProxy

    companion object {
        private val log = LoggerFactory.getLogger(BankSlipService::class.java)
    }

    @Transactional
    fun generateBankSlip(fileUrl: String, tenant: String, fileType: DownloadFileType, ds: String?): FileUrl {
        val url = URL(fileUrl)
        val connection: HttpURLConnection = url.openConnection() as HttpURLConnection
        connection.setRequestMethod("GET")
        val inputStream: InputStream = connection.inputStream
        return generateBankSlipUrl(inputStream, tenant, ds, fileType)
    }

    @Transactional
    fun generateBankSlipUrl(
        path: InputStream,
        tenant: String,
        ds: String?,
        fileType: DownloadFileType
    ): FileUrl {
        try {
            val companyTenantDTO = companyService.getCompanyTenantMappingObject(ds ?: tenant)
                ?: throw EntityNotFoundException("Tenant not found")
            val supplier = supplierProxy.getPartnerDetails(
                null,
                null,
                null,
                null,
                id = companyTenantDTO.partnerDetailId?.toInt(),
                null,
                withBankDetail = true
            )
            val company = companyService.getCompanyAccountDetailsByTenant(ds ?: tenant)
            val data = generateBankSlipBody(path)
            val headerMap: MutableMap<String, String> = mutableMapOf()
            headerMap["companyName"] = supplier?.get(0)?.name ?: ""
            headerMap["companyAddress"] = supplier?.get(0)?.address1 ?: ""
            headerMap["bankName"] = supplier?.get(0)?.bankDetails?.get(0)?.bankName ?: ""
            headerMap["branchName"] = supplier?.get(0)?.bankDetails?.get(0)?.branchName ?: ""
            headerMap["accountNo"] = supplier?.get(0)?.bankDetails?.get(0)?.accountNo ?: ""
            headerMap["ifscCode"] = supplier?.get(0)?.bankDetails?.get(0)?.ifscCode ?: ""
            headerMap["date"] = LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_DDMMYYY))
            headerMap["totalAmount"] = getTotalChequeValue(data)
            val dataMap: MutableList<MutableMap<String, Any?>> = mutableListOf()
            data.forEach {
                val bankSlipData: MutableMap<String, Any?> = mutableMapOf()
                bankSlipData["srNo"] = it.srNo
                bankSlipData["chqNo"] = it.chqNo
                bankSlipData["custName"] = it.custName
                bankSlipData["custCode"] = it.custCode
                bankSlipData["chqDt"] = it.chqDt
                bankSlipData["amount"] = it.amount
                dataMap.add(bankSlipData)
            }
            val templateName = company.bankSlipTemplateName
                ?: throw RequestException("No template defined for company ${company.name}")

            val url = when (fileType) {
                DownloadFileType.PDF -> {
                    val pdfBankSlipData: MutableMap<String, Any?> = mutableMapOf()
                    pdfBankSlipData["header"] = headerMap
                    pdfBankSlipData["data"] = dataMap
                    paperServiceProxy.generateInvoicePdfByTemplateName(templateName, pdfBankSlipData)
                }

                DownloadFileType.EXCEL -> {
                    val excelData = ExcelDataDTO(headerMap, dataMap)
                    paperServiceProxy.generateExcelByTemplateName(templateName, excelData)
                }

                else -> throw RequestException("File Type Not Supported")
            }
            return url
        } catch (e: Exception) {
            throw RequestException("Error in paper service for pdf generation, $e")
        }
    }


    private fun generateBankSlipBody(inputStream: InputStream): List<BankSlipDataDTO> {
        val dataList = mutableListOf<BankSlipDataDTO>()
        inputStream.bufferedReader().use { reader ->
            val headers = reader.readLine()?.split(",")?.map { it.trim() } ?: emptyList()
            var slNo = 1
            reader.forEachLine { line ->
                val values = line.split(",")
                val dto = BankSlipDataDTO(
                    srNo = slNo,
                    chqNo = values[headers.indexOf("cheque number")],
                    custName = values[headers.indexOf("customer name")],
                    custCode = values[headers.indexOf("pdi")],
                    chqDt = values.getOrElse(
                        headers.indexOf("cheque date(dd-mm-yyyy)")
                    ) { values.getOrElse(headers.indexOf("cheque date")) { "" } },
                    amount = values.getOrElse(headers.indexOf("cheque amount"))
                    { values.getOrElse(headers.indexOf("amount")) { "" } }
                )
                dataList.add(dto)
                slNo++
            }
        }
        return dataList
    }

    private fun getTotalChequeValue(dataList: List<BankSlipDataDTO>): String {
        val total = dataList.sumByDouble { it.amount.toDouble() }
        return String.format("%.2f", total) // Format the total amount to two decimal places
    }

}


