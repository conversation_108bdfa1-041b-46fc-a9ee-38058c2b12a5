package com.pharmeasy.service

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.pharmeasy.model.*
import com.pharmeasy.proxy.SupplierProxy
import com.pharmeasy.repo.*
import com.pharmeasy.repo.read.BkInvoiceReadRepo
import com.pharmeasy.service.einvoice.EInvoiceFactory
import com.pharmeasy.service.einvoice.EInvoiceInterface
import com.pharmeasy.stream.CronJobPusher
import com.pharmeasy.stream.InvoiceSettlementUpdateHandler
import com.pharmeasy.type.*
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Duration
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalDateTime.now

@Service
class CronJobService {

    companion object {
        private val log = LoggerFactory.getLogger(CronJobService::class.java)
    }

    @Autowired
    private lateinit var bkInvoiceReadRepo: BkInvoiceReadRepo

    @Autowired
    private lateinit var companyService: CompanyService

    @Autowired
    private lateinit var redisUtilityService: RedisUtilityService

    @Autowired
    private lateinit var supplierProxy: SupplierProxy

    @Autowired
    private lateinit var companyTenantMappingRepo: CompanyTenantMappingRepo

    @Autowired
    private lateinit var partnerService: PartnerService

    @Autowired
    private lateinit var eInvoiceFactory: EInvoiceFactory

    @Autowired
    private lateinit var cronJobPusher: CronJobPusher

    @Autowired
    private lateinit var retailerDebitNoteService: RetailerDebitNoteService

    @Autowired
    private lateinit var invoiceService: InvoiceService


    fun cacheAggregatedDataCron() {
        var currentExecutionDateTime = LocalDateTime.now()
        var lastExecutedAt = redisUtilityService.getfromRedis("cacheAggregatedDataCronExecuted")
        // to avoid minor seconds diff between two executions - ignoring 10 seconds
        if (lastExecutedAt != null && LocalDateTime.parse(lastExecutedAt).plusSeconds(590) > currentExecutionDateTime){
            log.debug("Skipping Cron Job cacheAggregatedDataCron. Last Executed: $lastExecutedAt")
            return
        }
        redisUtilityService.setToRedis("cacheAggregatedDataCronExecuted", currentExecutionDateTime.toString(), null)
        log.debug("START: cacheAggregatedDataCron initiated at $currentExecutionDateTime")
        val today: LocalDateTime = LocalDate.now().atTime(23, 59, 59)
        val from: LocalDateTime = today.minusYears(10)
        var tenants = companyTenantMappingRepo.getAllTheaDSTenants()
        var custTypes = listOf(PartnerType.VENDOR, PartnerType.CUSTOMER)
        var isDS = listOf(true, false)
        val mapper = jacksonObjectMapper()
        tenants.forEach{ tenant ->
            custTypes.forEach { custType ->
                isDS.forEach { ds ->
                    try {
                        var tenants = companyService.findTenants(tenant!!)
                        if(!tenants.isNullOrEmpty()) {
                            /*
                            * Since we are not showing aggregated data anymore in UI commenting the below code.
                            * Once sure if it is not causing any issue we can remove this code block.
                            * */
                            var invoiceAggregatedDataCacheKey = "bookkeeper_invoice_aggregated_data_${tenant}_${custType}_${ds}"
//                            redisUtilityService.setToRedis(invoiceAggregatedDataCacheKey, mapper.writeValueAsString(bkInvoiceRepo.getAggregatedInvoiceData(from, today, custType, null, tenants)), null)
//
                            var debitNoteAggregatedDataCacheKey = "bookkeeper_debit_note_aggregated_data_${tenant}_${custType}_${ds}"
//                            redisUtilityService.setToRedis(debitNoteAggregatedDataCacheKey, mapper.writeValueAsString(debitNoteRepo.getAggregatedDebitNoteData(from, today, null, custType, tenants,null)), null)
//
                            var creditNoteAggregatedDataCacheKey = "bookkeeper_credit_note_aggregated_data_${tenant}_${custType}_${ds}"
//                            redisUtilityService.setToRedis(creditNoteAggregatedDataCacheKey, mapper.writeValueAsString(creditNoteRepo.getAggregatedCreditNoteData(from, today, null, null, tenants, custType)), null)
                        }
                        } catch (e: Exception) {
                        log.error("Error while building cache for cacheAggregatedDataCron ${tenant} - ${custType} - ${ds} - Exception: ${e.message}")
                    }
                }
            }
        }
        log.debug("END: cacheAggregatedDataCron completed at ${LocalDateTime.now()}")
    }

    fun cacheSupplierDataCron(cronType: CronType) {
        if (redisUtilityService.setIfAbsent("cacheSupplierDataCronGlobalLock", "1", Duration.ofSeconds(10))) {
            var currentExecutionDateTime = LocalDateTime.now()
            var lastExecutedAt = redisUtilityService.getfromRedis("cacheSupplierDataCronExecuted")
            // to avoid minor seconds diff between two executions - ignoring 10 seconds
            if (lastExecutedAt != null && LocalDateTime.parse(lastExecutedAt).plusSeconds(3590) > currentExecutionDateTime) {
                log.debug("Skipping Cron Job cacheSupplierDataCron. Last Executed: $lastExecutedAt")
                return
            }
            redisUtilityService.setToRedis("cacheSupplierDataCronExecuted", currentExecutionDateTime.toString(), null)
            var tenants = companyTenantMappingRepo.getAllTheaDSTenants()
            var custTypes = listOf(PartnerType.VENDOR, PartnerType.CUSTOMER)
            var isDS = listOf(true, false)
            val mapper = jacksonObjectMapper()
            tenants.forEach { tenant ->
                custTypes.forEach { custType ->
                    isDS.forEach { ds ->
                        try {
                            var tenants = companyService.findTenants(tenant!!)
                            if (!tenants.isNullOrEmpty()) {
                                var listOfPartnerIdData = bkInvoiceReadRepo.getAllSupplier(tenants, custType).associateBy { it!!.partnerId }
                                var listOfPartnerDetailId = bkInvoiceReadRepo.getAllPartnerDetailId(tenants, custType)
                                var cacheKey = "bookkeeper_supplier_data_${custType}_${tenant}_${ds}"
                                if (!listOfPartnerDetailId.isNullOrEmpty()) {
                                    var partnerResult = supplierProxy.allTotalSupplier(true, listOfPartnerDetailId)
                                    partnerResult.forEach {
                                        if (listOfPartnerIdData.containsKey(it!!.partnerId)) {
                                            it!!.client = listOfPartnerIdData.get(it!!.partnerId)!!.client
                                        }
                                    }
                                    redisUtilityService.setToRedis(cacheKey, mapper.writeValueAsString(partnerResult), null)
                                }
                            }
                        } catch (e: Exception) {
                            log.error("Error while building cache for cacheSupplierDataCron ${tenant} - ${custType} - ${ds} - Exception: ${e.message}")
                        }
                    }
                }
            }
            log.debug("END: cacheSupplierDataCron completed at ${LocalDateTime.now()}")
        }else {
            log.debug("cache supplier data cron lock already captured, skipping")
        }
    }
    fun cacheCornTrigger(cronType: CronType){
        cronJobPusher.cacheVendorLedgerCornProducer(cronType)
    }
    fun cacheVendorLedgerCorn(cronType: CronType) {
        var currentExecutionDateTime = LocalDateTime.now()
        log.debug("START: cacheVendorLedgerCorn initiated at $currentExecutionDateTime")
        var tenants  = companyTenantMappingRepo.getAllTheaDSTenants()
        val mapper   = jacksonObjectMapper()
        var ds       = false
        var from     = LocalDate.now().minusDays(6)
        var to       = LocalDate.now()
        tenants.forEach { tenant ->

                try {
                        var tenants = companyService.findTenants(tenant!!, ds)
                        log.debug("from $from and to $to")
                        if (!tenants.isNullOrEmpty()) {
                            var data = partnerService.getVendorLedgerData(null, null, from, to, null, null, tenant, null, null, true, null,false,null,null, listOf(InvoiceType.RIO))
                            var cacheKey = "bookkeeper_customer_ledger_data_CUSTOMER_${tenant}"
                            log.debug("cacheKey $cacheKey")
                            redisUtilityService.setToRedis(cacheKey, mapper.writeValueAsString(data), null)
                        }
                    }catch (e:Exception){
                        log.error("Error while building cache for cacheVendorLedgerCorn ${tenant} - Exception: ${e.message}")
                    }

            }
        }

    fun updateInvoiceWithAmountLessThanOneRupee(){
        var lastExecutedAt = redisUtilityService.getfromRedis("cronExecutionTime")
        if (lastExecutedAt != null && LocalDateTime.parse(lastExecutedAt).plusMinutes(10) > now()){
            log.debug("Skipping Cron Job updateInvoiceWithAmountLessThanOneRupee. Last Executed: $lastExecutedAt")
            return
        }
        redisUtilityService.setToRedis("cronExecutionTime", now().toString(), null)
        val createdOnBuffer = now().minusDays(30)
        invoiceService.closeLessThanOneRupeeInvoices(createdOnBuffer)
        retailerDebitNoteService.closeLessThanOneRupeeDebitNote(createdOnBuffer)
    }

    fun retryEInvoices(eInvoiceType: String) {
        try {
            if (!EInvoiceType.values().map { it.name }.contains(eInvoiceType)) {
                throw IllegalArgumentException("Wrong eInvoice Type encountered")
            }
            val eInvoiceService: EInvoiceInterface? = eInvoiceFactory.getEInvoiceService(eInvoiceType);
            eInvoiceService ?: throw IllegalStateException("Service doesn't exist for eInvoiceType : $eInvoiceType")
            log.info("Pushing to retry Kafka topic for Service  : $eInvoiceType")
            eInvoiceService.pushRetryToKafka()
        }
        catch (ex: Exception){
            log.error("Error occurred while retrying EInvocies cron : ${ex.message}")
            throw ex
        }
    }
}