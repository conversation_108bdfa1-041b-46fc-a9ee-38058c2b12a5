package com.pharmeasy.service

import com.pharmeasy.data.ChequeHandle
import com.pharmeasy.data.CreditNote
import com.pharmeasy.data.RetailerDebitNote
import com.pharmeasy.data.RetailerDnMapping
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.RetailerDebitNoteDto
import com.pharmeasy.repo.RetailerDnMappingRepo
import com.pharmeasy.type.DnType
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.math.RoundingMode

@Service
class RetailerDebitNoteMappingService @Autowired constructor(
    private val retailerDebitNoteMappingRepository: RetailerDnMappingRepo,
    private val creditNoteService: CreditNoteService
) {
    companion object {
        private val log = LoggerFactory.getLogger(RetailerDebitNoteMappingService::class.java)
    }


    fun validateCreditNoteMapping(creditNoteId: Long, retailerDebitNoteDto: RetailerDebitNoteDto) {
        val creditNote = creditNoteService.getById(creditNoteId)
            ?: throw RequestException("Credit note not found for id: $creditNoteId")

        if (creditNote.partnerDetailId != retailerDebitNoteDto.partnerDetailId) {
            throw RequestException("Credit note partner detail id does not match with retailer debit note")
        }

        if (creditNote.amount.setScale(2, RoundingMode.HALF_UP) !=
            BigDecimal.valueOf(retailerDebitNoteDto.amount).setScale(2, RoundingMode.HALF_UP)) {
            throw RequestException("Credit note amount does not match with retailer debit note")
        }

        if (retailerDebitNoteMappingRepository.findByCreditNoteId(creditNoteId) != null) {
            throw RequestException("Retailer debit note is already created for this Credit note")
        }
    }

    fun validateChequeBounceMapping(
        bkChequeHandle: ChequeHandle,
    ) {
        retailerDebitNoteMappingRepository.findByBkChequeHandleAndBounceDate(
            bkChequeHandle,
            bkChequeHandle.bounceDate!!
        )
            .ifPresent {
                throw RequestException("Retailer debit note is already created for this Cheque bounce")
            }
    }

    fun save(retailerDebitNote: RetailerDebitNote, creditNote: CreditNote?, bkChequeHandle: ChequeHandle?) {
        retailerDebitNoteMappingRepository.save(
            RetailerDnMapping(
                retailerDebitNote = retailerDebitNote,
                creditNote = if (retailerDebitNote.type == DnType.CREDIT_NOTE) creditNote else null,
                bkChequeHandle = if (retailerDebitNote.type == DnType.BOUNCE_CHARGE) bkChequeHandle else null
            )
        )
    }
}