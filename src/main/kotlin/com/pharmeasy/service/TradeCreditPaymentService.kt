package com.pharmeasy.service

import com.pharmeasy.data.TradeCreditPayment
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.*
import com.pharmeasy.model.OutstandingTradeCreditDto
import com.pharmeasy.model.TradeCreditDto
import com.pharmeasy.model.VendorLedgerDto
import com.pharmeasy.model.ops.RIOPaymentDTO
import com.pharmeasy.proxy.CoreServiceProxy
import com.pharmeasy.proxy.SupplierProxy
import com.pharmeasy.repo.TradeCreditPaymentRepo
import com.pharmeasy.type.*
import com.pharmeasy.type.ops.PaymentPartnerType
import com.pharmeasy.util.DateUtils
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime.now

@Service
class TradeCreditPaymentService {
    companion object {
        private val log = LoggerFactory.getLogger(TradeCreditPaymentService::class.java)
        private const val TRADE_CREDIT_PAYMENT_MODE="Credit"
    }

    @Autowired
    private lateinit var tradeCreditPaymentRepo: TradeCreditPaymentRepo

    @Autowired
    private lateinit var tradeCreditRepaymentTransactionService: TradeCreditRepaymentTransactionService

    @Autowired
    private lateinit var partnerService: PartnerService

    @Autowired
    private lateinit var tradeCreditService: TradeCreditService

    @Autowired
    private lateinit var settlementService: SettlementService

    @Autowired
    private lateinit var invoiceService: InvoiceService

    @Autowired
    private lateinit var coreServiceProxy: CoreServiceProxy

    @Autowired
    private lateinit var supplierProxy: SupplierProxy



    @Transactional
    fun saveTradeCreditPayment(tradeCreditDto: TradeCreditDto){
        val checkExistingTradeCredit = getExistingTradeCreditByTransactionId(tradeCreditDto.transactionId)
        if (checkExistingTradeCredit == null) {
            save(
                TradeCreditPayment(
                    paidAmount = 0.00,//keeping it zero because when a new entry will save as debit transaction
                    amount = tradeCreditDto.debitAmount,
                    partnerDetailId = tradeCreditDto.partnerDetailId,
                    dueDate = tradeCreditDto.dueDate,
                    transactionId = tradeCreditDto.transactionId,
                    status = PaymentStatus.PENDING
                )
            )
        }
        else{
            checkExistingTradeCredit.amount = checkExistingTradeCredit.amount?.plus(tradeCreditDto.debitAmount!!)
            checkExistingTradeCredit.updatedBy = tradeCreditDto.userId
            save(checkExistingTradeCredit)
        }

        tradeCreditService.updateTradeCreditBalance(tradeCreditDto.partnerDetailId, tradeCreditDto.debitAmount, tradeCreditDto.creditAmount)
    }

    @Transactional
    fun updateTradeCreditPayment(tradeCreditDto: TradeCreditDto){
        log.info("inside updateTradeCreditLedger")
        val tradeCreditPayment = getTradeCreditPaymentByTransactionId(tradeCreditDto.transactionId)
            ?: throw RequestException("Repayment not possible, trade credit transaction id ${tradeCreditDto.transactionId} does not exist")

        tradeCreditPayment.paidAmount = tradeCreditPayment.paidAmount?.plus(tradeCreditDto.creditAmount?:0.00)
        tradeCreditPayment.updatedBy = tradeCreditDto.userId
        tradeCreditPayment.status = if(tradeCreditPayment.paidAmount!! < tradeCreditPayment.amount!!){
            PaymentStatus.PARTIAL_PAID
        }else{
            PaymentStatus.PAID
        }
        save(tradeCreditPayment)
        tradeCreditService.updateTradeCreditBalance(tradeCreditDto.partnerDetailId, tradeCreditDto.debitAmount, tradeCreditDto.creditAmount)
        tradeCreditDto.tradeCreditPaymentId = tradeCreditPayment.id
        tradeCreditRepaymentTransactionService.saveTradeCreditRepayment(tradeCreditDto)
        updateVendorLedger(tradeCreditDto, BigDecimal(tradeCreditDto.creditAmount?:0.00), BigDecimal.ZERO)
        sendSettlementUpdateEvent(tradeCreditDto.partnerDetailId)
    }


    fun getTradeCreditPaymentByTransactionId(transactionId: String): TradeCreditPayment?{
        log.info("inside getTradeCreditLedgerByTransactionId")
        return tradeCreditPaymentRepo.getTradeCreditLedgerByTransactionId(transactionId)
    }

    fun getOutstandingTradeCreditByPartnerDetailId(partnerDetailId: Long): OutstandingTradeCreditDto{
        log.info("inside getOutstandingTradeCreditByPartnerDetailId")
        val tradeCredit = tradeCreditService.getTradeCreditByPartnerDetailId(partnerDetailId)
            ?: return OutstandingTradeCreditDto(0.00,0.00,0)
        val tradeCreditPayment = tradeCreditPaymentRepo.getTradeCreditLedgerByPartnerDetailId(partnerDetailId, now())
        if(tradeCreditPayment.isNullOrEmpty()){
            return OutstandingTradeCreditDto(0.00,0.00,0)
        }
        val tradeCreditOutstandingAmount = tradeCredit.balance
        var tradeCreditOverdueAmount = 0.00
        tradeCreditPayment.forEach {
            tradeCreditOverdueAmount += (it.amount!! - it.paidAmount!!)
        }
        val page = PageRequest.of(0, 1)
        val oldestTradeCredit = tradeCreditPaymentRepo.getMaxTradeCreditDueDays(partnerDetailId, page)
        val maxTradeCreditDueDays = if(oldestTradeCredit.hasContent()){
            java.time.temporal.ChronoUnit.DAYS.between(oldestTradeCredit.content[0]?.createdOn?.toLocalDate(), LocalDate.now())
        }else{
            0
        }
        return OutstandingTradeCreditDto(tradeCreditOutstandingAmount!!, tradeCreditOverdueAmount, maxTradeCreditDueDays)
    }

    private fun save(tradeCreditPayment: TradeCreditPayment){
        tradeCreditPaymentRepo.save(tradeCreditPayment)
    }

    private fun updateVendorLedger(tradeCreditDto:TradeCreditDto, creditAmount: BigDecimal, debitAmount: BigDecimal){
        val vl = VendorLedgerDto(
            transactionDate = LocalDate.now(),
            vendorId = tradeCreditDto.partnerId,
            vendorName = tradeCreditDto.partnerName,
            ledgerEntryType = if (creditAmount != BigDecimal.ZERO) LedgerEntryType.CREDIT else LedgerEntryType.DEBIT,
            documentType = if (creditAmount != BigDecimal.ZERO) DocumentType.TRADE_CREDIT_REPAYMENT else DocumentType.TRADE_CREDIT,
            documentNumber = tradeCreditDto.transactionId,
            referenceNumber = tradeCreditDto.repaymentReferenceNumber,
            externalReferenceNumber = tradeCreditDto.repaymentReferenceNumber,
            particulars = if (creditAmount != BigDecimal.ZERO) "Trade Credit Repayment" else "Trade Credit",
            debitAmount = debitAmount,
            creditAmount = creditAmount,
            tenant = tradeCreditDto.tenant,
            partnerDetailId = tradeCreditDto.partnerDetailId,
            partnerId = tradeCreditDto.partnerId,
            type = PartnerType.CUSTOMER,
            client = InvoiceType.RIO,
            remark = if (creditAmount != BigDecimal.ZERO) "Trade Credit Repayment" else "Trade Credit"
        )

        partnerService.addVendorLedgerEntry(tradeCreditDto.userId!!, vl)
    }

    private fun sendSettlementUpdateEvent(partnerDetailId: Long){
        /*
        since for cases where advance payment was formed by trade credit we don't
        have invoice mapped with it so here getting most recent settled invoice since
        we need to send invoice details in settlement update event
         */

        val settledBkInvoiceId = invoiceService.getRecentInvoiceSettledForPartner(partnerDetailId)
        settlementService.sendSettlementDataToRio(settledBkInvoiceId[0].id, CreationType.INVOICE)
    }

    fun getExistingTradeCreditByTransactionId(transactionId: String): TradeCreditPayment?{
        return tradeCreditPaymentRepo.getTradeCreditLedgerByTransactionId(transactionId)
    }

    /*
    * Payment event is received as list and while settling the invoices data gets looped.
    * So for each invoice a separate line item in settlement and ledger gets created.
    * For trade credit also same was happening which was wrong.
    * To avoid it, this method is created which will check if current event is for Trade Credit Payment or Re-Payment
    * If event is for payment ledger will get updated once the transaction is processed, creating only one record in customer ledger
    * */
    @Transactional
    fun createTradeCreditCustomerLedger(rioPaymentDTOList: List<RIOPaymentDTO>){
        var retailerTxnId: String? = null
        var debitAmount = 0.00
        rioPaymentDTOList.forEach {
            val isTradeCredit = it.creditPartner == PaymentPartnerType.TRADE_CREDIT &&
                    it.paymentMode.equals(TRADE_CREDIT_PAYMENT_MODE, ignoreCase = true)
            if(isTradeCredit){
                retailerTxnId = it.retailerTxnId
                debitAmount += it.retailerTxnAmount?:0.00
            }
        }
        if(retailerTxnId != null) {
            val tenant = when (rioPaymentDTOList[0].distributorId) {
                null -> coreServiceProxy.getRetailerByPartnerDetailId(rioPaymentDTOList[0].retailerFrontEndPartyCode!!.toLong())?.tenant
                else -> invoiceService.getTenantCodeAndCustomerType(rioPaymentDTOList[0].distributorId!!.toLong()).tenant
            }
            if (tenant.isNullOrEmpty()){
                throw RequestException("Tenant not found. distributorId: ${rioPaymentDTOList[0].distributorId!!.toLong()} partnerDetailId: ${rioPaymentDTOList[0].retailerFrontEndPartyCode!!.toLong()}")
            }
            val supplier = supplierProxy.supplier(null, rioPaymentDTOList[0].retailerFrontEndPartyCode!!.toLong())
            val tradeCreditDto = TradeCreditDto(
            tenant = tenant,
            partnerId = supplier[0]?.partnerId!!,
            partnerName = supplier[0]?.partnerName!!,
            transactionId = retailerTxnId!!,
            partnerDetailId = rioPaymentDTOList[0].retailerFrontEndPartyCode!!.toLong(),
            creditAmount = 0.00,
            debitAmount = debitAmount,
            userId = rioPaymentDTOList[0].initiatedBy,
            dueDate = DateUtils.getDateTimeFromEpochStamp(rioPaymentDTOList[0].creditDueDate)!!,
            repaymentReferenceNumber = null
            )
            updateVendorLedger(tradeCreditDto, BigDecimal.ZERO, BigDecimal(debitAmount)
            )
        }
    }

    @Transactional
    fun migrateTradeCredit(migrateTradeCredit: MigrateTradeCreditDto, userId: String){
        log.info("inside migrateTradeCredit")
        val supplier = supplierProxy.supplier(null, migrateTradeCredit.partnerDetailId)
        val tenant = invoiceService.getTenantCodeAndCustomerType(migrateTradeCredit.distributorPdi).tenant
        val tradeCreditDto = TradeCreditDto(
            tenant =  tenant,
            partnerId = supplier[0]?.partnerId!!,
            partnerName = supplier[0]?.partnerName!!,
            transactionId = migrateTradeCredit.transactionId,
            partnerDetailId = migrateTradeCredit.partnerDetailId,
            creditAmount = 0.00, //migrating only open trade credits
            debitAmount = migrateTradeCredit.amount,
            userId = userId,
            dueDate = migrateTradeCredit.dueDate.atStartOfDay(),
            repaymentReferenceNumber = null
        )
        saveTradeCreditPayment(tradeCreditDto)
        updateVendorLedger(tradeCreditDto, BigDecimal.ZERO, BigDecimal(migrateTradeCredit.amount))
    }

}
