package com.pharmeasy.service

import com.pharmeasy.data.*
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.*
import com.pharmeasy.repo.BkInvoiceRepo
import com.pharmeasy.repo.SettlementTaskCsvMappingRepo
import com.pharmeasy.repo.SettlementTaskItemRepo
import com.pharmeasy.repo.SettlementTaskRepo
import com.pharmeasy.repo.read.SettlementTaskItemReadRepo
import com.pharmeasy.type.AdvancePaymentSource
import com.pharmeasy.type.PaymentType
import com.pharmeasy.type.SelectionType
import com.pharmeasy.type.SettlementTaskStatus
import com.pharmeasy.util.UUIDUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.lang.Exception
import java.time.LocalDateTime
import kotlin.math.abs

@Component
class SettlementTaskProcessor(@Value("\${app.maxCashLimit}") val maxCashLimit: Double) {

    companion object {
        private val log = LoggerFactory.getLogger(SettlementTaskProcessor::class.java)
    }

    @Autowired
    private lateinit var settlementTaskItemRepo: SettlementTaskItemRepo

    @Autowired
    private lateinit var settlementTaskItemReadRepo: SettlementTaskItemReadRepo

    @Autowired
    private lateinit var settlementService: SettlementService

    @Autowired
    private lateinit var settlementTaskRepo: SettlementTaskRepo

    @Autowired
    private lateinit var invoiceRepo: BkInvoiceRepo

    @Autowired
    private lateinit var settlementTaskCsvMappingRepo: SettlementTaskCsvMappingRepo

    @Autowired
    private lateinit var companyService: CompanyService

    @Transactional
    fun processTask(taskDto: ProcessSettlementTaskDto, user: String, ds: String? = null, tenant: String, userHash: String) {
        var partialSuccess = false
        val parentTaskOpt = settlementTaskRepo.findById(taskDto.taskId)
        val parentTask: SettlementTask

        if (!parentTaskOpt.isPresent) {
            throw RequestException("No settlement task with id ${taskDto.taskId} exists")
        } else {
            parentTask = parentTaskOpt.get()
        }
        if(parentTask.settlementMode == PaymentType.CASH){
            var settlementTaskPartners = settlementTaskItemReadRepo.getPartnersBySettlementTask(taskDto.taskId)
            settlementTaskPartners.forEach {
                var amount = settlementTaskItemReadRepo.getPartnersAmountBySettlementTask(taskDto.taskId, it)
                if(amount > maxCashLimit){
                    parentTask.taskStatus = SettlementTaskStatus.FAILED
                    settlementTaskRepo.save(parentTask)
                    log.error("Settlement Task ${taskDto.taskId} for Partner Detail Id $it is greater than 2 Lakh")
                    throw RequestException("Settlement Task ${taskDto.taskId} for Partner Detail Id $it is greater than 2 Lakh")

                }
                if(!settlementService.checkCashSettlementAmount(it!!, amount)){
                    parentTask.taskStatus = SettlementTaskStatus.FAILED
                    settlementTaskRepo.save(parentTask)
                    log.error("Settlement Task ${taskDto.taskId} for Partner Detail Id $it is greater than 2 Lakh")
                    throw RequestException("Settlement amount is greater than daily cash settlement limit for partner detail id $it")

                }
            }
        }
        try {
            log.debug("Settlement task processing started with id ${taskDto.taskId} started by user $user")

            val uniqueItemMap = mutableMapOf<SettlementUniqueKey, MutableList<SettlementTaskItem>>()
            val invoiceNumToTaskItemMap = mutableMapOf<String, MutableList<SettlementTaskItem>>()
            val invoiceNumToBkInvoiceMap = mutableMapOf<String, BkInvoice>()

            val taskItemIdList = when(taskDto.selectionType) {
                SelectionType.ALL -> settlementTaskItemRepo.getAllItemsId(taskDto.taskId)
                SelectionType.EXCLUDE -> settlementTaskItemRepo.getAllItemsIdNotInList(taskDto.taskId, taskDto.taskItemIdList!!)
                SelectionType.INCLUDE -> taskDto.taskItemIdList!!
            }

            val taskItemsSublist = taskItemIdList.windowed(200, 200, partialWindows = true)

            // store all invoice to taskItem mapping
            taskItemsSublist.forEach { sublist ->
                val taskItems = settlementTaskItemRepo.getOpenItems(taskDto.taskId, sublist)
                taskItems.forEach { item ->
                    if (invoiceNumToTaskItemMap[item.invoiceNumber] == null) {
                        invoiceNumToTaskItemMap[item.invoiceNumber] = mutableListOf()
                    }
                    invoiceNumToTaskItemMap[item.invoiceNumber]?.add(item)
                }
            }
            log.debug("Total unique invoices for task id ${parentTask.id} is ${invoiceNumToTaskItemMap.size}")

            // validate what invoices can be settled
            invoiceNumToTaskItemMap.forEach taskItemLoop@{ (invoiceNum, taskItems) ->
                try {
                    if (!invoiceNumToBkInvoiceMap.containsKey(invoiceNum)) {
                      //  val tenants = companyService.findTenants(parentTask.company.tenant, ds)
                        val invoice = invoiceRepo.getByInvoiceNum(invoiceNum, mutableListOf(parentTask.company.tenant))
                        if (invoice.isNullOrEmpty() || invoice[0]?.status == InvoiceStatus.PAID) {
                            partialSuccess = true
                            return@taskItemLoop
                        }
                        invoiceNumToBkInvoiceMap.putIfAbsent(invoiceNum, invoice[0]!!)
                    }
                    val invoiceFromDb = invoiceNumToBkInvoiceMap[invoiceNum]!!
                    var invoiceOpenAmt = invoiceFromDb.amount.minus(invoiceFromDb.paidAmount)
                    val itemsToSettle = mutableListOf<SettlementTaskItem>()
                    taskItems.forEach { item ->
                        if (abs(item.amount - invoiceOpenAmt) <= 1 || invoiceOpenAmt >= item.amount) {
                            itemsToSettle.add(item)
                            invoiceOpenAmt -= item.amount
                        }
                    }
                    if (itemsToSettle.isEmpty()) {
                        partialSuccess = true
                    }
                    invoiceNumToTaskItemMap[invoiceNum] = itemsToSettle
                } catch (e: Exception) {
                    log.error("Error filtering invoices to settle for invoice $invoiceNum, settlementTaskId: ${parentTask.id}", e)
                    partialSuccess = true
                }
            }

            // group all possible settling invoices by partner detail id, ref_no, payment_date
            invoiceNumToTaskItemMap.forEach { (_, taskItems) ->
                taskItems.forEach { item ->
                    val key = SettlementUniqueKey(
                        item.partnerDetailId, item.chequeOrRefNo,
                        item.paymentDate, item.bank
                    )
                    if (uniqueItemMap[key] == null) {
                        uniqueItemMap[key] = mutableListOf()
                    }
                    uniqueItemMap[key]?.add(item)
                }
            }

            // create settlement object on the basis of unique key
            uniqueItemMap.forEach uniqueItemLoop@{ (key, taskItems) ->
                val invoicesToSettle = mutableListOf<BkInvoice>()
                taskItems.forEach { item ->
                   // val tenants = companyService.findTenants(parentTask.company.tenant, ds)
                    val invoiceFromDb = invoiceRepo.getByInvoiceNum(item.invoiceNumber, mutableListOf(parentTask.company.tenant))[0]!!
                    invoiceNumToBkInvoiceMap[item.invoiceNumber] = invoiceFromDb
                    val invoice = createCopyInvoice(invoiceFromDb, item.amount)
                    invoicesToSettle.add(invoice)
                }

                val totalPayableAmount = taskItems.sumByDouble { it.amount }
                var totalDueAmount = 0.0
                invoicesToSettle.forEach { invoice ->
                    totalDueAmount += invoiceNumToBkInvoiceMap[invoice.invoiceNum]!!.amount - invoiceNumToBkInvoiceMap[invoice.invoiceNum]!!.paidAmount
                }

                // Settlement level round off check.
                if (abs(totalDueAmount - totalPayableAmount) > 1.0 && totalPayableAmount > totalDueAmount) {
                    taskItems.forEach { item ->
                        item.settled = false
                        item.updatedOn = LocalDateTime.now()
                    }
                    settlementTaskItemRepo.saveAll(taskItems)
                    partialSuccess = true
                    return@uniqueItemLoop
                }

                log.debug("TaskId : ${parentTask.id} : invoice to settle : ${invoicesToSettle.size}")

                var mapListInvoice: Map<String?, List<BkInvoice>> = invoicesToSettle.groupBy { it.invoiceNum }
                var listToSettle = mutableListOf<BkInvoice>()
                mapListInvoice.forEach { (key, value) ->
                    var finalInvoiceAmt = value.sumByDouble { it.paidAmount }
                    value[0].paidAmount = finalInvoiceAmt
                    listToSettle.add(value[0])

                }

                log.debug("TaskId : ${parentTask.id} : list to settle : ${listToSettle.size}")

                val settlementObj = Settlement(
                    createdOn = LocalDateTime.now(),
                    updatedOn = LocalDateTime.now(),
                    createdBy = userHash,
                    supplierId = invoicesToSettle.first().supplierId!!,
                    supplierName = invoicesToSettle.first().supplierName,
                    amount = totalDueAmount,
                    paidAmount = totalPayableAmount,
                    remarks = "Settled",
                    settlementNumber = null,
                    invoices = listToSettle.toMutableList(),
                    paymentType = parentTask.settlementMode,
                    paymentReference = key.paymentRefNo,
                    paymentDate = key.paymentDate,
                    partnerId = invoicesToSettle.first().partnerId,
                    partnerDetailId = invoicesToSettle.first().partnerDetailId,
                    type = parentTask.partnerType,
                    tenant = tenant,
                    chequeDate = if (parentTask.settlementMode == PaymentType.CHEQUE) taskItems[0].chequeDate else null,
                    bankId = if (parentTask.settlementMode == PaymentType.CHEQUE) key.bank?.id else null,
                    bankName = if (parentTask.settlementMode == PaymentType.CHEQUE) key.bank?.name else null,
                    id = 0,
                    creditNotes = mutableListOf(),
                    isBounced = false,
                    reversed = false,
                    advancePayment = mutableListOf(),
                    chargeInvoice = mutableListOf(),
                    charge = false,
                    receipt = null,
                    paymentSource = AdvancePaymentSource.SYSTEM,
                    retailerDebitNotes = mutableListOf(),
                    uuid = UUIDUtil.generateUuid()
                )

                val settlement = settlementService.save(userHash, settlementObj, ds)
                val csvMappingList = mutableListOf<SettlementTaskCsvMapping>()

                taskItems.forEach { taskItem ->
                    taskItem.settled = true
                    taskItem.updatedOn = LocalDateTime.now()
                }

                val items = settlementTaskItemRepo.saveAll(taskItems)
                items.forEach { csvMappingList.add(SettlementTaskCsvMapping(settlementTaskItem = it, settlement = settlement)) }
                settlementTaskCsvMappingRepo.saveAll(csvMappingList)
            }

            parentTask.updatedOn = LocalDateTime.now()
            parentTask.taskStatus =
                if (partialSuccess) SettlementTaskStatus.PARTIAL_COMPLETE else SettlementTaskStatus.COMPLETE
            settlementTaskRepo.save(parentTask)
            log.debug("Settlement task with id ${parentTask.id} completed with status ${parentTask.taskStatus}")
        } catch (t: Throwable) {
            log.error("Settlement task with id ${taskDto.taskId} failed", t)
            parentTask.taskStatus = if(partialSuccess) SettlementTaskStatus.PARTIAL_COMPLETE else SettlementTaskStatus.FAILED
            settlementTaskRepo.save(parentTask)
        }
    }

    private fun createCopyInvoice(invoice: BkInvoice, amountFromFile: Double): BkInvoice {
        var amountToPay = amountFromFile
        if(invoice.status == InvoiceStatus.PARTIAL_PAID){
            amountToPay += invoice.paidAmount
        }
        return invoice.copy(paidAmount = amountToPay)
    }
}


