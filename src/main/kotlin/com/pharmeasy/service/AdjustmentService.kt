package com.pharmeasy.service

import com.pharmeasy.constants.DefaultConst
import com.pharmeasy.data.AdjustmentDataLog
import com.pharmeasy.data.AdjustmentEntry
import com.pharmeasy.data.CompanyTenantMapping
import com.pharmeasy.data.Partner
import com.pharmeasy.data.VendorLedger
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.PaginationDto
import com.pharmeasy.model.Result
import com.pharmeasy.model.success
import com.pharmeasy.repo.*
import com.pharmeasy.specification.AdjustmentSpecification
import com.pharmeasy.type.DocumentType
import com.pharmeasy.type.LedgerEntryType
import com.pharmeasy.type.PartnerType
import com.pharmeasy.type.Status
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.dao.DataAccessException
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import javax.transaction.Transactional

@Service
class AdjustmentService {
    companion object {
        private val log = LoggerFactory.getLogger(AdjustmentService::class.java)
    }

    @Autowired
    private lateinit var adjustmentRepo: AdjustmentRepo

    @Autowired
    private lateinit var companyRepo: CompanyRepo

    @Autowired
    private lateinit var vendorLedgerRepo: VendorLedgerRepo

    @Autowired
    private lateinit var partnerRepo: PartnerRepo

    @Autowired
    private lateinit var checkerService: CheckerService

    @Autowired
    private lateinit var adjustmentDataLogRepo: AdjustmentDataLogRepo

    @Autowired
    private lateinit var documentMasterService: DocumentMasterService

    @Autowired
    private lateinit var companyService: CompanyService

    @Autowired
    private lateinit var redisUtilityService: RedisUtilityService

    @Autowired
    private lateinit var vaultCheckerService: CheckerService

    @Transactional
    fun createAdjustment(adjustment: AdjustmentEntry, userName: String, userId: String, tenant: String, userMail: String, ds: String? = null): AdjustmentEntry {
        var companyMappingObj: CompanyTenantMapping = companyService.getCompanyTenantMappingObject(ds?:tenant)
                ?: throw RequestException("No tenant mapping found for $tenant : ds :$ds")
        if (adjustment.id == null) {
            adjustment.createdBy = userName
            adjustment.createdById = userId
            adjustment.createdOn = LocalDateTime.now()
            adjustment.tenant = companyMappingObj.tenant
            adjustment.companyId = companyMappingObj.companyId
        } else {
            var adjustmentObj: AdjustmentEntry =
                adjustmentRepo.get(adjustment.id!!)
                    ?: throw RequestException("Invalid adjustment transaction !")

            adjustment.createdBy = adjustmentObj.createdBy
            adjustment.createdById = adjustmentObj.createdById
            adjustment.createdOn = adjustmentObj.createdOn
            adjustment.tenant = adjustmentObj.tenant
            adjustment.companyId = adjustmentObj.companyId
        }
        adjustment.updatedBy = userName
        adjustment.updatedOn = LocalDateTime.now()
        adjustment.status = Status.PENDING_APPROVAL
        // adjustment.partnerDetailId = bkInvoiceRepo.getPartnerDetailId(adjustment.partnerId) ?: 0
        checkAndSendNotificationForApproval(adjustment, tenant, userMail, userId, ds)

        var adjustmentSave = adjustmentRepo.save(adjustment)

        try {
            adjustmentDataLogRepo.save(
                AdjustmentDataLog(
                    null,
                    adjustmentSave.id!!,
                    adjustmentSave.status,
                    LocalDateTime.now(),
                    userName
                )
            )
        } catch (e: DataAccessException) {
            log.error("failed to save adjustment log", e)
        }

        return adjustmentSave
    }

    fun getAllAdjustment(page: Int?, size: Int?, documentId: String?, partnerId: Long?, createdBy: String?, updatedBy: String?,
                         from: LocalDate?, to: LocalDate?, transactionFrom: LocalDate?, transactionTo: LocalDate?, id: Long?, status: MutableList<String?>?, assignedTo: String?, tenant: String, adjType: String?, ds: String? = null): PaginationDto {
        val sortBy = if (transactionFrom != null && transactionTo != null) "transactionDate" else "updatedOn"
        val pagination =
            PageRequest.of(page ?: DefaultConst.ZERO, size ?: DefaultConst.TEN, Sort.Direction.DESC, sortBy)

        var type: PartnerType? = null
        if (adjType != null) {
            try {
                type = PartnerType.valueOf(adjType.toUpperCase())
            } catch (e: IllegalArgumentException) {
                log.error(" $adjType:Adjustment type is not valid", e)
            }
        }
        var statuslist: MutableList<Status?> = mutableListOf()

        if (!status.isNullOrEmpty()) {
            status.forEach {
                try {
                    if (it != null) statuslist.add(Status.valueOf(it.toUpperCase()))
                } catch (e: IllegalArgumentException) {
                    log.error("Could not derive Status from $it", e)
                }
            }
        }

        var companyMappingObj: CompanyTenantMapping? = companyService.getCompanyTenantMappingObject(ds?:tenant)
                ?: throw RequestException("No tenant mapping found for ${tenant} ")

        val res =
            adjustmentRepo.findAll(
                AdjustmentSpecification(
                    documentId,
                    partnerId,
                    createdBy,
                    updatedBy,
                    from,
                    to,
                    transactionFrom,
                    transactionTo,
                    companyMappingObj?.companyId,
                    id,
                    statuslist,
                    assignedTo,
                    type
                ),
                pagination
            )

        return PaginationDto(res.totalElements, res.totalPages, res.hasPrevious(), res.hasNext(), res.content)
    }


    fun checkAndSendNotificationForApproval(adjustment: AdjustmentEntry, tenant: String, userMail: String, userId: String,ds: String? = null) {

        var checkers = checkerService.findCheckers(tenant,null,ds)

        if (checkers.isNotEmpty()) {
            var checker = checkers.get(0)
            adjustment.assignedTo = checker?.userName
            adjustment.assignedToId = checker?.userId
        } else {
            throw RequestException("No checker found for this company !")
        }
    }

    @Transactional
    fun changeAdjustmentStatus(id:Long,status:String,tenant: String,userId:String,userMail: String, ds: String? = null):Result{

        var adjustmentObj = adjustmentRepo.get(id)
        if (adjustmentObj == null) throw RequestException("Invalid adjustment transaction !")
        var checkers = checkerService.findCheckers(tenant, null, ds)

        if (userId == adjustmentObj.createdById) {
            throw RequestException("Cannot approve the request as created by you")
        }

        if (Status.APPROVED.name.equals(status.toUpperCase()) || Status.REJECTED.name.equals(status.toUpperCase()) ||
            Status.REVIEW.name.equals(
                status.toUpperCase()
            )
        ) {
            if (checkers.isEmpty()) throw RequestException("No checker found for this company !")

            var checker = checkerService.compareChecker(checkers, adjustmentObj.assignedToId ?: "")
            if (!adjustmentObj.assignedToId.equals(userId) || checker == null) {
                throw RequestException(
                    "You are not authorized to change adjustment status !"
                )
            }

            adjustmentObj.updatedBy = checker?.userName
            adjustmentObj.updatedOn = LocalDateTime.now()
            adjustmentObj.status = Status.valueOf(status)

            if (Status.REVIEW.name.equals(status.toUpperCase())) {
                adjustmentObj.assignedTo = adjustmentObj.createdBy
                adjustmentObj.assignedToId = adjustmentObj.createdById
            }
            if (Status.APPROVED.name.equals(status.toUpperCase())) {
                addDocumentId(adjustmentObj)
                updateVendorBalance(adjustmentObj)
                addAdjustmentToLedger(adjustmentObj)
                updateVendorLedgerData(adjustmentObj)
            }

            try {
                adjustmentRepo.save(adjustmentObj)
            } catch (e: DataAccessException) {
                log.error("Could not save Adjustment", e)
                return Result(DefaultConst.HTTP_BAD_REQUEST, "Failed to update status.")
            }
            try {
                adjustmentDataLogRepo.save(
                    AdjustmentDataLog(
                        null,
                        adjustmentObj.id!!,
                        adjustmentObj.status,
                        LocalDateTime.now(),
                        adjustmentObj.updatedBy
                            ?: ""
                    )
                )
            } catch (e: DataAccessException) {
                log.error("failed to save adjustment log", e)
            }
        }

        return Result(DefaultConst.HTTP_OK, "Success")
    }

    fun addDocumentId(adjustment: AdjustmentEntry): AdjustmentEntry {
        val company =
            companyRepo.getCompanyByTenant(adjustment.tenant!!)
                ?: throw RequestException("Company mapping not found for ${adjustment.tenant}")
        adjustment.documentId =
            documentMasterService.getDocumentNumber(
                adjustment.updatedBy!!, company.companyCode,
                DocumentType.ADJUSTMENT
            )
        return adjustment
    }

    fun addAdjustmentToLedger(
        adjustment: AdjustmentEntry,
        referenceNumber: String? = "",
        externalReferenceNumber: String? = ""
    ) {
        // todo need to change for customer transaction
        log.debug("adjust obj = $adjustment")
        var lastTransaction =
            vendorLedgerRepo.getLastVendorLedgerForDay(
                adjustment.transactionDate,
                adjustment.companyId ?: 0,
                adjustment.partnerId,
                adjustment.type.name
            )

        log.debug("last trans bal = $lastTransaction")
        var bal: BigDecimal
        var creditBal: BigDecimal? = BigDecimal.ZERO
        var debitBal: BigDecimal? = BigDecimal.ZERO

        if (adjustment.ledgerEntryType.name.equals(LedgerEntryType.CREDIT.name)) {
            creditBal = adjustment.creditAmount
        } else {
            debitBal = adjustment.debitAmount
        }
        if (lastTransaction != null) {
            if (adjustment.ledgerEntryType.name.equals(LedgerEntryType.CREDIT.name)) {
                if (adjustment.type == PartnerType.VENDOR) {
                    bal = lastTransaction.balance.add(adjustment.creditAmount)
                } else {
                    bal = lastTransaction.balance.subtract(adjustment.creditAmount)
                }
            } else {
                if (adjustment.type == PartnerType.VENDOR) {
                    bal = lastTransaction.balance.subtract(adjustment.debitAmount)
                } else {
                    bal = lastTransaction.balance.add(adjustment.debitAmount)
                }
            }
        } else {
            if (adjustment.ledgerEntryType.name.equals(LedgerEntryType.CREDIT.name)) {
                if (adjustment.type == PartnerType.VENDOR) {
                    bal = BigDecimal.ZERO.add(adjustment.creditAmount)
                } else {
                    bal = BigDecimal.ZERO.subtract(adjustment.creditAmount)
                }
            } else {
                if (adjustment.type == PartnerType.VENDOR) {
                    bal = BigDecimal.ZERO.subtract(adjustment.debitAmount)
                } else {
                    bal = BigDecimal.ZERO.add(adjustment.debitAmount)
                }
            }
        }

        log.debug("bal = $bal")
        val vl =
            VendorLedger(
                id = 0,
                createdOn = LocalDateTime.now(),
                updatedOn = LocalDateTime.now(),
                createdBy = adjustment.createdBy,
                updatedBy = adjustment.createdBy,
                transactionDate = adjustment.transactionDate,
                vendorId = adjustment.partnerId,
                vendorName = adjustment.vendorName,
                ledgerEntryType = adjustment.ledgerEntryType,
                documentNumber = adjustment.documentId!!,
                documentType = adjustment.documentType,
                referenceNumber = referenceNumber,
                externalReferenceNumber = externalReferenceNumber,
                particulars = adjustment.particulars,
                debitAmount = debitBal ?: BigDecimal.ZERO,
                creditAmount = creditBal ?: BigDecimal.ZERO,
                balance = bal,
                partnerId = adjustment.partnerId,
                partnerDetailId = adjustment.partnerDetailId,
                tenant = adjustment.tenant!!,
                companyId = adjustment.companyId!!,
                type = adjustment.type,
                client = adjustment.client,
                transactionTimestamp = adjustment.transactionDate.atTime(LocalTime.now())
            )
        vendorLedgerRepo.save(vl)
    }

    fun updateVendorBalance(adjustment: AdjustmentEntry) {
        log.debug("inside updateVendorBalance : AdjustmentEntry $adjustment")
        var partner = partnerRepo.getVendorBalance(adjustment.partnerId, adjustment.companyId!!, adjustment.type)

        if (partner == null) {
            partner =
                Partner(
                    0, LocalDateTime.now(), LocalDateTime.now(), adjustment.createdBy, adjustment.updatedBy,
                    adjustment.partnerId, adjustment.vendorName ?: "",
                    BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, 0L,
                    adjustment.partnerId, adjustment.partnerDetailId, adjustment.companyId ?: 0,
                    adjustment.type
                )
        }

        if (adjustment.ledgerEntryType.name.equals(LedgerEntryType.DEBIT.name)) {
            if (partner.type == PartnerType.VENDOR) {
                partner.balance = partner.balance.subtract(adjustment.debitAmount)
                partner.debitBalance = partner.debitBalance.add(adjustment.debitAmount)
            } else {
                partner.balance = partner.balance.add(adjustment.debitAmount)
                partner.debitBalance = partner.debitBalance.add(adjustment.debitAmount)
            }
        } else {
            if (partner.type == PartnerType.VENDOR) {
                partner.balance = partner.balance.add(adjustment.creditAmount)
                partner.creditBalance = partner.creditBalance.add(adjustment.creditAmount)
            } else {
                partner.balance = partner.balance.subtract(adjustment.creditAmount)
                partner.creditBalance = partner.creditBalance.add(adjustment.creditAmount)
            }
        }
        partnerRepo.save(partner)
    }

    fun updateVendorLedgerData(adjustment: AdjustmentEntry) {
        var ledgerData =
            vendorLedgerRepo.getForUpdateVendorLedger(
                adjustment.transactionDate,
                adjustment.companyId!!,
                adjustment.partnerId,
                adjustment.type
            )
        if (ledgerData.isNotEmpty()) {
            ledgerData.forEach {
                updateBalance(adjustment, it)
            }
        }
        vendorLedgerRepo.saveAll(ledgerData)
    }

    private fun updateBalance(
        adjustment: AdjustmentEntry,
        it: VendorLedger?
    ) {
        if (adjustment.ledgerEntryType.name.equals(LedgerEntryType.DEBIT.name)) {
            log.debug("inside debit ${it?.balance}")
            if (it?.type == PartnerType.VENDOR) {
                it?.balance = it?.balance?.subtract(adjustment.debitAmount)!!
            } else {
                it?.balance = it?.balance?.add(adjustment.debitAmount)!!
            }
            log.debug("after debit ${it?.balance}")
        } else {
            log.debug("inside credit ${it?.balance}")
            if (it?.type == PartnerType.VENDOR) {
                it?.balance = it?.balance?.add(adjustment.creditAmount)!!
            } else {
                it?.balance = it?.balance?.subtract(adjustment.creditAmount)!!
            }
            log.debug("after credit ${it?.balance}")
        }
    }

    @Transactional
    fun updateAdjustmentChecker(adjustmentId: Long, checkerId: Long, tenant: String, userName: String, ds: String? = null): Result{

        val checker = checkerService.findCheckers(tenant, checkerId, ds)
        if (checker.isEmpty()) { throw RequestException("No active checker found with id  $checkerId , for logged in company!") }
        if (checker.size>1) throw RequestException("More than 1 checker found $checkerId , for logged in company!")

        val adjustment =
            adjustmentRepo.get(adjustmentId)
                ?: throw RequestException("No adjustment entry found with id $adjustmentId !")

        if (adjustment.status != Status.PENDING_APPROVAL) {
            throw RequestException(
                "Adjustment with id $adjustmentId is not in PENDING_APPROVAL state!!"
            )
        }

        val companyId = companyService.getCompanyTenantMappingObject(ds?:tenant)?.companyId
            ?: throw RequestException("Company not found for the given tenant")

        val checkerUserIdList = vaultCheckerService.getCheckerByCompanyId(companyId).mapNotNull { it?.userId }.toMutableList()

        if(userName != adjustment.createdBy && userName !in checkerUserIdList){
            throw RequestException("not a valid user cannot update")
        }

        adjustment.assignedToId = checker[0]?.userId
        adjustment.assignedTo = checker[0]?.userName
        adjustment.updatedBy = userName

        adjustmentRepo.save(adjustment)

        return success
    }
}
