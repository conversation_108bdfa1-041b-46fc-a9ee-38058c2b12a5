package com.pharmeasy.service


import com.pharmeasy.data.*
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.*
import com.pharmeasy.proxy.SupplierProxy
import com.pharmeasy.repo.advancepayment.AdvancePaymentRepo
import com.pharmeasy.repo.*
import com.pharmeasy.type.*
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.time.Duration
import java.time.LocalDate
import com.pharmeasy.type.DnType
import com.pharmeasy.util.UUIDUtil
import org.springframework.validation.annotation.Validated
import java.math.RoundingMode
import java.time.LocalDateTime.now
import java.time.format.DateTimeFormatter
import javax.validation.Valid

@Service
class EasySolMigrationService {

    companion object {
        private val log = LoggerFactory.getLogger(EasySolMigrationService::class.java)
    }

    @Autowired
    private lateinit var debitNoteRepo: DebitNoteRepo

    @Autowired
    private lateinit var debitNoteService: DebitNoteService

    @Autowired
    private lateinit var supplierProxy: SupplierProxy

    @Autowired
    private lateinit var bkInvoiceRepo : BkInvoiceRepo

    @Autowired
    private lateinit var companyService: CompanyService

    @Autowired
    private lateinit var companyRepo: CompanyRepo

    @Autowired
    private lateinit var creditNoteRepo: CreditNoteRepo

    @Autowired
    private lateinit var documentMasterService: DocumentMasterService

    @Autowired
    private lateinit var advancePaymentRepo: AdvancePaymentRepo

    @Autowired
    private lateinit var partnerService: PartnerService

    @Autowired
    private lateinit var retailerDebitNoteService: RetailerDebitNoteService

    @Autowired
    private lateinit var redisUtilityService: RedisUtilityService

    @Autowired
    private lateinit var pdcDataRepo: PdcDataRepo

    @Autowired
    private lateinit var retailerDebitNoteRepo: RetailerDebitNoteRepo

    @Autowired
    private lateinit var settlementService: SettlementService

    @Autowired
    private lateinit var companyTenantMappingRepo: CompanyTenantMappingRepo

    @Autowired
    private lateinit var flatDiscountTaskRepo: FlatDiscountTaskRepo

    @Autowired
    private lateinit var flatDiscountTaskDetailRepo: FlatDiscountTaskDetailRepo

    @Autowired
    private lateinit var vendorLedgerRepo: VendorLedgerRepo

    @Autowired
    private lateinit var debitNoteDetailRepo: DebitNoteDetailRepo

    @Autowired
    private lateinit var catalogService: CatalogService



    @Transactional
    fun migrateCreditNotes(easySolCnMigrationDTO: EasySolCnMigrationDTO):ResponseEntity<Any>{
        val partnerDetailId = easySolCnMigrationDTO.partnerDetailId.toLong()
        val cnAmount = easySolCnMigrationDTO.cnAmount.toBigDecimal()
        val tenant = easySolCnMigrationDTO.tenant
        val referenceId = easySolCnMigrationDTO.easysolCnId
        var documentDate = LocalDate.parse(easySolCnMigrationDTO.documentDate, DateTimeFormatter.ofPattern("dd/MM/yyyy")).atStartOfDay()
        if(redisUtilityService.setIfAbsent("CN_MIGRATION_GLOBAL_LOCK_$referenceId", "$partnerDetailId", Duration.ofMillis(500))) {
            val data = creditNoteRepo.checkMigrationData(referenceId, tenant)
            if (data != null) {
                return ResponseEntity(HttpStatus.CONFLICT)
            }

            val company = companyRepo.getCompanyByTenant(tenant)
                ?: throw RequestException("Company mapping not found for ${tenant}")

            val companyTenant = companyTenantMappingRepo.getByTenant(tenant)

            val partner = supplierProxy.supplier(null, partnerDetailId)
            if (partner.isEmpty()) throw RequestException("Unable to create the cn, partner Info not found : $partnerDetailId")

            val flatDiscountObj = flatDiscountTaskRepo.save(FlatDiscountTask(null, now(), now(), LocalDate.now(), LocalDate.now(), "SYSTEM",
            "SYSTEM","SYSTEM","SYSTEM","SYSTEM",now(),"","",tenant,
                NoteTypes.PURCHASE_SLAB_DISCOUNT_CN, SlabTaskStatus.COMPLETED, companyTenant!!))

            val sgst = 0
            val cgst = 0
            val igst = 0

            val flatTaskDetail = flatDiscountTaskDetailRepo.save(FlatDiscountTaskDetail(null, now(), now(),partner[0]?.partnerId!!,partnerDetailId,
                partner[0]?.partnerName!!,cnAmount,cnAmount,BigDecimal(0),sgst,cgst,igst,0,InvoiceType.EASY_SOL, flatDiscountObj,"",""))

            var creditNote = CreditNote(
                id = 0,
                createdOn = now(),
                updatedOn = now(),
                createdBy = "SYSTEM",
                updatedBy = "SYSTEM",
                status = NoteStatus.PENDING,
                noteType = flatDiscountObj.type,
                amount = cnAmount,
                supplierId = partner[0]?.partnerId!!,
                supplierName = partner[0]?.partnerName!!,
                remarks = "Created by Easysol CN Migration task",
                settlementId = null,
                invoiceId = null,
                debitNotes = null,
                closureType = CreditNoteClosureType.MANUAL,
                partnerId = partner[0]?.partnerId!!,
                partnerDetailId = partnerDetailId,
                tenant = tenant,
                creditNoteNumber = documentMasterService.getDocumentNumber("SYSTEM", company.companyCode,DocumentType.CX_CN_DISCOUNT),
                type = PartnerType.CUSTOMER,
                client = InvoiceType.EASY_SOL,
                receiptStatus = ReceiptStatus.RECEIVED,
                expectedDate = null,
                vendorCreditNoteDate = null,
                vendorCreditNoteAmount = null,
                vendorCreditNoteNumber = null,
                slabTaskDetail = null,
                flatTaskDetail = flatTaskDetail,
                referenceId = referenceId,
                documentDate = documentDate
            )
            creditNote = creditNoteRepo.save(creditNote)
            debitNoteService.createDebitNoteForSlabTask(creditNote, company.companyCode, false)

            return ResponseEntity(HttpStatus.OK)
        }else{
            return ResponseEntity(HttpStatus.CONFLICT)
        }
    }


    fun migrateCreateInvoice(invoiceEasySolDto: InvoiceEasySolDTO) : ResponseEntity<Any>{
        var partnerDetailId = invoiceEasySolDto.partnerDetailId?.trim()?.toLong()
        var amount = invoiceEasySolDto.amount
        var tenant = invoiceEasySolDto.tenant
        var partner = supplierProxy.supplier(null, partnerDetailId)
        if (partner.isNullOrEmpty()) throw RequestException("Unable to create the invoice partner Info not found : $partnerDetailId")
        var dueDate = LocalDate.parse(invoiceEasySolDto.dueDate, DateTimeFormatter.ofPattern("dd/MM/yyyy"))
        var createdDate = LocalDate.parse(invoiceEasySolDto.createdOn, DateTimeFormatter.ofPattern("dd/MM/yyyy"))
        var invoice = bkInvoiceRepo.getEasySolInvoice(invoiceEasySolDto.invoiceId!!, tenant!!)
        if (invoice == null) {
            var bkInvoiceData = BkInvoice(
                id = 0,
                createdOn = createdDate.atStartOfDay(),
                createdBy = "SYSTEM",
                invoiceId = invoiceEasySolDto.invoiceId,
                invoiceNum = invoiceEasySolDto.invoiceNum,
                supplierId = partner[0]!!.partnerId,
                supplierName = partner[0]!!.partnerName,
                amount = (invoiceEasySolDto.amount).toDouble(),
                paidAmount = (invoiceEasySolDto.paidAmount).toDouble(),
                dueDate = dueDate,
                status = if((invoiceEasySolDto.status).toLowerCase() == "partial_paid")InvoiceStatus.PARTIAL_PAID else InvoiceStatus.PENDING,
                items = mutableListOf(),
                partnerId = partner[0]!!.partnerId,
                partnerDetailId = partnerDetailId,
                tenant = tenant,
                type = PartnerType.CUSTOMER,
                client = InvoiceType.EASY_SOL,
                settledOn = null,
                updatedOn = now(),
                updatedBy = "SYSTEM",
                apiVersion = APIVersionType.V2,
                distributorPdi = invoiceEasySolDto.distributorPdi?.toLong()
            )

            var dbObj = bkInvoiceRepo.save(bkInvoiceData)
            return ResponseEntity(HttpStatus.OK)
        }else{
            return ResponseEntity(HttpStatus.CONFLICT)
        }
    }

    @Transactional
    fun migrateAdvancePayment(easySolAdvancePaymentMigrationDTO: EasySolAdvancePaymentMigrationDTO):ResponseEntity<Any>{

        val partnerDetailId = easySolAdvancePaymentMigrationDTO.partnerDetailId.toLong()
        val amount = easySolAdvancePaymentMigrationDTO.amount.toBigDecimal()
        val paymentReferenceNumber = easySolAdvancePaymentMigrationDTO.paymentReferenceNumber
        val paymentDate = LocalDate.parse(easySolAdvancePaymentMigrationDTO.paymentDate, DateTimeFormatter.ofPattern("dd/MM/yyyy"))
        val payment = easySolAdvancePaymentMigrationDTO.paymentType
        val bankName = easySolAdvancePaymentMigrationDTO.bankName
        var paymentType: PaymentType?
        var chequeDate: LocalDate? = null
        val tenant = easySolAdvancePaymentMigrationDTO.tenant

        if (this.redisUtilityService.setIfAbsent("ADVANCE_PAYMENT_MIGRATION_GLOBAL_LOCK_${paymentReferenceNumber}", "$paymentReferenceNumber", Duration.ofMillis(500))) {
            val companyMappingObj = companyService.getCompanyTenantMappingObject(tenant)
                ?: throw RequestException("no tenant mapping found for ${tenant}")

            val company = companyRepo.getCompanyByTenant(tenant)
                ?: throw RequestException("Company mapping not found for ${tenant}")


        var advancePayRefList : MutableList<AdvancePayRefMapping> = mutableListOf()


            val advancePayRef = AdvancePayRefMapping(0, now(), now(),"",
                LocalDate.now(),null)
            advancePayRefList.add(advancePayRef)
        log.info("partnerDetailId $partnerDetailId and amount $amount and paymentReferenceNumber $paymentReferenceNumber and paymentDate $paymentDate")
        var partner = supplierProxy.supplier(null, partnerDetailId)
        if (partner.isNullOrEmpty()) throw RequestException("Unable to create the advance payment, partner Info not found : $partnerDetailId")

            when (payment) {
                "CASH" -> paymentType = PaymentType.CASH
                "CHEQUE" -> {
                    paymentType = PaymentType.CHEQUE
                    chequeDate = paymentDate
                }
                "NEFT" -> paymentType = PaymentType.NEFT
                "RIO_PAY" -> paymentType = PaymentType.RIO_PAY
                else -> throw RequestException("Invalid Payment Type")
            }

            val data = advancePaymentRepo.checkMigrationData(paymentReferenceNumber, tenant)
            if (data != null) {
                return ResponseEntity(HttpStatus.CONFLICT)
            }

            val documentId =
                documentMasterService.getDocumentNumber("SYSTEM", company.companyCode, DocumentType.ADVANCE_PAYMENT)

            val advancePayment = AdvancePayment(
                id = 0,
                createdOn = now(),
                updatedOn = now(),
                createdBy = "SYSTEM",
                createdByName = "SYSTEM",
                updatedBy = "SYSTEM",
                userEmail = "SYSTEM",
                assignedTo = "SYSTEM",
                assignedToId = "SYSTEM",
                approvalDate = LocalDate.now(),
                vendorName = partner[0]?.partnerName,
                documentId = documentId,
                typeOfAdvance = AdvanceType.DIRECT,
                type = PartnerType.CUSTOMER,
                status = Status.APPROVED,
                amount = amount,
                amountPending = amount,
                partnerId = partner[0]?.partnerId!!,
                partnerDetailId = partnerDetailId,
                tenant = tenant,
                companyId = companyMappingObj.companyId,
                client = InvoiceType.EASY_SOL,
                remarks = "EASY_SOL_MIGRATED_DATA",
                chequeDate = chequeDate,
                bankName = bankName,
                paymentReference = paymentReferenceNumber,
                paymentDate = paymentDate,
                refDocuments = advancePayRefList,
                paymentType = paymentType
            )
            advancePayment.refDocuments.forEach { refDocument ->
                log.info("ref details :: $refDocument")
                refDocument.advancePayment = advancePayment
            }

            advancePaymentRepo.save(advancePayment)
            return ResponseEntity(HttpStatus.OK)
        }else{
            return ResponseEntity(HttpStatus.CONFLICT)
        }
    }

    @Transactional
    fun migrateLedger(easySolLedgerMigrationDTO: EasySolLedgerMigrationDTO):ResponseEntity<Any>{
        val partnerDetailId = easySolLedgerMigrationDTO.partnerDetailId.toLong()
        val amount = easySolLedgerMigrationDTO.amount.toBigDecimal()
        val tenant = easySolLedgerMigrationDTO.tenant
        if (this.redisUtilityService.setIfAbsent("LEDGER_MIGRATION_GLOBAL_LOCK_${partnerDetailId}", "$partnerDetailId", Duration.ofMillis(500))) {
            val company = companyRepo.getCompanyByTenant(tenant)
                ?: throw RequestException("Company mapping not found for ${tenant}")

            val docNumber =
                documentMasterService.getDocumentNumber("system", company.companyCode, DocumentType.ADJUSTMENT)

            val partner = supplierProxy.supplier(null, partnerDetailId)
            if (partner.isEmpty()) throw RequestException("Unable to create the advance payment, partner Info not found : $partnerDetailId")

            val data = vendorLedgerRepo.checkMigratedPartner(partnerDetailId, tenant, DocumentType.ADJUSTMENT, InvoiceType.EASY_SOL, company.id)

            if (data != null) {
                return ResponseEntity(HttpStatus.CONFLICT)
            }

            val ledgerDto = VendorLedgerDto(
                transactionDate = LocalDate.now(),
                vendorId = partner[0]!!.partnerId!!,
                vendorName = partner[0]!!.partnerName!!,
                partnerDetailId = partnerDetailId,
                partnerId = partner[0]!!.partnerId!!,
                type = PartnerType.CUSTOMER,
                client = InvoiceType.EASY_SOL,
                creditAmount = BigDecimal.ZERO,
                debitAmount = amount,
                documentNumber = docNumber,
                documentType = DocumentType.ADJUSTMENT,
                externalReferenceNumber = "",
                ledgerEntryType = LedgerEntryType.DEBIT,
                particulars = "Easy Sol Migration Entry",
                referenceNumber = "",
                tenant = tenant
            )
            partnerService.addVendorLedgerEntry("SYSTEM", ledgerDto)
            return ResponseEntity(HttpStatus.OK)
        }else{
            return ResponseEntity(HttpStatus.CONFLICT)
        }
    }

    fun migratePdc(pdcData: PdcDataDTO): ResponseEntity<Any>{
       try {
           var chequeDate = LocalDate.parse(pdcData.chequeDate, DateTimeFormatter.ofPattern("dd/MM/yyyy"))
           var createdDate = LocalDate.parse(pdcData.createdOn, DateTimeFormatter.ofPattern("dd/MM/yyyy"))
           var pdcObj = pdcDataRepo.save(PDCMigrationData(id=0, createdOn = createdDate.atStartOfDay(), tenant = pdcData.tenant, adjustedAmt = pdcData.adjustedAmt!!.toDouble(),
               chequeAmt = pdcData.chequeAmt!!.toDouble(), chequeDate = chequeDate, chequeNo = pdcData.chequeNo, invoiceAmt = pdcData.invoiceAmt!!.toDouble(),
               invoiceNo = pdcData.invoiceNo, isMigrated = false, partyName = pdcData.partyName, pdi = pdcData.pdi.toLong()))

           return ResponseEntity(HttpStatus.OK)
       }catch (e: DataIntegrityViolationException){
           return ResponseEntity(HttpStatus.CONFLICT)
       }catch (e: Exception){
               throw RequestException("error : $e")

       }

    }

    @Transactional
    fun settleMigratedInvoice(pdcData: PDCSettlementDto): ResponseEntity<Any>{
//            var allChequeNumber = pdcDataRepo.getChequeNos(tenant)
            log.info("cheque found : ${pdcData.chequeNo}")
            val version =
            try {
                 APIVersionType.valueOf(pdcData.version.toUpperCase())
            }catch (e: Exception){
                throw RequestException("API version ${pdcData.version} not supported")
            }




//            allChequeNumber.forEach {
                var allPdcDataForChequeNo = pdcDataRepo.getByChequeNo(pdcData.tenant, pdcData.chequeNo)
                if(!allPdcDataForChequeNo.isNullOrEmpty()) {

                    var grpPdiCheque: Map<Long, List<PDCMigrationData?>> = allPdcDataForChequeNo.groupBy { it!!.pdi }

                    grpPdiCheque.forEach { t, u ->
                        var openInvoices =
                            bkInvoiceRepo.getEasySolInvoiceForSettlement(u.map { it!!.invoiceNo },t,
                                pdcData.tenant)
                        if(openInvoices.isEmpty()) {
                            log.error("No open invoice found for cheque no ${pdcData.chequeNo} and pdi : $t")
                        }else {
                            when(version){
                                APIVersionType.V1 -> createPdcSettlement(u, openInvoices, pdcData.tenant)
                                APIVersionType.V2 -> createPdcSettlementAfterCn(u, openInvoices, pdcData.tenant)
                            }
                        }
                    }
                    return ResponseEntity(HttpStatus.OK)

//                }
            }else{
                throw RequestException("cheque no $pdcData.chequeNo not found!")
                }
    }

    private fun createPdcSettlement(
        allPdcDataForChequeNo: List<PDCMigrationData?>,
        openInvoices:List<BkInvoice?> ,tenant: String
    ):ResponseEntity<Any> {
        var bkInvoiceMap = openInvoices.associateBy { it!!.invoiceNum }
        var copyInvoiceList = mutableListOf<BkInvoice>()

        allPdcDataForChequeNo.forEach {
            var invoice = bkInvoiceMap.get(it!!.invoiceNo)
            if(invoice!= null) {
                var copy = invoice!!.copy()
                copy.paidAmount = invoice.paidAmount + it.adjustedAmt!!.toDouble()
                copyInvoiceList.add(copy)
            }else{
                throw RequestException("invoice not found : ${it!!.invoiceNo}")
            }

        }
        var partnerDetailId = copyInvoiceList[0].partnerDetailId
        log.info("create settlement for partnerDetailId $partnerDetailId and cheque number ${allPdcDataForChequeNo[0]!!.chequeNo}")
        var partner = supplierProxy.supplier(null, partnerDetailId)
        if (partner.isEmpty()) throw RequestException("Unable to settle invoice, partner Info not found : $partnerDetailId")

            var settlement = Settlement(
                0,
                null,
                null,
                null,
                partner[0]!!.partnerId!!,
                partner[0]!!.partnerName,
                allPdcDataForChequeNo[0]!!.chequeAmt!!.toDouble(),
                allPdcDataForChequeNo[0]!!.chequeAmt!!.toDouble(),
                "easySol_migration",
                null,
                copyInvoiceList,
                mutableListOf(),
                PaymentType.CHEQUE,
                allPdcDataForChequeNo[0]!!.chequeNo,
                LocalDate.now(),
                partner[0]!!.partnerId!!,
                partnerDetailId,
                PartnerType.CUSTOMER,
                tenant,
                allPdcDataForChequeNo[0]!!.chequeDate,
                null,
                null,
                false,
                false,
                advancePayment = mutableListOf(),
                chargeInvoice = mutableListOf(),
                charge = false,
                receipt = null,
                paymentSource = AdvancePaymentSource.SYSTEM,
                retailerDebitNotes = mutableListOf(),
                uuid = UUIDUtil.generateUuid()
            )
            settlementService.save("SYSTEM", settlement, null)

        allPdcDataForChequeNo.forEach {
            it?.isMigrated = true
        }
        pdcDataRepo.saveAll(allPdcDataForChequeNo)
        return ResponseEntity(HttpStatus.OK)
    }

    @Transactional
    fun migrateDebitNotes(debitNoteMigrationDto: DebitNoteMigrationDto): ResponseEntity<Any> {
        val lock = retailerDebitNoteRepo.getDebitNoteByReferenceId(debitNoteMigrationDto.referenceId, debitNoteMigrationDto.partnerDetailId.toLong())
        if (lock == null) {
            val type = when (debitNoteMigrationDto.type) {
                "CREDIT_NOTE" -> DnType.CREDIT_NOTE
                "BOUNCE_CHARGE" -> DnType.BOUNCE_CHARGE
                else -> DnType.ADHOC
            }
            val retailerDebitNoteDto = RetailerDebitNoteDto(
                remarks = "Easy-sol Migrated Data",
                type = type,
                amount = debitNoteMigrationDto.amount.toDouble(),
                amountReceived = debitNoteMigrationDto.amountReceived?.toDouble() ?: 0.00,
                taxableValue = debitNoteMigrationDto.taxableValue?.toDouble() ?: 0.00,
                taxRate = debitNoteMigrationDto.taxableRate?.toDouble() ?: 0.00,
                taxPercent = debitNoteMigrationDto.taxPercent?.toDouble() ?: 0.00,
                partnerDetailId = debitNoteMigrationDto.partnerDetailId.toLong(),
                migrationReferenceId = debitNoteMigrationDto.referenceId,
                isMigrated = true,
                hsn = null,
                isGstnApplicable = false,
                refId = null,
                useCase = null,
                uuid = UUIDUtil.generateUuid()
            )
            retailerDebitNoteService.createRetailerDebitNote("SYSTEM", retailerDebitNoteDto, debitNoteMigrationDto.tenant)
            return ResponseEntity(HttpStatus.OK)
        } else {
            return ResponseEntity(HttpStatus.CONFLICT)
        }
    }

    private fun createPdcSettlementAfterCn(
        allPdcDataForChequeNo: List<PDCMigrationData?>,
        openInvoices: List<BkInvoice?>, tenant: String
    ): ResponseEntity<Any> {
        var bkInvoiceMap = openInvoices.associateBy { it!!.invoiceNum }
        var copyInvoiceList = mutableListOf<BkInvoice>()
        var remainingChequeAmount = allPdcDataForChequeNo[0]!!.chequeAmt.toBigDecimal()
        allPdcDataForChequeNo.forEach {
            var invoice = bkInvoiceMap.get(it!!.invoiceNo)
            if (invoice != null) {
                val invoiceRemainingAmount = (invoice.amount - invoice.paidAmount).toBigDecimal()
                if (invoiceRemainingAmount > BigDecimal.ZERO && remainingChequeAmount > BigDecimal.ZERO) {
                    var bkInvoiceCopy = invoice.copy()
                    var eligibleSettleAmount = invoiceRemainingAmount.min(remainingChequeAmount)
                    bkInvoiceCopy.paidAmount = bkInvoiceCopy.paidAmount.plus(eligibleSettleAmount.toDouble())
                    copyInvoiceList.add(bkInvoiceCopy)
                    remainingChequeAmount -= eligibleSettleAmount
                }
            } else {
                throw RequestException("invoice not found : ${it!!.invoiceNo}")
            }

        }
        var partnerDetailId = copyInvoiceList[0].partnerDetailId
        log.info("create settlement for partnerDetailId $partnerDetailId and cheque number ${allPdcDataForChequeNo[0]!!.chequeNo}")
        var partner = supplierProxy.supplier(null, partnerDetailId)
        if (partner.isEmpty()) throw RequestException("Unable to settle invoice, partner Info not found : $partnerDetailId")

        var settlement = Settlement(
            0,
            null,
            null,
            null,
            partner[0]!!.partnerId!!,
            partner[0]!!.partnerName,
            allPdcDataForChequeNo[0]!!.chequeAmt!!.toDouble(),
            allPdcDataForChequeNo[0]!!.chequeAmt!!.toDouble(),
            "easySol_migration",
            null,
            copyInvoiceList,
            mutableListOf(),
            PaymentType.CHEQUE,
            allPdcDataForChequeNo[0]!!.chequeNo,
            LocalDate.now(),
            partner[0]!!.partnerId!!,
            partnerDetailId,
            PartnerType.CUSTOMER,
            tenant,
            allPdcDataForChequeNo[0]!!.chequeDate,
            null,
            null,
            false,
            false,
            advancePayment = mutableListOf(),
            chargeInvoice = mutableListOf(),
            charge = false,
            receipt = null,
            paymentSource = AdvancePaymentSource.SYSTEM,
            retailerDebitNotes = mutableListOf(),
            uuid = UUIDUtil.generateUuid()
        )
        settlementService.save("SYSTEM", settlement, null)

        allPdcDataForChequeNo.forEach {
            it?.isMigrated = true
        }
        pdcDataRepo.saveAll(allPdcDataForChequeNo)
        return ResponseEntity(HttpStatus.OK)
    }

    @Transactional
    fun settleInvoiceWithCn(easySolCnInvoiceSettlementDto: EasySolCnInvoiceSettlementDto): ResponseEntity<Any> {

        var allPdcDataForChequeNo = pdcDataRepo.getByChequeNoAndPdi(
            easySolCnInvoiceSettlementDto.tenant,
            easySolCnInvoiceSettlementDto.chequeNumber,
            easySolCnInvoiceSettlementDto.pdi
        ) ?: throw RequestException("PDC data not found for cheque no ${easySolCnInvoiceSettlementDto.chequeNumber}")

        val uniqueInvoiceNumbers = allPdcDataForChequeNo.mapNotNull { it?.invoiceNo }.toSet()
        val invoices = bkInvoiceRepo.getEasySolInvoiceForSettlement(
            uniqueInvoiceNumbers.toMutableList(),
            easySolCnInvoiceSettlementDto.pdi,
            easySolCnInvoiceSettlementDto.tenant
        )
        if (invoices.isEmpty()) throw RequestException("Invoice not found for cheque no ${easySolCnInvoiceSettlementDto.chequeNumber}")
        val creditNote = creditNoteRepo.getOpenEasySolCnData(
            easySolCnInvoiceSettlementDto.cnNumber,
            easySolCnInvoiceSettlementDto.pdi,
            easySolCnInvoiceSettlementDto.tenant
        )
            ?: throw RequestException("No open CN found for credit note no ${easySolCnInvoiceSettlementDto.cnNumber}")
        var creditNoteRemainingAmount = creditNote.remainingAmount
        invoices.forEach { invoice ->
            val invoiceRemainingAmount = (invoice!!.amount - invoice.paidAmount).toBigDecimal()
            if (invoiceRemainingAmount > BigDecimal.ZERO && creditNoteRemainingAmount > BigDecimal.ZERO) {
                var bkInvoiceCopy = invoice.copy()
                var eligibleCNAmount = invoiceRemainingAmount.min(creditNoteRemainingAmount)
                bkInvoiceCopy.paidAmount = bkInvoiceCopy.paidAmount.plus(eligibleCNAmount.toDouble())
                creditNote.amountUsed = eligibleCNAmount
                var remarks = easySolCnInvoiceSettlementDto.chequeNumber
                var settlement = Settlement(
                    0,
                    null,
                    null,
                    null,
                    bkInvoiceCopy.partnerId!!,
                    bkInvoiceCopy.supplierName,
                    invoiceRemainingAmount.toDouble(),
                    eligibleCNAmount.toDouble(),
                    remarks,
                    null,
                    mutableListOf(bkInvoiceCopy),
                    mutableListOf(creditNote),
                    PaymentType.CREDITNOTE,
                    easySolCnInvoiceSettlementDto.cnNumber,
                    LocalDate.now(),
                    bkInvoiceCopy.partnerId,
                    null,
                    PartnerType.CUSTOMER,
                    easySolCnInvoiceSettlementDto.tenant,
                    null,
                    null,
                    null,
                    false,
                    reversed = false,
                    advancePayment = mutableListOf(),
                    chargeInvoice = mutableListOf(),
                    charge = false,
                    receipt = null,
                    paymentSource = AdvancePaymentSource.SYSTEM,
                    retailerDebitNotes = mutableListOf(),
                    uuid = UUIDUtil.generateUuid()
                )
                settlementService.save("SYSTEM", settlement)
                creditNoteRemainingAmount -= eligibleCNAmount
                //logic to allow round off within 1 rupee for all settlements
                val allowedRoundOffDiff = if ((bkInvoiceCopy.amount - bkInvoiceCopy.amount.toInt() != 0.00) &&
                    (bkInvoiceCopy.amount.toInt() + 1 >= bkInvoiceCopy.paidAmount && bkInvoiceCopy.paidAmount >= bkInvoiceCopy.amount.toInt())
                ) {
                    0.99
                } else {
                    0.01
                }

                if (invoiceRemainingAmount == eligibleCNAmount || (bkInvoiceCopy.amount - bkInvoiceCopy.paidAmount) <= allowedRoundOffDiff) {
                    allPdcDataForChequeNo.find { it?.invoiceNo == invoice?.invoiceNum }?.let {
                        it.isMigrated = true
                        pdcDataRepo.save(it)
                    }
                }

            } else {
                return ResponseEntity(HttpStatus.OK)
            }
        }
        return ResponseEntity(HttpStatus.CONFLICT)
    }

    @Transactional
    fun migrateProofCn( proofCnList : List<ProofCnDTO>): List<ProofCnDTO>{
        val debitNoteNumber = proofCnList[0].debitNoteNumber
        val lockKey = "MIGRATE_PROOF_CN_LOCK_$debitNoteNumber"

        val lockAcquired = redisUtilityService.setIfAbsent(lockKey, "LOCKED", Duration.ofMinutes(5))
        if (lockAcquired) {
            try { val validationErrors = proofCnList.map { it.validate() }.flatten()
        if (validationErrors.isNotEmpty()) {
             throw RequestException("Validation failed for ProofCnDTO items: ${validationErrors.joinToString(", ")}")
        }
        if (proofCnList.map { it.debitNoteNumber }.toSet().size > 1) {
            throw RequestException("All ProofCnDTO items must have the same debitNoteNumber")
        }
        val sumOfDnBillValue = proofCnList.sumOf { it.dnBillValue }
        val roundedSumOfDnBillValue = BigDecimal(sumOfDnBillValue).setScale(2, RoundingMode.HALF_UP)
        val totalAmount = BigDecimal(proofCnList[0].totalAmount).setScale(2, RoundingMode.HALF_UP)
        var ucodeList = proofCnList.map { it.ucode }
        val productUnitList = catalogService.getCatalogData(ucodeList)
        if (proofCnList.isNotEmpty() && totalAmount != roundedSumOfDnBillValue) {
            throw RequestException("Total amount is not equal to the sum of all DN Bill Values.$totalAmount != $roundedSumOfDnBillValue")
        }
        val company = companyRepo.getCompanyByTenant(proofCnList[0].tenant) ?: throw RequestException("Company mapping not found for ${proofCnList[0].tenant}")

        val proofCnNumber = proofCnList[0].debitNoteNumber
        val dn = debitNoteRepo.getByReturnNumber(mutableListOf(proofCnList[0].tenant),proofCnNumber, PartnerType.CUSTOMER,NoteTypes.valueOf(proofCnList[0].noteType) ,ReturnReferenceType.RETURN_ORDER, proofCnList[0].partnerDetailId.toLong())
        if (dn != null) {
            throw RequestException("Duplicate Proof cn, id : $proofCnNumber")
        }
        val partner = supplierProxy.supplier(null, proofCnList[0].partnerDetailId.toLong())
        if (partner.isEmpty()) {
            throw RequestException("No partner details found for pdi ${proofCnList[0].partnerDetailId}")
        }
        val items = mutableListOf<DebitNoteDetails>()
        proofCnList.forEach {

            var ucodeDetails = productUnitList[it.ucode]
            val debitNoteDetails = DebitNoteDetails(
                id = null,
                createdOn = now(),
                updatedOn = now(),
                itemId = 0,
                ucode = it.ucode,
                batch = it.batch,
                amount = it.mrp,
                quantity = it.quantity,
                epr = BigDecimal.ZERO,
                trueEpr = BigDecimal.ZERO,
                discountPercentage = null,
                discountAmount = it.discountAmount?.toBigDecimal(),
                cgst = it.cgst.toBigDecimal(),
                sgst = it.sgst.toBigDecimal(),
                igst = it.igst.toBigDecimal(),
                hsn = it.hsn,
                invoiceAmount = it.invoiceAmt.toBigDecimal(),
                netGstAmt = it.dnBillValue.toBigDecimal(),
                abtMrp = it.abtMrp.toBigDecimal(),
                name = (ucodeDetails?.name)?:"",
                taxableAmount = it.dnTaxableRate.toBigDecimal(),
                invoiceNumber = it.invoiceNumber,
                returnQty = it.returnQty,
                conversion = 0.0,
                cnTaxableValue = null,
                cnNetGST = null,
                cnBillValue = null,
                debitnoteId = null,
                expiry = null,
                cnMrpValue = null,
                schemePercent = it.schemePercent?.toBigDecimal(),
                quantityFree = it.quantityFree,
                ptr = BigDecimal.ZERO,
                schemeType = "",
                gst = it.dnNetGstAmt.toBigDecimal(),
                quantityOrdered = it.quantity,
                quantityScheme = BigDecimal.ZERO,
                totalInvoiceQty = it.quantity,
                invoiceTaxableValue = it.dnTaxableRate.toBigDecimal(),
                dnTaxableRate = it.dnTaxableRate.toBigDecimal(),
                debitNoteNumber = null,
                purchaseRate = null,
                purchaseTransactionNumber = null,
                creditnoteId = null,
                rejectedQty = null,
                acceptedQty = null,
                consumed = false,
                creditItemAmount = null,
                ucodeSchemePercent = BigDecimal.ZERO,
                invoiceDate = null,
                showGstSeparately = null
            )
            items.add(debitNoteDetails)
        }
        val documentType = when (proofCnList[0].noteType) {
            NoteTypes.SR_ACCEPTED.name -> DocumentType.DN_SR_ACCEPTED_B2B
            NoteTypes.SR_EXPIRED.name -> DocumentType.DN_SR_EXPIRED_B2B
            NoteTypes.SR_DAMAGED.name -> DocumentType.DN_SR_EXPIRED_B2B
            NoteTypes.NSR_ACCEPTED.name -> DocumentType.DEBIT_NOTE_NSR
            NoteTypes.NSR_EXPIRED.name -> DocumentType.DEBIT_NOTE_NSR
            NoteTypes.NSR_DAMAGED.name -> DocumentType.DEBIT_NOTE_NSR
            else -> throw RequestException("Invalid Note Type")
        }

        val debitNoteNumber = documentMasterService.getDocumentNumber("", company.companyCode, documentType)

        val debitNote = DebitNote(
            id = 0,
            createdOn =now(),
            updatedOn = now(),
            createdBy = "SYSTEM",
            updatedBy = "SYSTEM",
            supplierId = partner[0]?.partnerId!!,
            supplierName =partner[0]?.partnerName!!,
            amountReceivable = proofCnList[0].totalAmount.toBigDecimal(),
            status = NoteStatus.PENDING,
            noteType = NoteTypes.valueOf(proofCnList[0].noteType),
            debitItems = items,
            partnerId = partner[0]?.partnerId!!,
            partnerDetailId =  proofCnList[0].partnerDetailId.toLong(),
            tenant = proofCnList[0].tenant,
            type = PartnerType.CUSTOMER,
            client = InvoiceType.EASY_SOL,
            purchaseTransactionNumber = proofCnNumber,
            creditNoteId = null,
            creditNoteNumber = null,
            debitNoteNumber = debitNoteNumber,
            remarks = "Proof CN EasySol Migration",
            bkInvoices = mutableListOf(),
            apiVersion = APIVersionType.V1,
            logisticsPackageId = null,
            returnEventType = DebitNoteReturnEventType.REGULAR,
            returnReferenceType = ReturnReferenceType.RETURN_ORDER,
            roundOffAmount = null,
            baseDNAmount = null
        )
        val dnObj = debitNoteRepo.save(debitNote)
        items.forEach {
            it.debitnoteId = dnObj.id
            it.debitNoteNumber = dnObj.debitNoteNumber
        }
        debitNoteDetailRepo.saveAll(items)

        return proofCnList
            } finally {
                redisUtilityService.deleteFromRedis(lockKey)
            }
        } else {
            throw RequestException( "Another process is already migrating the proof CN with debitNoteNumber: $debitNoteNumber")
        }
    }

}