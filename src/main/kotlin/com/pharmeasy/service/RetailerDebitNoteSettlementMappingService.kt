package com.pharmeasy.service

import com.pharmeasy.data.RetailerDebitNoteSettlementMapping
import com.pharmeasy.repo.RetailerDebitNoteSettlementMappingRepo
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class RetailerDebitNoteSettlementMappingService {
    @Autowired
    private lateinit var retailerDebitNoteSettlementMappingRepo: RetailerDebitNoteSettlementMappingRepo

    fun saveAll(mapping: List<RetailerDebitNoteSettlementMapping>): List<RetailerDebitNoteSettlementMapping?> {
        return retailerDebitNoteSettlementMappingRepo.saveAll(mapping)
    }

    fun save(mapping: RetailerDebitNoteSettlementMapping): RetailerDebitNoteSettlementMapping {
        return retailerDebitNoteSettlementMappingRepo.save(mapping)
    }

    fun getRetailerDebitNoteSettlementMapping(
        retailerDebitNoteId: Long,
        settlementId: Long
    ): RetailerDebitNoteSettlementMapping {
        return retailerDebitNoteSettlementMappingRepo.findByRetailerDNIdAndSettlementId(
            retailerDebitNoteId,
            settlementId
        )
    }


    fun getRetailerDebitNoteSettlementBySettlementId(settlementId: Long): List<RetailerDebitNoteSettlementMapping> {
        return retailerDebitNoteSettlementMappingRepo.findBySettlementId(settlementId)
    }
}
