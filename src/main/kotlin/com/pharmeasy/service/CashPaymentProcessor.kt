package com.pharmeasy.service

import com.pharmeasy.model.ops.PartnerInfo
import com.pharmeasy.model.ops.PaymentInfo
import com.pharmeasy.service.abstracts.PaymentProcessor
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.time.LocalDate

@Service
class CashPaymentProcessor(
    private val draftReceiptService: DraftReceiptService,
    @Value("\${app.maxCashLimit}") private val cashThreshold: Double
) : PaymentProcessor(draftReceiptService) {
    /**
     * Validates the payment details for cash transactions.
     * This method can be overridden to provide specific validation logic for cash payments.
     * Check for cash payments crossing the two Lakhs (200000) threshold in a day
     *
     * @param payment The payment details to validate.
     */
    override fun validatePayment(payment: PaymentInfo, partnerInfo: PartnerInfo): String? {
        super.validatePayment(payment, partnerInfo)?.let { return it }

        val startDate = LocalDate.now().atStartOfDay()
        val endDate = LocalDate.now().plusDays(1).atStartOfDay()
        val totalCash =
            draftReceiptService.findCashTotalForPartner(partnerInfo.partnerDetailId, startDate, endDate)
        val remark =
            if (totalCash + payment.transactionAmount > cashThreshold) {
                "Cash payment cannot exceed $cashThreshold in a day"
            } else {
                null
            }
        return remark
    }
}
