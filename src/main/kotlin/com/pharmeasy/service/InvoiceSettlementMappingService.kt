package com.pharmeasy.service

import com.pharmeasy.data.BkInvoice
import com.pharmeasy.data.InvoiceSettlement
import com.pharmeasy.repo.InvoiceSettlementRepo
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class InvoiceSettlementMappingService @Autowired constructor(
    private val invoiceSettlementRepo: InvoiceSettlementRepo
) {
    companion object {
        private val log = LoggerFactory.getLogger(InvoiceSettlementMappingService::class.java)
    }

    fun getInvoicesForSettlement(settlementId: Long): List<BkInvoice>{
        return invoiceSettlementRepo.getInvoicesForSettlement(settlementId)
    }

    fun getSettlementInvoiceBySettlementId(settlementId: Long): List<InvoiceSettlement> {
        return invoiceSettlementRepo.getSettlementInvoiceBySettlementId(settlementId)
    }
}