package com.pharmeasy.service

import com.pharmeasy.data.*
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.InvoiceStatus
import com.pharmeasy.model.PaginationDto
import com.pharmeasy.model.User
import com.pharmeasy.model.advancepayment.AdvancePaymentDto
import com.pharmeasy.model.advancepayment.PaymentRefItem
import com.pharmeasy.model.advancepayment.UpdateAdvancePaymentDto
import com.pharmeasy.model.ops.RIOPaymentDTO
import com.pharmeasy.model.rioDraftInvoice.*
import com.pharmeasy.proxy.SupplierProxy
import com.pharmeasy.proxy.UserProxy
import com.pharmeasy.repo.*
import com.pharmeasy.repo.read.BkInvoiceReadRepo
import com.pharmeasy.repo.read.RioDraftInvoiceReadRepo
import com.pharmeasy.stream.InvoiceSettlementUpdateHandler
import com.pharmeasy.type.*
import com.pharmeasy.util.DateUtils.getDateFromEpochStamp
import com.pharmeasy.util.RioDraftFileUtils
import com.pharmeasy.util.UUIDUtil
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVPrinter
import org.apache.kafka.common.protocol.types.Field.Bool
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.io.ByteArrayOutputStream
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalDateTime.now

@Service
class DigitalReceiptsWritebackService {
    companion object {
        private val log = LoggerFactory.getLogger(DigitalReceiptsWritebackService::class.java)
        private const val ADVANCE_PAYMENT = "ADVANCE_PAYMENT"
    }

    @Autowired
    private lateinit var rioDraftInvoiceRepo: RioDraftInvoiceRepo

    @Autowired
    private lateinit var bkInvoiceReadRepo: BkInvoiceReadRepo

    @Autowired
    private lateinit var invoiceSettlementUpdateHandler: InvoiceSettlementUpdateHandler

    @Autowired
    private lateinit var settlementService: SettlementService

    @Autowired
    private lateinit var companyService: CompanyService

    @Autowired
    private lateinit var digitalReceiptsWritebackLogsRepo: DigitalReceiptsWritebackLogsRepo

    @Autowired
    private lateinit var advancePaymentService: AdvancePaymentService

    @Autowired
    private lateinit var receiptService: ReceiptService

    @Autowired
    private lateinit var s3FileUtilityService: S3FileUtilityService

    @Autowired
    private lateinit var supplierProxy: SupplierProxy

    @Autowired
    private lateinit var notificationMailService: NotificationMailService

    @Autowired
    private lateinit var rioDraftInvoiceReadRepo: RioDraftInvoiceReadRepo

    @Autowired
    private lateinit var retailerDebitNoteRepo: RetailerDebitNoteRepo

    @Autowired
    private lateinit var invoiceSettlementRepo: InvoiceSettlementRepo

    @Autowired
    private lateinit var slipService: SlipService

    @Autowired
    private lateinit var digitalReceiptsAdvanceInvoiceMappingRepo: DigitalReceiptsAdvanceInvoiceMappingRepo

    @Autowired
    private lateinit var userProxy: UserProxy

    @Autowired
    private lateinit var invoiceService: InvoiceService

    @Transactional
    fun createDraftReceipt(rioPaymentDTO: RIOPaymentDTO, createdBy: String) {
        var bkInvoice: RioBkInvoiceData? = null
        var debitNote: RetailerDebitNote? = null
        val data = rioDraftInvoiceRepo.getExistingInvoices(rioPaymentDTO.retailerTxnId, rioPaymentDTO.invoiceNumber)
        val tenant = companyService.getTenantByPDI(rioPaymentDTO.distributorId?:throw RequestException("Distributor id ${rioPaymentDTO.distributorId} does not exist")).tenant
        if (data.isNullOrEmpty()) {
            when (rioPaymentDTO.type) {
                CreationType.INVOICE -> {
                    bkInvoice =
                        bkInvoiceReadRepo.getBkInvoiceData(
                            rioPaymentDTO.distributorInvoiceId,
                            rioPaymentDTO.invoiceNumber,
                            tenant,
                            rioPaymentDTO.retailerFrontEndPartyCode?.toLong()
                        )
                }

                CreationType.DEBIT_NOTE -> {
                    debitNote = retailerDebitNoteRepo.getRetailerDebitNoteByDebitNoteNumber(rioPaymentDTO.invoiceNumber)
                }
            }
            if (bkInvoice != null || debitNote != null || (rioPaymentDTO.category == ADVANCE_PAYMENT)) {
                val rioPaymentType = rioPaymentDTO.retailerTxnType
                val txnStatus = rioPaymentDTO.retailerTxnStatus
                val rioPaymentMode = rioPaymentDTO.paymentMode
                var paymentMode: PaymentMode? = null
                var paymentType: PaymentType? = null
                var transactionStatus: TransactionStatus? = null

                paymentType = when (rioPaymentType?.toUpperCase()) {
                    "CHEQUE" -> PaymentType.CHEQUE
                    "CASH" -> PaymentType.CASH
                    "NEFT" -> PaymentType.NEFT
                    "DIRECT PAYMENT" -> PaymentType.DIRECT_PAYMENT
                    else -> PaymentType.OTHERS
                }
                when (txnStatus?.toUpperCase()) {
                    "SUCCESS" -> transactionStatus = TransactionStatus.SUCCESS
                    "FAILURE" -> transactionStatus = TransactionStatus.FAILURE
                    "PENDING" -> transactionStatus = TransactionStatus.PENDING
                }

                if (rioPaymentMode!!.toUpperCase() == "DIGITAL_RECEIPT") {
                    paymentMode = PaymentMode.DIGITAL_RECEIPT
                }

                val checkExistingData = rioDraftInvoiceRepo.getInvoicesByRetailerTxnIdAndDistributorInvoiceId(
                    rioPaymentDTO.retailerTxnId,
                    rioPaymentDTO.distributorInvoiceId
                )
                val existingCheque = findChequeWithSameDetails(rioPaymentDTO)
                val existingNeft = findDuplicateNeftTransaction(rioPaymentDTO)
                if (checkExistingData.isNullOrEmpty()) {
                    rioDraftInvoiceRepo.save(
                        DigitalReceiptsWriteback(
                            id = null,
                            createdOn = now(),
                            updatedOn = null,
                            createdBy = createdBy,
                            updatedBy = null,
                            retailerTxnId = rioPaymentDTO.retailerTxnId,
                            retailerTxnDate = getDateFromEpochStamp(rioPaymentDTO.retailerTxnDate),
                            retailerName = rioPaymentDTO.retailerName,
                            retailerPartyCode = rioPaymentDTO.retailerPartyCode,
                            retailerFrontEndPartyCode = rioPaymentDTO.retailerFrontEndPartyCode,
                            distributorInvoiceId = rioPaymentDTO.distributorInvoiceId,
                            distributorInvoiceAmount = rioPaymentDTO.distributorInvoiceAmount,
                            distributorInvoiceOutstandingAmount = rioPaymentDTO.distributorInvoiceOutstandingAmount,
                            retailerTxnAmount = rioPaymentDTO.retailerTxnAmount,
                            distributorInvoiceDueDate = getDateFromEpochStamp(rioPaymentDTO.distributorInvoiceDate),
                            retailerTxnType = paymentType,
                            retailerTxnStatus = transactionStatus!!,
                            advanceAmount = rioPaymentDTO.advanceAmount,
                            paymentMode = paymentMode!!,
                            invoicePrefix = rioPaymentDTO.invoicePrefix,
                            distributorInvoiceDate = getDateFromEpochStamp(rioPaymentDTO.distributorInvoiceDate),
                            category = rioPaymentDTO.category,
                            initiatedBy = rioPaymentDTO.initiatedBy,
                            invoiceNumber = rioPaymentDTO.invoiceNumber,
                            salesmanName = rioPaymentDTO.salesmanName,
                            salesmanId = rioPaymentDTO.salesmanId,
                            chequeNo = rioPaymentDTO.chequeNo,
                            chequeDate = getDateFromEpochStamp(rioPaymentDTO.chequeDate),
                            neftId = rioPaymentDTO.neftId,
                            invoiceStatus = if (existingCheque || existingNeft) {
                                RioDraftInvoiceStatusType.REJECTED
                            } else {
                                RioDraftInvoiceStatusType.DRAFT
                            },
                            closureStatus = if (existingCheque || existingNeft) {
                                RioDraftInvoiceClosureStatusType.CLOSE
                            } else {
                                RioDraftInvoiceClosureStatusType.OPEN
                            },
                            remarks = if (existingCheque) {
                                "Duplicate cheque received for retailer transaction id ${rioPaymentDTO.retailerTxnId}."
                            } else if(existingNeft){
                                "Duplicate NEFT transaction received for retailer transaction id ${rioPaymentDTO.retailerTxnId}."
                                }else{
                                    ""
                            },
                            bkInvoiceId = bkInvoice?.bkInvoiceId ?: 0,
                            tenant = tenant,
                            isValid = if (existingCheque || existingNeft) {
                                false
                            } else {
                                true
                            },
                            editedRetailerTxnAmount = 0.0,
                            distributorPdi = bkInvoice?.distributorPdi ?: rioPaymentDTO.distributorId,
                            apiVersion = bkInvoice?.apiVersion ?: APIVersionType.V1,
                            source = Source.RIO,
                            bankName = rioPaymentDTO.bankName,
                            paymentType = rioPaymentType,
                            retailerTotalTxnAmount = rioPaymentDTO.retailerTotalTxnAmount,
                            eventType = rioPaymentDTO.type?:CreationType.INVOICE,
                            retailerDebitNoteId = debitNote?.id ?: 0,
                            bankDepositSlipNo = rioPaymentDTO.bankDepositSlipNo,
                            isBankDeposit = rioPaymentDTO.isBankDeposit ?: false
                        )
                    )
                }
                try {
                    //update slip status for invoice
                    if (bkInvoice != null) {
                        log.debug("Updating slip status for bkInvoice: $bkInvoice")
                        slipService.statusChangeSlip(
                            listOf(rioPaymentDTO.distributorInvoiceId!!),
                            SlipStatus.DRAFT,
                            "Draft-${paymentType.name}"
                        )
                    }
                } catch (requestException: RequestException) {
                    log.error("Failed to close slips due to request error: ${requestException.message}")
                } catch (e: Exception) {
                    log.error("Failed to close slips due to an unexpected error: ${e.message}", e)
                }
            } else {
                log.error("Error while saving Rio collection event for transaction id ${rioPaymentDTO.retailerTxnId}.")
            }
        } else {
            log.info("Data with same retailer transaction id: ${rioPaymentDTO.retailerTxnId} and invoice number: ${rioPaymentDTO.invoiceNumber} is already available.")
        }
    }

    fun getDraftInvoice(
        page: Int?,
        size: Int?,
        tenant: String,
        paymentType: PaymentType,
        paymentStatus: TransactionStatus?,
        invoiceNumber: String?,
        partnerDetailId: Long?,
        salesmanName: String?,
        paymentReferenceNum: String?,
        ds: String? = null,
        retailerName: String?,
        partnerIds: List<Long>?,
        from: LocalDate,
        to: LocalDate
    ): PaginationDto {
        val pagination = PageRequest.of(page ?: 0, size ?: 10)
        val tenants = companyService.findTenants(ds ?: tenant)
        var neftId: String? = null
        var chequeNo: String? = null
        var retailerTxnNum: String? = null
        var fromDate: LocalDateTime = from.atStartOfDay()
        var toDate: LocalDateTime = to.atTime(23, 59, 59)
        when (paymentType) {
            PaymentType.CHEQUE -> chequeNo = paymentReferenceNum
            PaymentType.NEFT -> neftId = paymentReferenceNum
            PaymentType.DIRECT_PAYMENT -> {
                chequeNo = paymentReferenceNum
                neftId = paymentReferenceNum
            }

            else -> retailerTxnNum = paymentReferenceNum
        }
        if (tenants.isEmpty()) throw RequestException("Your tenant: $tenant is not mapped to any Company")

        val res = when {
            partnerIds.isNullOrEmpty() -> {
                rioDraftInvoiceRepo.getRioDraftInvoiceDataWithoutPartnerId(
                    tenants,
                    paymentType,
                    paymentStatus,
                    invoiceNumber,
                    partnerDetailId,
                    salesmanName,
                    chequeNo,
                    neftId,
                    retailerName,
                    fromDate,
                    toDate,
                    pagination
                )
            }

            else -> {
                rioDraftInvoiceRepo.getRioDraftInvoiceDataWithPartnerId(
                    tenants,
                    paymentType,
                    paymentStatus,
                    invoiceNumber,
                    partnerDetailId,
                    salesmanName,
                    chequeNo,
                    neftId,
                    retailerName,
                    partnerIds!!,
                    fromDate,
                    toDate,
                    pagination
                )
            }
        }

        res.content.forEach {
            it.invoiceList = getInvoiceListByTransactionTypeAndTransactionNumberAndTenant(
                paymentType!!,
                it.paymentRefNum!!,
                tenant,
                ds,
                it.partnerDetailId,
                it.retailerTxnId
            )
            if (it.invoiceList.isNullOrEmpty()) {
                it.advanceAmount = it.receiptAmount
                it.totalInvoices = 0L
            } else {
                it.advanceAmount = getInvoiceAdvanceAmount(it.invoiceList!!)
            }
        }
        return PaginationDto(res.totalElements, res.totalPages, res.hasPrevious(), res.hasNext(), res.content)
    }

    @Transactional
    fun handleInvoiceProcess(
        rioDraftInvoiceBulkActionDto: RioDraftInvoiceBulkActionDto,
        tenant: String,
        userName: String,
        userEmail: String,
        ds: String? = null
    ) {
        var rioSettlementEventData = processInvoices(rioDraftInvoiceBulkActionDto, userName, tenant, ds, userEmail)
        sendSettlementEventToRio(rioSettlementEventData)
    }

    @Transactional
    fun processInvoices(
        rioDraftInvoiceBulkActionDto: RioDraftInvoiceBulkActionDto,
        userName: String,
        tenant: String,
        ds: String? = null,
        userEmail: String
    ): MutableList<MutableList<RioDraftSettlementDto>> {
        val tenants = companyService.findTenants(ds ?: tenant)
        if (tenants.isEmpty()) throw RequestException("Your tenant: $tenant is not mapped to any Company")

        var invoicesToBeSettled: MutableList<RioDraftSettlementDto> = mutableListOf()
        val rioSettlementEventData: MutableList<MutableList<RioDraftSettlementDto>> = mutableListOf()
        val referenceNumbers: MutableList<Pair<String, String>> = mutableListOf()
        val referenceNumberInvoiceMap: MutableMap<Pair<String, String>, DraftInvoiceReferenceMap> = mutableMapOf()

        rioDraftInvoiceBulkActionDto.draftInvoiceReference.forEach {
            val pair = Pair(it.referenceNumber, it.retailerTxnId)
            referenceNumbers.add(pair)
            referenceNumberInvoiceMap[pair] = it
        }

        if (rioDraftInvoiceBulkActionDto.action != RioDraftInvoiceStatusType.REJECTED) {
            validateInvoiceStatus(referenceNumbers, rioDraftInvoiceBulkActionDto.action, referenceNumberInvoiceMap)
        }

        referenceNumbers.forEach {
            if (referenceNumberInvoiceMap[it]?.invoiceAdvanceAmountMap.isNullOrEmpty()) {
                //when no invoice settled with advance amount.
                checkAndHandleAdvancePayments(
                    it.first,
                    it.second,
                    referenceNumberInvoiceMap,
                    userName,
                    userEmail,
                    ds,
                    rioDraftInvoiceBulkActionDto.action,
                    rioDraftInvoiceBulkActionDto.remarks,
                    referenceNumberInvoiceMap[it]!!
                )
            }
            if (referenceNumberInvoiceMap[it]?.invoiceAdvanceAmountMap?.isNotEmpty() == true) {
                //inserted invoices mapped with advance payment in new table
                mapInvoiceWithAdvanceAmount(
                    referenceNumberInvoiceMap[it]?.invoiceAdvanceAmountMap!!,
                    it.first,
                    referenceNumberInvoiceMap[it]?.retailerTxnId!!,
                    userName
                )
            }
            val invoices = getInvoiceListByTransactionTypeAndTransactionNumberAndTenant(
                referenceNumberInvoiceMap[it]?.transactionType!!,
                it.first,
                tenant,
                ds,
                referenceNumberInvoiceMap[it]?.partnerDetailId,
                it.second
            )
            //invoices variable will be empty when complete advance payment will be processed for which checker is settling invoices
            if (invoices?.isNotEmpty() == true || referenceNumberInvoiceMap[it]?.invoiceAdvanceAmountMap?.isNotEmpty() == true) {
                if (invoices?.isNotEmpty() == true) {
                    invoicesToBeSettled = updateDraftInvoice(
                        invoices,
                        userName,
                        rioDraftInvoiceBulkActionDto.remarks,
                        rioDraftInvoiceBulkActionDto.action,
                        referenceNumberInvoiceMap[it]!!
                    )
                }
                if (referenceNumberInvoiceMap[it]?.invoiceAdvanceAmountMap?.isNotEmpty() == true) {
                    //mapping advance invoices for settlement
                    invoicesToBeSettled = mapAdvanceInvoiceForSettlement(
                        referenceNumberInvoiceMap[it]?.invoiceAdvanceAmountMap!!,
                        it.first,
                        referenceNumberInvoiceMap[it]?.retailerTxnId!!,
                        invoicesToBeSettled,
                        rioDraftInvoiceBulkActionDto.action,
                        userName,
                        rioDraftInvoiceBulkActionDto.remarks,
                        referenceNumberInvoiceMap[it]!!
                    )
                }
                if (rioDraftInvoiceBulkActionDto.action != RioDraftInvoiceStatusType.REJECTED) {
                    val settlement = settleInvoice(
                        invoicesToBeSettled,
                        userName,
                        referenceNumberInvoiceMap[it]?.updatedReferenceNum ?: it.first,
                        referenceNumberInvoiceMap[it]?.updatePaymentType
                            ?: referenceNumberInvoiceMap[it]?.transactionType!!
                    )
                    rioSettlementEventData.add(invoicesToBeSettled)
                    createAdvancePaymentFromDraftInvoice(it.first, it.second, settlement, userName, userEmail, ds)
                }
            }
        }

        return rioSettlementEventData
    }

    fun getDraftInvoiceByStatus(
        page: Int?,
        size: Int?,
        tenant: String,
        paymentType: PaymentType?,
        paymentStatus: TransactionStatus?,
        invoiceId: String?,
        status: RioDraftInvoiceStatusType
    ): PaginationDto {
        val pagination = PageRequest.of(page ?: 0, size ?: 10)
        val res = rioDraftInvoiceRepo.getRioDraftInvoiceDataByStatus(
            paymentType,
            paymentStatus,
            invoiceId,
            tenant,
            status,
            pagination
        )
        return PaginationDto(res.totalElements, res.totalPages, res.hasPrevious(), res.hasNext(), res.content)
    }

    @Transactional
    fun settleInvoice(
        invoicesToBeSettled: MutableList<RioDraftSettlementDto>,
        userName: String,
        paymentRefNum: String,
        retailerTxnType: PaymentType
    ): Settlement {
        val bkInvoices: MutableList<BkInvoice> = mutableListOf()
        val retailerDebitNote: MutableList<RetailerDebitNote> = mutableListOf()
        var paymentDate: LocalDate = LocalDate.now()
        var settledAmount = 0.0
        var supplierId: Long? = null
        var supplierName: String? = null
        var totalAmount = 0.0
        var partnerDetailId: Long? = null
        var partnerId: Long? = null
        var tenant: String? = null
        var chequeDate: LocalDate? = null
        var bankName: String? = null
        invoicesToBeSettled.forEach {
            var bkInvoice: BkInvoice? = null
            var debitNote: RetailerDebitNote? = null
            var debitNoteCopy: RetailerDebitNote? = null
            when (it.eventType) {
                CreationType.INVOICE -> bkInvoice = bkInvoiceReadRepo.getOne(it.referenceId)
                CreationType.DEBIT_NOTE -> {
                    debitNote = retailerDebitNoteRepo.getOne(it.referenceId)
                    debitNoteCopy = debitNote.copy()
                }
            }

            partnerDetailId = bkInvoice?.partnerDetailId ?: debitNoteCopy?.partnerDetailId
            val draftInvoice = rioDraftInvoiceRepo.getOne(it.draftInvoiceId)
            if (draftInvoice.retailerTxnType == PaymentType.CASH) {
                val cashSettlementFlag = settlementService.checkCashSettlementAmount(
                    partnerDetailId
                        ?: throw RequestException("Cannot check cash limit for partner detail id $partnerDetailId"),
                    draftInvoice.retailerTxnAmount!!
                )
                if (!cashSettlementFlag) {
                    throw RequestException("Settlement amount exceeds daily cash limit")
                }
            }
            if (bkInvoice != null) {
                bkInvoice.status = checkInvoiceStatusForSettlement(bkInvoice, draftInvoice)
                bkInvoice.paidAmount = checkInvoicePaidAmountForSettlement(bkInvoice, draftInvoice)
                bkInvoice.updatedOn = now()
                bkInvoice.updatedBy = userName
                bkInvoices.add(bkInvoice)
            } else if (debitNoteCopy != null) {
                val debitNoteRemainingAmount = debitNoteCopy.amount - debitNoteCopy.amountReceived
                if (draftInvoice.retailerTxnAmount!! >= debitNoteRemainingAmount) {
                    debitNoteCopy.status = InvoiceStatus.PAID
                } else if (draftInvoice.retailerTxnAmount!! < debitNoteRemainingAmount) {
                    debitNoteCopy.status = InvoiceStatus.PARTIAL_PAID
                }
                debitNoteCopy.amountReceived += draftInvoice.retailerTxnAmount!!
                debitNoteCopy.updatedBy = userName
                debitNoteCopy.updatedOn = now()
                retailerDebitNote.add(debitNoteCopy)
            }

            val amount = bkInvoice?.amount ?: debitNoteCopy?.amount
            paymentDate = draftInvoice.retailerTxnDate ?: LocalDate.now()
            settledAmount += if (bkInvoice != null) {
                checkInvoiceSettledAmountForSettlement(bkInvoice!!, draftInvoice)
            } else {
                draftInvoice.retailerTxnAmount!!
            }
            supplierId = bkInvoice?.supplierId ?: debitNoteCopy?.partnerId
            supplierName = bkInvoice?.supplierName ?: debitNoteCopy?.partnerName
            totalAmount += amount!!
            partnerDetailId = partnerDetailId
            partnerId = bkInvoice?.partnerId ?: debitNoteCopy?.partnerId
            tenant = bkInvoice?.tenant ?: debitNoteCopy?.tenant
            chequeDate = draftInvoice.chequeDate
            bankName = draftInvoice.bankName

        }
        val obj = Settlement(
            0,
            null,
            null,
            null,
            supplierId!!,
            supplierName,
            totalAmount,
            settledAmount,
            "Settled",
            null,
            bkInvoices,
            mutableListOf(),
            retailerTxnType,
            paymentRefNum,
            LocalDate.now(),
            partnerId,
            partnerDetailId,
            PartnerType.CUSTOMER,
            tenant!!,
            chequeDate,
            null,
            bankName,
            false,
            false,
            mutableListOf(),
            mutableListOf(),
            false,
            null,
            AdvancePaymentSource.RIO_COLLECTIONS,
            retailerDebitNote,
            UUIDUtil.generateUuid()
        )
        return settlementService.save("SYSTEM", obj, null)

    }

    fun getSalesmanList(tenant: String, ds: String? = null): List<String> {
        var tenants = companyService.findTenants(ds ?: tenant)
        if (tenants.isEmpty()) throw RequestException("Your tenant: $tenant is not mapped to any Company")
        return rioDraftInvoiceRepo.getSalesmanList(tenants)
    }


    fun getTransactionNumbers(
        paymentType: PaymentType?,
        salesManName: String?,
        transactionNumber: String?,
        retailerName: String?,
        tenant: String,
        ds: String? = null
    ): List<String> {
        var tenants = companyService.findTenants(ds ?: tenant)
        if (tenants.isEmpty()) throw RequestException("Your tenant: $tenant is not mapped to any Company")
        return rioDraftInvoiceRepo.getTransactionNumbers(
            paymentType,
            salesManName,
            transactionNumber,
            retailerName,
            tenants
        ).distinct()
    }

    fun getInvoiceListByTransactionTypeAndTransactionNumberAndTenant(
        txnType: PaymentType?,
        paymentRefNum: String,
        tenant: String,
        ds: String? = null,
        partnerDetailId: Long?,
        retailerTxnId: String?
    ): List<DigitalReceiptsWriteback>? {
        val tenants = companyService.findTenants(ds ?: tenant)
        if (tenants.isEmpty()) throw RequestException("Your tenant: $tenant is not mapped to any Company")
        return rioDraftInvoiceRepo.getDraftInvoiceList(
            tenants,
            paymentRefNum,
            txnType,
            partnerDetailId.toString(),
            ADVANCE_PAYMENT,
            retailerTxnId
        )
    }


    fun validateInvoiceStatus(
        referenceNumbers: MutableList<Pair<String, String>>,
        action: RioDraftInvoiceStatusType,
        referenceNumberInvoiceMap: MutableMap<Pair<String, String>, DraftInvoiceReferenceMap>
    ) {
        val bkInvoiceIds = mutableListOf<Long>()
        val draftInvoiceMap: MutableMap<Long, DigitalReceiptsWriteback> = mutableMapOf()
        val errorList = mutableListOf<String>()
        val editedInvoice: MutableMap<String, Double> = mutableMapOf()
        val txnNum: MutableList<String> = mutableListOf()
        referenceNumbers.forEach {
            txnNum.add(it.first)
        }
        val invoices = rioDraftInvoiceRepo.getDraftInvoicesByRefNumAndTxnType(txnNum)

        invoices.forEach {
            val refNum = getInvoiceReferenceNumber(it)
            if (it.category != ADVANCE_PAYMENT) {
                val pair = Pair(refNum, it.retailerTxnId)
                val referenceMap = referenceNumberInvoiceMap[pair]
                if (it.retailerTxnId == referenceMap?.retailerTxnId) {
                    val txnType = referenceMap?.transactionType
                    val pdi = referenceMap?.partnerDetailId
                    val draftInvoicePdi = it.retailerFrontEndPartyCode?.toLong()
                    if (it.retailerTxnType == txnType && draftInvoicePdi == pdi) {
                        bkInvoiceIds.add(it.bkInvoiceId!!)
                        draftInvoiceMap[it.bkInvoiceId!!] = it
                    }
                }
            }
            if (action == RioDraftInvoiceStatusType.APPROVED) {
                if ((it.retailerTxnType != PaymentType.CHEQUE && it.retailerTxnDate == null) || (it.retailerTxnType == PaymentType.CHEQUE && it.chequeDate == null)) {
                    errorList.add("Transaction date is null for invoice number: ${it.invoiceNumber} in transaction reference number $refNum. Please check.")
                }
                if (it.retailerTxnType == PaymentType.CHEQUE && it.bankName.isNullOrEmpty()) {
                    errorList.add("Bank name required for cheque number: ${it.chequeNo} in transaction reference number $refNum. Please check.")
                }
            }
        }

        val bkInvoices = bkInvoiceReadRepo.getByIds(bkInvoiceIds)

        bkInvoices.forEach {
            val draftInvoice = draftInvoiceMap[it.id]
            val refNum = getInvoiceReferenceNumber(draftInvoice!!)
            val retailerTxnAmount =
                getRetailerTransactionAmount(action, editedInvoice, draftInvoice, it.invoiceNum!!) ?: 0.0

            if (it.status == InvoiceStatus.PAID && draftInvoice.eventType == CreationType.INVOICE) {
                errorList.add("Invoice number: ${it.invoiceNum} in Transaction Reference Number $refNum is already settled on: ${it.settledOn} with settlement number: ${it.settlementNumber}. Please check.")
            } else if (it.status != InvoiceStatus.PAID && draftInvoice.eventType == CreationType.INVOICE) {
                val bkInvoiceRemainingAmount = it.amount - it.paidAmount + 1
                if (retailerTxnAmount > bkInvoiceRemainingAmount) {
                    errorList.add(
                        "Invoice number: ${it.invoiceNum} in Transaction Reference Number $refNum is in Partial Paid status with settlement number: ${it.settlementNumber}. Paid Amount: ${
                            "%.2f".format(
                                it.paidAmount
                            )
                        }, Remaining Amount: ${"%.2f".format(bkInvoiceRemainingAmount)}. Please check."
                    )
                }
            }
        }

        if (errorList.isNotEmpty()) {
            val result = errorList.joinToString(separator = ", ")
            throw RequestException(result)
        }
    }

    @Transactional
    fun updateDraftInvoice(
        invoices: List<DigitalReceiptsWriteback>,
        userName: String,
        remarks: String?,
        status: RioDraftInvoiceStatusType,
        referenceNumberInvoiceMapData: DraftInvoiceReferenceMap
    ): MutableList<RioDraftSettlementDto> {
        val settledInvoices: MutableList<RioDraftSettlementDto> = mutableListOf()
        if (status == RioDraftInvoiceStatusType.EDITED) {
            return updateEditedDraftInvoices(invoices, userName, remarks, status, referenceNumberInvoiceMapData)
        }
        invoices.forEach {
            if (it.retailerTxnId == referenceNumberInvoiceMapData.retailerTxnId) {
                it.updatedBy = userName
                it.updatedOn = now()
                it.invoiceStatus = status
                it.closureStatus = RioDraftInvoiceClosureStatusType.CLOSE
                it.remarks = remarks
                if (it.category != ADVANCE_PAYMENT)
                    if (it.bkInvoiceId == 0L) {
                        settledInvoices.add(
                            RioDraftSettlementDto(
                                it.retailerDebitNoteId!!,
                                it.id!!,
                                it.eventType ?: CreationType.INVOICE
                            )
                        )
                    } else {
                        settledInvoices.add(
                            RioDraftSettlementDto(
                                it.bkInvoiceId!!,
                                it.id!!,
                                it.eventType ?: CreationType.INVOICE
                            )
                        )
                    }
                try {
                    if (status == RioDraftInvoiceStatusType.REJECTED) {
                        slipService.statusChangeSlip(
                            listOf(it.distributorInvoiceId!!),
                            SlipStatus.OUT_FOR_COLLECTION,
                            status.name
                        )
                    }
                } catch (requestException: RequestException) {
                    log.error("Failed to close slips due to request error: ${requestException.message}")
                } catch (e: Exception) {
                    log.error("Failed to close slips due to an unexpected error: ${e.message}", e)
                }
            }
        }
        rioDraftInvoiceRepo.saveAll(invoices)
        return settledInvoices
    }

    @Transactional
    fun updateEditedDraftInvoices(
        invoices: List<DigitalReceiptsWriteback>,
        userName: String,
        remarks: String?,
        status: RioDraftInvoiceStatusType,
        referenceNumberInvoiceMapData: DraftInvoiceReferenceMap
    ): MutableList<RioDraftSettlementDto> {
        val settledInvoices: MutableList<RioDraftSettlementDto> = mutableListOf()
        val editedInvoice: MutableMap<String, Double> = mutableMapOf()
        referenceNumberInvoiceMapData.invoiceAmountMap.forEach {
            editedInvoice[it.invoiceNumber!!] = it.updatedRetailerTxnAmount ?: it.retailerTxnAmount!!
        }

        val editedLogs: MutableList<DigitalReceiptsWritebackLogs> = mutableListOf()
        invoices.forEach {
            if (it.retailerTxnId == referenceNumberInvoiceMapData.retailerTxnId) {
                val referenceNumber = getInvoiceReferenceNumber(it)
                val txnDate = getRetailerTransactionDate(it)
                editedLogs.add(
                    DigitalReceiptsWritebackLogs(
                        0L,
                        it.id!!,
                        it.retailerTxnType,
                        referenceNumber,
                        it.retailerTxnAmount!!,
                        txnDate,
                        now()
                    )
                )
                val amount = editedInvoice[it.invoiceNumber] ?: it.retailerTxnAmount!!
                val paymentType = referenceNumberInvoiceMapData.updatePaymentType ?: it.retailerTxnType
                val referenceNum = referenceNumberInvoiceMapData.updatedReferenceNum ?: referenceNumber
                val paymentDate = referenceNumberInvoiceMapData.updatedPaymentDate ?: txnDate
                val bankName = referenceNumberInvoiceMapData.updatedBankName ?: it.bankName
                it.retailerTxnAmount = amount
                it.retailerTxnType = paymentType
                it.retailerTotalTxnAmount = referenceNumberInvoiceMapData.updatedAmount ?: it.retailerTotalTxnAmount
                if (it.category == ADVANCE_PAYMENT && referenceNumberInvoiceMapData.invoiceAmountMap.isEmpty()) {
                    it.advanceAmount = referenceNumberInvoiceMapData.updatedAmount
                }
                when (paymentType) {
                    PaymentType.CHEQUE -> it.chequeDate = paymentDate
                    else -> it.retailerTxnDate = paymentDate
                }
                when (it.retailerTxnType) {
                    PaymentType.NEFT -> it.neftId = referenceNum
                    PaymentType.CHEQUE -> it.chequeNo = referenceNum
                    else -> it.retailerTxnId
                }
                it.updatedBy = userName
                it.updatedOn = now()
                it.invoiceStatus = status
                it.closureStatus = RioDraftInvoiceClosureStatusType.CLOSE
                it.remarks = remarks
                it.bankName = bankName
                if (it.category != ADVANCE_PAYMENT)
                    settledInvoices.add(
                        RioDraftSettlementDto(
                            it.bkInvoiceId!!,
                            it.id!!,
                            it.eventType ?: CreationType.INVOICE
                        )
                    )
            }
        }
        saveEditedDraftInvoiceLogs(editedLogs)
        rioDraftInvoiceRepo.saveAll(invoices)
        return settledInvoices
    }

    fun sendSettlementEventToRio(settledInvoices: MutableList<MutableList<RioDraftSettlementDto>>) {
        settledInvoices.forEach { draftInvoices ->
            draftInvoices.forEach {
                invoiceSettlementUpdateHandler.createSettlementConsumer(it.referenceId, it.eventType)
            }
        }
    }

    fun getRetailerList(tenant: String, ds: Boolean): List<String> {
        val tenants = companyService.findTenants(tenant, ds)
        if (tenants.isEmpty()) throw RequestException("Your tenant: $tenant is not mapped to any Company")
        return rioDraftInvoiceRepo.getRetailerList(tenants)
    }

    fun saveEditedDraftInvoiceLogs(editedLogs: MutableList<DigitalReceiptsWritebackLogs>) {
        digitalReceiptsWritebackLogsRepo.saveAll(editedLogs)
    }

    fun getInvoiceReferenceNumber(draftInvoice: DigitalReceiptsWriteback): String {
        return when (draftInvoice.retailerTxnType) {
            PaymentType.NEFT, PaymentType.CHEQUE -> draftInvoice.neftId ?: draftInvoice.chequeNo!!
            PaymentType.DIRECT_PAYMENT -> draftInvoice.neftId ?: draftInvoice.chequeNo ?: draftInvoice.retailerTxnId!!
            else -> draftInvoice.retailerTxnId!!
        }
    }

    fun getRetailerTransactionAmount(
        action: RioDraftInvoiceStatusType,
        editedInvoice: MutableMap<String, Double>,
        draftInvoice: DigitalReceiptsWriteback,
        invoiceNum: String
    ): Double? {
        return when (action) {
            RioDraftInvoiceStatusType.APPROVED -> draftInvoice.retailerTxnAmount
            RioDraftInvoiceStatusType.EDITED -> editedInvoice[invoiceNum]
            else -> throw Exception("Invalid User Action")
        }
    }

    fun getRetailerTransactionDate(draftInvoice: DigitalReceiptsWriteback): LocalDate? {
        return when (draftInvoice.retailerTxnType) {
            PaymentType.CHEQUE -> draftInvoice.chequeDate
            else -> draftInvoice.retailerTxnDate
        }
    }

    fun getInvoiceAdvanceAmount(draftInvoice: List<DigitalReceiptsWriteback>): Double {
        val retailerTotalTxnAmount = if (draftInvoice.isNotEmpty()) {
            draftInvoice[0].retailerTotalTxnAmount
        } else {
            0.0
        }
        var retailerTxnAmount = 0.0
        val draftInvoiceId: MutableList<Long> = mutableListOf()
        draftInvoice.forEach {
            draftInvoiceId.add(it.id!!)
        }
        val advanceAmountSettled =
            digitalReceiptsAdvanceInvoiceMappingRepo.getAmountByDraftInvoiceIds(draftInvoiceId) ?: 0.00

        draftInvoice.forEach {
            if (it.category != ADVANCE_PAYMENT)
                retailerTxnAmount += it.retailerTxnAmount ?: 0.0
        }
        return retailerTotalTxnAmount!! - (retailerTxnAmount + advanceAmountSettled)
    }

    @Transactional
    fun createAdvancePaymentFromDraftInvoice(
        referenceNumber: String,
        retailerTxnId: String,
        settlement: Settlement,
        userName: String,
        userEmail: String,
        ds: String? = null
    ) {
        val draftInvoice = rioDraftInvoiceRepo.getDraftInvoicesByTxnId(referenceNumber, retailerTxnId)
        val advanceAmount = getInvoiceAdvanceAmount(draftInvoice)

        var advancePaymentTxn = false
        draftInvoice.forEach {
            if (it.category == ADVANCE_PAYMENT) {
                advancePaymentTxn = true

            }
        }
        var advanceId: Long? = null
        if (advanceAmount > 0) {
            if (draftInvoice[0].retailerTxnType != PaymentType.CHEQUE) {
                val bkInvoice = when (advancePaymentTxn) {
                    true -> getInvoiceByDraftReceipt(draftInvoice)
                    false -> bkInvoiceReadRepo.getOne(draftInvoice[0].bkInvoiceId!!)
                }

                advanceId = advancePaymentService.autoCreateAndApproveAdvancePayment(
                    advanceAmount,
                    bkInvoice,
                    settlement,
                    userName,
                    userEmail,
                    null,
                    ds
                )
            }
        }
        receiptService.updateAdvancePaymentAmountInReceipt(
            settlement,
            advanceAmount,
            AdvancePaymentSource.RIO_COLLECTIONS,
            advanceId
        )

    }

    fun generateDraftInvoiceFileEmail(draftInvoiceReportDto: DraftInvoiceReportDto) {
        log.info("Generating Draft Invoice File Email")
        val tenants = companyService.findTenants(draftInvoiceReportDto.ds ?: draftInvoiceReportDto.tenant)
        if (tenants.isEmpty()) throw RequestException("Your tenant: ${draftInvoiceReportDto.tenant} is not mapped to any Company")
        val fromDate = draftInvoiceReportDto.from
        val toDate = draftInvoiceReportDto.to
        val users = rioDraftInvoiceReadRepo.getDistinctApprovers(tenants, fromDate.atStartOfDay(), toDate.atTime(23, 59, 59))
        val userMap = if (users.isNullOrEmpty()) {
            emptyMap<String, User>()
        } else {
            try {
                userProxy.getUserList(users)?.associateBy { it.id }
            } catch (e: Exception) {
                log.error("Error in user proxy", e)
                emptyMap<String, User>()
            }

        }
        val res = mutableListOf<RioDraftInvoiceDto>()
        var currentDate = fromDate
        while (!currentDate.isAfter(toDate)) {
            log.info("Processing invoices for date: $currentDate")
            var currentPage = 0
            val startDate = currentDate.atStartOfDay()
            val endDate = currentDate.atTime(23, 59, 59)
            do {
                val pagination = PageRequest.of(currentPage, 500)
                val page = rioDraftInvoiceReadRepo.generateDraftInvoicesData(tenants, startDate, endDate, pagination)
                page.content.forEach { request ->
                    request.approvedBy = userMap?.get(request.approvedBy)?.name ?: request.approvedBy
                }
                res.addAll(page.content)
                currentPage++
            } while (res.size < page.totalElements && page.content.isNotEmpty())
            currentDate = currentDate.plusDays(1)
        }
        if (res.isEmpty()) return
        val s3Link = printRioInvoiceToCSV(res)
        log.info("s3Link : $s3Link")
        notificationMailService.emailFile(
            taskName = "Draft Invoice",
            from = draftInvoiceReportDto.from,
            to = draftInvoiceReportDto.to,
            toEmailIds = mutableListOf(draftInvoiceReportDto.email),
            s3Link = s3Link
        )
    }

    private fun printRioInvoiceToCSV(data: List<RioDraftInvoiceDto>): String {
        val prefix = "RioDraftInvoiceList-"

        val csvPrinter: CSVPrinter
        var bytes = ByteArrayOutputStream()

        csvPrinter = CSVPrinter(
            bytes.bufferedWriter(), CSVFormat.DEFAULT
                .withHeader(
                    RioDraftFileUtils.draftFileCols[0],
                    RioDraftFileUtils.draftFileCols[1],
                    RioDraftFileUtils.draftFileCols[2],
                    RioDraftFileUtils.draftFileCols[3],
                    RioDraftFileUtils.draftFileCols[4],
                    RioDraftFileUtils.draftFileCols[5],
                    RioDraftFileUtils.draftFileCols[6],
                    RioDraftFileUtils.draftFileCols[7],
                    RioDraftFileUtils.draftFileCols[8],
                    RioDraftFileUtils.draftFileCols[9],
                    RioDraftFileUtils.draftFileCols[10],
                    RioDraftFileUtils.draftFileCols[11],
                    RioDraftFileUtils.draftFileCols[12]
                )
        )
        data!!.forEach { it ->
            if (it != null)
                csvPrinter.printRecord(
                    it.paymentRefNum,
                    it.partnerDetailId,
                    it.retailerName,
                    it.salesManName,
                    it.txnType,
                    it.txnDate,
                    it.totalInvoices,
                    it.receiptAmount,
                    it.settledAmount,
                    it.retailerTxnId,
                    it.status,
                    it.approvedBy,
                    it.approvedOn
                )
        }
        csvPrinter.flush()

        val uploaded = s3FileUtilityService.uploadFile(bytes.toByteArray(), "CSV", prefix)
            ?: throw RequestException("s3 url failed to genrated!")

        return uploaded!!
    }

    fun checkAndHandleAdvancePayments(
        referenceNumber: String,
        retailerTxnId: String,
        referenceNumberInvoiceMap: MutableMap<Pair<String, String>, DraftInvoiceReferenceMap>,
        userName: String,
        userEmail: String,
        ds: String? = null,
        action: RioDraftInvoiceStatusType,
        remarks: String?,
        referenceNumberInvoiceMapData: DraftInvoiceReferenceMap
    ) {
        val draftInvoices = rioDraftInvoiceRepo.getDraftInvoicesByTxnId(referenceNumber, retailerTxnId)
        draftInvoices.forEach {
            if (it.category != ADVANCE_PAYMENT) {
                return
            }
        }
        val draftInvoice: DigitalReceiptsWriteback? = draftInvoices.find { it.category == ADVANCE_PAYMENT }
        if (draftInvoice != null) {
            if (referenceNumberInvoiceMap[Pair(
                    referenceNumber,
                    retailerTxnId
                )]?.transactionType == draftInvoice.retailerTxnType
            ) {
                if (action != RioDraftInvoiceStatusType.REJECTED) {
                    val amount = referenceNumberInvoiceMapData.updatedAmount ?: draftInvoice.advanceAmount
                    val supplier = supplierProxy.supplier(null, draftInvoice.retailerFrontEndPartyCode?.toLong())
                    val advancePaymentDto = AdvancePaymentDto(
                        amount = amount?.toBigDecimal() ?: BigDecimal.ZERO,
                        partnerId = supplier[0]?.partnerId!!,
                        partnerName = supplier[0]?.partnerName!!,
                        partnerDetailId = draftInvoice.retailerFrontEndPartyCode?.toLong(),
                        tenant = draftInvoice.tenant!!,
                        type = PartnerType.CUSTOMER,
                        client = InvoiceType.RIO,
                        typeOfAdvance = AdvanceType.INVOICE,
                        remarks = "Rio Collection Advance Payment Transaction",
                        createdByName = userName,
                        userEmail = userEmail,
                        refDocuments = listOf(PaymentRefItem(referenceNumber, LocalDate.now())),
                        source = AdvancePaymentSource.SYSTEM
                    )

                    val advancePayment = advancePaymentService.createAdvancePayment(advancePaymentDto, "SYSTEM", null)
                    val updateAdvancePaymentDto = UpdateAdvancePaymentDto(
                        amount = advancePayment.amount,
                        paymentReference = referenceNumber,
                        paymentType = draftInvoice.retailerTxnType,
                        paymentDate = LocalDate.now(),
                        chequeDate = draftInvoice.chequeDate,
                        bankName = draftInvoice.bankName,
                        remarks = "Approved Rio Collection Advance Payment Transaction",
                        tenant = draftInvoice.tenant!!,
                        change = true,
                        source = AdvancePaymentSource.SYSTEM,
                        isRioTransaction = true
                    )
                    advancePaymentService.checkerAdvancePayment(
                        advancePayment.id!!,
                        updateAdvancePaymentDto,
                        advancePayment.assignedToId!!,
                        ds
                    )
                }
            }
            updateDraftInvoice(listOf(draftInvoice), userName, remarks, action, referenceNumberInvoiceMapData)
        }
    }


    fun mapInvoiceWithAdvanceAmount(
        invoiceAndAdvanceAmountMap: List<InvoiceNumAdvanceAmountMap>,
        referenceNumber: String,
        retailerTxnId: String,
        userName: String
    ) {
        val draftInvoices = rioDraftInvoiceRepo.getDraftInvoicesByTxnId(referenceNumber, retailerTxnId)
        var draftInvoice = draftInvoices.find { it.category == ADVANCE_PAYMENT }
        if (draftInvoice == null) {
            val draftInvoiceCopy = draftInvoices[0].copy()
            draftInvoiceCopy.id = null
            draftInvoiceCopy.bkInvoiceId = null
            draftInvoiceCopy.category = ADVANCE_PAYMENT
            draftInvoiceCopy.advanceAmount =
                draftInvoices[0].retailerTotalTxnAmount!! - draftInvoices[0].retailerTxnAmount!!
            draftInvoiceCopy.retailerTxnAmount = draftInvoices[0].advanceAmount
                ?: (draftInvoices[0].retailerTotalTxnAmount!! - draftInvoices[0].retailerTxnAmount!!)
            draftInvoiceCopy.invoiceNumber = null
            draftInvoiceCopy.distributorInvoiceId = null
            draftInvoice = rioDraftInvoiceRepo.save(draftInvoiceCopy)
        }
        val mappingData: MutableList<DigitalReceiptsAdvanceInvoiceMapping> = mutableListOf()
        invoiceAndAdvanceAmountMap.forEach {
            mappingData.add(
                DigitalReceiptsAdvanceInvoiceMapping(
                    null,
                    draftInvoice.id,
                    it.invoiceId,
                    it.advanceAmountSettled,
                    now(),
                    userName
                )
            )
        }
        digitalReceiptsAdvanceInvoiceMappingRepo.saveAll(mappingData)
    }

    fun mapAdvanceInvoiceForSettlement(
        invoiceAndAdvanceAmountMap: List<InvoiceNumAdvanceAmountMap>,
        referenceNumber: String,
        retailerTxnId: String,
        invoicesToBeSettled: MutableList<RioDraftSettlementDto>,
        action: RioDraftInvoiceStatusType,
        userName: String,
        remarks: String?,
        referenceNumberInvoiceMapData: DraftInvoiceReferenceMap
    ): MutableList<RioDraftSettlementDto> {
        val draftInvoices = rioDraftInvoiceRepo.getDraftInvoicesByTxnId(referenceNumber, retailerTxnId)
        val draftInvoice = draftInvoices.find { it.category == ADVANCE_PAYMENT }

        if (action != RioDraftInvoiceStatusType.REJECTED) {
            val advanceMappedInvoices = digitalReceiptsAdvanceInvoiceMappingRepo.getByDraftInvoiceId(draftInvoice?.id!!)
            advanceMappedInvoices.forEach {
                invoicesToBeSettled.add(
                    RioDraftSettlementDto(
                        it.invoiceId!!,
                        it.digitalReceiptsWritebackId!!,
                        draftInvoices[0].eventType ?: CreationType.INVOICE
                    )
                )
            }
        }
        //updating status of draft invoice where category is advance payment(new transactions)
        if (draftInvoice != null) {
            updateDraftInvoice(listOf(draftInvoice), userName, remarks, action, referenceNumberInvoiceMapData)
        }
        return invoicesToBeSettled
    }

    fun checkInvoiceStatusForSettlement(bkInvoice: BkInvoice, draftInvoice: DigitalReceiptsWriteback): InvoiceStatus {
        if (draftInvoice.category == ADVANCE_PAYMENT) {
            val advanceMappedInvoices = digitalReceiptsAdvanceInvoiceMappingRepo.getByDraftInvoiceIdsAndInvoiceIds(
                draftInvoice.id!!,
                bkInvoice.id
            )
            advanceMappedInvoices.forEach {
                if (bkInvoice.id == it.invoiceId) {
                    if (it.invoiceSettledAmount!! >= (bkInvoice.amount - bkInvoice.paidAmount)) {
                        return InvoiceStatus.PAID
                    } else if (it.invoiceSettledAmount!! < (bkInvoice.amount - bkInvoice.paidAmount)) {
                        return InvoiceStatus.PARTIAL_PAID
                    }
                }
            }
        } else {
            if (draftInvoice.retailerTxnAmount!! >= (bkInvoice.amount - bkInvoice.paidAmount)) {
                return InvoiceStatus.PAID
            } else if (draftInvoice.retailerTxnAmount!! < (bkInvoice.amount - bkInvoice.paidAmount)) {
                return InvoiceStatus.PARTIAL_PAID
            }
        }
        return InvoiceStatus.PAID
    }

    fun checkInvoicePaidAmountForSettlement(bkInvoice: BkInvoice, draftInvoice: DigitalReceiptsWriteback): Double {
        if (draftInvoice.category == ADVANCE_PAYMENT) {
            val advanceMappedInvoices = digitalReceiptsAdvanceInvoiceMappingRepo.getByDraftInvoiceIdsAndInvoiceIds(
                draftInvoice.id!!,
                bkInvoice.id
            )
            advanceMappedInvoices.forEach {
                if (bkInvoice.id == it.invoiceId) {
                    return bkInvoice.paidAmount + it.invoiceSettledAmount!!
                }
            }
        } else {
            return bkInvoice.paidAmount + draftInvoice.retailerTxnAmount!!
        }
        return 0.00
    }

    fun checkInvoiceSettledAmountForSettlement(bkInvoice: BkInvoice, draftInvoice: DigitalReceiptsWriteback): Double {
        if (draftInvoice.category == ADVANCE_PAYMENT) {
            val advanceMappedInvoices = digitalReceiptsAdvanceInvoiceMappingRepo.getByDraftInvoiceIdsAndInvoiceIds(
                draftInvoice.id!!,
                bkInvoice.id
            )
            advanceMappedInvoices.forEach {
                if (bkInvoice.id == it.invoiceId) {
                    return it.invoiceSettledAmount!!
                }
            }
        } else {
            return draftInvoice.retailerTxnAmount!!
        }
        return 0.00
    }

    fun getInvoiceByDraftReceipt(draftInvoice: List<DigitalReceiptsWriteback>): BkInvoice {
        //function to check if settling invoices for complete advance or partial advance payment
        var bkInvoiceId: Long? = null
        draftInvoice.forEach {
            if (it.bkInvoiceId != null && it.bkInvoiceId?.toInt() != 0) {
                bkInvoiceId = it.bkInvoiceId
            }
        }
        if (bkInvoiceId == null || bkInvoiceId?.toInt() == 0) {
            val data = digitalReceiptsAdvanceInvoiceMappingRepo.getByDraftInvoiceId(draftInvoice[0].id!!)
            return bkInvoiceReadRepo.getOne(data[0].invoiceId!!)
        } else {
            return bkInvoiceReadRepo.getOne(bkInvoiceId!!)
        }

    }

    fun findChequeWithSameDetails(rioPaymentDTO: RIOPaymentDTO): Boolean {
        if (rioPaymentDTO.retailerTxnType?.uppercase() == PaymentType.CHEQUE.name) {

            val chequeDate = getDateFromEpochStamp(rioPaymentDTO.chequeDate)
            val distributorDetails = invoiceService.getTenantCodeAndCustomerType(rioPaymentDTO.distributorId!!)
            val tenant = when (distributorDetails.fulfilmentType) {
                OrderType.B2B2B -> {
                    val companyTenantMapping = companyService.getTenantByPDI(rioPaymentDTO.distributorId!!)
                    companyTenantMapping.tenant
                }

                OrderType.B2B -> distributorDetails.tenant
            }

            val data = rioDraftInvoiceRepo.findChequeWithSameDetails(
                rioPaymentDTO.retailerTxnId!!,
                rioPaymentDTO.chequeNo!!,
                rioPaymentDTO.bankName,
                tenant,
                chequeDate,
                rioPaymentDTO.retailerFrontEndPartyCode!!,
                PaymentType.CHEQUE
            )
            if (data.isNotEmpty()) {
                return true
            }
        }
        return false
    }

    fun findDuplicateNeftTransaction(rioPaymentDTO: RIOPaymentDTO): Boolean {
        if (rioPaymentDTO.retailerTxnType?.uppercase() == PaymentType.NEFT.name) {

            val transactionalDate = getDateFromEpochStamp(rioPaymentDTO.retailerTxnDate)
            val distributorDetails = invoiceService.getTenantCodeAndCustomerType(rioPaymentDTO.distributorId!!)
            val tenant = when (distributorDetails.fulfilmentType) {
                OrderType.B2B2B -> {
                    val companyTenantMapping = companyService.getTenantByPDI(rioPaymentDTO.distributorId!!)
                    companyTenantMapping.tenant
                }

                OrderType.B2B -> distributorDetails.tenant
            }

            val data = rioDraftInvoiceRepo.findDuplicateNeftTxn(
                rioPaymentDTO.retailerTxnId!!,
                rioPaymentDTO.neftId!!,
                transactionalDate,
                tenant,
                rioPaymentDTO.retailerFrontEndPartyCode!!
            )
            if (data.isNotEmpty()) {
                return true
            }
        }
        return false
    }
}
