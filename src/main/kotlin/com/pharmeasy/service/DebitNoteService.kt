package com.pharmeasy.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.pharmeasy.data.*
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.*
import com.pharmeasy.model.debitnote.DiscountDebitNoteDto
import com.pharmeasy.model.debitnote.NsrDebitNoteDto
import com.pharmeasy.model.debitnote.PurchaseReturnPdfDto
import com.pharmeasy.proxy.*
import com.pharmeasy.specification.SupplierDebitNoteSpecification
import com.pharmeasy.repo.*
import com.pharmeasy.repo.read.DebitNoteReadRepo
import com.pharmeasy.stream.CnCreatePdfVaultSinkPusher
import com.pharmeasy.stream.EinvoiceSinkPusher
import com.pharmeasy.stream.EwayBillSinkPusher
import com.pharmeasy.type.*
import com.pharmeasy.type.NoteStatus
import com.pharmeasy.type.NoteTypes
import com.pharmeasy.util.*
import com.pharmeasy.util.DateUtils.getISTDateTime
import com.pharmeasy.util.DateUtils.getUTCDateTime
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVPrinter
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.io.ByteArrayOutputStream
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalDateTime.now
import kotlin.collections.HashMap



@Service
class DebitNoteService(@Value("\${app.retail-io.version}")val retailIoVersion: String,
                       @Value("\${app.retail-io.source}")val retailIoSource: String,
                       @Value("\${app.retail-io.key}")val retailIoKey: String) {
    companion object {
        private val log = LoggerFactory.getLogger(DebitNoteService::class.java)
        private val AUTO_CN_ENABLED_CLIENTS = listOf(InvoiceType.PE, InvoiceType.ML, InvoiceType.CS)
    }

    @Autowired
    private lateinit var debitNoteRepo: DebitNoteRepo

    @Autowired
    private lateinit var debitNoteReadRepo: DebitNoteReadRepo

    @Autowired
    private lateinit var bkSupplierRepo: BkSupplierRepo

    @Autowired
    private lateinit var invoiceProxy: InvoiceProxy

    @Autowired
    private lateinit var supplierProxy: SupplierProxy

    @Autowired
    private lateinit var invoiceDebitNoteRepo: InvoiceDebitNoteRepo

    @Autowired
    private lateinit var bkInvoiceRepo: BkInvoiceRepo

    @Autowired
    private lateinit var companyTenantMappingRepo: CompanyTenantMappingRepo

    @Autowired
    private lateinit var orderProxy: OrderProxy

    @Autowired
    private lateinit var s3FileUtilityService: S3FileUtilityService

    @Autowired
    private lateinit var debitDataLinksRepo: DebitDataLinksRepo

    @Autowired
    private lateinit var debitNotePrRepo: DebitNotePrRepo

    @Autowired
    private lateinit var vendorFileService: VendorFileService

    @Autowired
    private lateinit var documentMasterService: DocumentMasterService

    @Autowired
    private lateinit var companyRepo: CompanyRepo

    @Autowired
    private lateinit var bkItemRepo: BkItemRepo

    @Autowired
    private lateinit var debitNoteDetailRepo: DebitNoteDetailRepo

    @Autowired
    private lateinit var creditNoteService: CreditNoteService

    @Autowired
    private lateinit var settlementService: SettlementService

    @Autowired
    private lateinit var invoiceService: InvoiceService

    @Autowired
    private lateinit var companyService: CompanyService

    @Autowired
    private lateinit var storeProxy: StoreProxy

    @Autowired
    private lateinit var creditNoteRepo: CreditNoteRepo

    @Autowired
    private lateinit var partnerService: PartnerService

    @Autowired
    private lateinit var reportProxy: ReportProxy

    @Autowired
    private lateinit var slabTaskDetailRepo: SlabTaskDetailRepo

    @Autowired
    private lateinit var redisUtilityService: RedisUtilityService

    @Autowired
    private lateinit var systemConfigService: SystemConfigService


    @Autowired
    private lateinit var retailIoProxy: RetailIoProxy


    @Autowired
    private lateinit var eventPublisherUtil: EventPublisherUtil

    @Autowired
    private lateinit var ewayBillSinkPusher: EwayBillSinkPusher

    @Autowired
    private lateinit var gstUtil: GstUtils


    fun get(id: Long): DebitNote? {
        return debitNoteRepo.get(id)
    }

    fun getDebitNotesForSupplier(id: Long, status: NoteStatus?, type: NoteTypes?, tenant: String, customerType: Boolean, ds: String? = null): List<DebitNote>? {
        var custType: PartnerType = PartnerType.VENDOR
        if (customerType) custType = PartnerType.CUSTOMER
        var tenants = companyService.findTenants(ds ?: tenant)

        if (tenants.isNullOrEmpty()) return null

        val debitNotes = debitNoteRepo.getDebitNotesForSupplier(id, status, type, custType, tenants)

        addInvoiceToDebitNotes(debitNotes)

        return debitNotes
    }

    fun addInvoiceToDebitNotes(debitNotes: List<DebitNote>?) {
        if (debitNotes != null && debitNotes.isNotEmpty()) {

            val invoiceDebitNoteList = invoiceDebitNoteRepo.getInvoiceDebitNotes(debitNotes?.map { it.id }!!)
            if (invoiceDebitNoteList.isNotEmpty()) {

                val invoices = bkInvoiceRepo.getAll(invoiceDebitNoteList?.map { it.id.invoiceId } as List<Long>)
                if (invoices.isNotEmpty()) {

                    val invoiceIdMap = HashMap<Long, BkInvoice>()
                    invoices.forEach { invoiceIdMap[it.id] = it }

                    debitNotes.forEach { debitNote ->
                        debitNote.bkInvoices = mutableListOf()
                        invoiceDebitNoteList.forEach {
                            if (it.id.debitNoteId == debitNote.id && invoiceIdMap[it.id.invoiceId] != null)
                                debitNote.bkInvoices.add(invoiceIdMap[it.id.invoiceId]!!)
                        }
                    }
                }
            }
        }
    }

    @Transactional
    fun save(user: String, debitNote: DebitNote, items: MutableList<DebitNoteDetails>?, `invoicePresentInDb`: Boolean = true, isCopyEvent:Boolean = false): DebitNote {
        if (isProcessed(debitNote)) {
            throw RequestException("Duplicate Debit Note")
        }

        debitNote.createdBy = user
        debitNote.updatedBy = debitNote.createdBy
        debitNote.createdOn = debitNote.createdOn ?: now()
        debitNote.updatedOn = debitNote.updatedOn ?: now()

        if (!debitNote.bkInvoices.isNullOrEmpty()) {
            debitNote.supplierId = debitNote.bkInvoices[0].partnerId!!
            debitNote.partnerDetailId = debitNote.bkInvoices[0].partnerDetailId
            debitNote.partnerId = debitNote.bkInvoices[0].partnerId
        }
        if(!isCopyEvent) {
            debitNote.bkInvoices.forEach { bkInvoice ->
                bkInvoice.items.forEach { bkItem ->
                    bkItem.bkInvoice = bkInvoice
                    bkItem.trueEpr = bkItem.epr
                }
            }
        }

        val dn = debitNoteRepo.save(debitNote)
        var list = mutableListOf<InvoiceDebitNote>()
        debitNote.bkInvoices.forEach { bkInvoice ->
            list.add(InvoiceDebitNote(InvoiceDebitNoteId(bkInvoice.id, dn.id)))
        }
        if (!items.isNullOrEmpty()) {

            items.forEach {
                it.debitnoteId = dn.id
                it.debitNoteNumber = dn.debitNoteNumber
            }
            dn.debitItems = debitNoteDetailRepo.saveAll(items)
        }

        if (!list.isNullOrEmpty() && invoicePresentInDb) {
            log.debug("Trying to save invoiceDebitNote **")
            invoiceDebitNoteRepo.saveAll(list)
        }
        dn.bkInvoices = debitNote.bkInvoices
        return dn
    }

    fun isProcessed(debitNote: DebitNote): Boolean {
        log.debug("Inside isProcessed")

        val existing = debitNoteRepo.findDuplicates(debitNote.supplierId!!, debitNote.createdBy, debitNote.debitNoteNumber,
                debitNote.amountReceivable, debitNote.amountReceived)

        if (existing != null && existing.isNotEmpty()) {
            log.error("Debit note being saved is similar to an existing debit note")
            log.error("${existing[0].id}, ${existing[0].createdBy}, ${existing[0].debitNoteNumber}, ${existing[0].amountReceivable}, " +
                    "${existing[0].amountReceived}, ${existing[0].status}, ${existing[0].noteType}")
            return true
        }

        return false
    }

    @Transactional
    fun closeAll(user: String, reason: String, debitNotes: List<Long>): MutableList<DebitNote> {
        log.debug("Closing the given debit notes")

        var res = mutableListOf<DebitNote>()
        var partnerIds = mutableSetOf<Long>()
        var lossAmt = BigDecimal(0)
        var tenant = String()

        for (dnId in debitNotes) {
            val dn = debitNoteRepo.get(dnId)

            if (dn == null || dn.status == NoteStatus.RECORDED) {
                log.error("Debit Note with id - $dnId is either NULL or already recorded")
                continue
            }

            tenant = dn.tenant

            val loss = dn.amountReceivable - dn.amountReceived
            if (loss < BigDecimal(0)) {
                log.error("Debit note ${dn.id}, amount receivable is less than amount received")
                continue
            }

            partnerIds.add(dn.partnerId!!)
            if (partnerIds.size > 1)
                throw RequestException("Cannot close Debit Notes from different vendors")

            dn.remarks = reason
            dn.updatedBy = user
            dn.updatedOn = now()
            dn.status = NoteStatus.RECORDED
            res.add(dn)
            lossAmt += (dn.amountReceivable - dn.amountReceived)
        }

        val r = debitNoteRepo.saveAll(res)

        val bkSupplier = bkSupplierRepo.getByPartnerId(r[0].partnerId!!)
                ?: BkSupplier(0, now(), now(),
                        r[0].supplierName, r[0].supplierId, lossAmt, null, null, tenant)

        bkSupplier.loss += lossAmt
        bkSupplier.updatedOn = now()
        bkSupplierRepo.save(bkSupplier)

        return r
    }

    @Transactional
    fun update(user: String, id: Long, debitNote: DebitNote): DebitNote {
        log.debug("Updating the given debit note ${debitNote.id}")

        val dn = debitNoteRepo.get(id) ?: throw RequestException("Debit note with id: $id not found")
        if (dn.status == NoteStatus.RECORDED) {
            throw RequestException("Debit note with id: $id already closed")
        }

        // We are only going to update the following fields and just ignore the rest.
        dn.updatedBy = user
        dn.updatedOn = now()
        dn.amountReceived = debitNote.amountReceived
        dn.status = debitNote.status
        dn.creditNoteId = debitNote.creditNoteId
        //  dn.creditNote = debitNote.creditNote

        // calculate loss
        val loss = dn.amountReceivable - dn.amountReceived
        if (loss > BigDecimal(0)) {
            log.debug("Supplier ${dn.supplierId} incurred loss of Rs. $loss")
            val bkSupplier = bkSupplierRepo.getByPartnerId(dn.supplierId)
            if (bkSupplier == null) {
                bkSupplierRepo.save(BkSupplier(0, now(), now(), dn.supplierName, dn.supplierId, loss, dn.supplierId, dn.partnerDetailId, dn.tenant))
            } else {
                bkSupplier.loss += loss
                bkSupplier.updatedOn = now()
                bkSupplierRepo.save(bkSupplier)
            }
        }

        return debitNoteRepo.save(dn)
    }

    fun getDebitNotes(page: Int?, size: Int?, supplierId: Long?, invoiceNum: String?, debitNoteId: Long?, status: NoteStatus?, type: NoteTypes?,
                      tenant: String?, from: LocalDate?, to: LocalDate?, customerType: Boolean, ds: String? = null): PageableDebitNote {

        log.debug("Inside GetDebitNotes")

        var custType: PartnerType = PartnerType.VENDOR
        if (customerType) custType = PartnerType.CUSTOMER

        var tenants=companyService.findTenants(ds?:tenant!!)
        var companyTenantmapping = companyService.getCompanyTenantMappingObject(ds?:tenant!!)?: throw RequestException("No tenant Mapping found")


        if (tenants.isNullOrEmpty()) return PageableDebitNote()

        var fromDate: LocalDateTime? = null
        if (from != null) {
            fromDate = getUTCDateTime(from.atStartOfDay())
        }

        var toDate: LocalDateTime? = null
        if (to != null) {
            toDate = getUTCDateTime(to.atTime(23, 59, 59))
        }

        val page = PageRequest.of(if (page == null) 0 else page, if (size == null) 10 else size)

        val res = if (invoiceNum != null) {
            log.debug("inside if block invoice num = $invoiceNum")
            val invoice = bkInvoiceRepo.getByInvoiceNum(invoiceNum, tenants)
            if (invoice.isNotEmpty()) {
                var ids = invoice.map { it?.id }.toMutableList()
                log.debug(("all invoice id ${ids}"))

                invoiceDebitNoteRepo.getPageableDebitNotesForInvoice(ids, page)
            } else {

                return PageableDebitNote()
            }
        } else {
            debitNoteRepo.getDebitNotes(supplierId, debitNoteId, status, type, tenants, fromDate, toDate, custType, page)
        }

        if (res == null)
            return PageableDebitNote()
        else {
            res.content.forEach { debitNote ->
                debitNote.createdOn = getISTDateTime(debitNote.createdOn)
                debitNote.updatedOn = getISTDateTime(debitNote.createdOn)
                debitNote.bkInvoices = invoiceDebitNoteRepo.getInvoicesForDebitNote(debitNote.id)
                var supplierList = supplierProxy.supplier(listOf(debitNote.partnerId))
                var supplier = if (supplierList.isNotEmpty()) supplierList.get(0) else null
                if (supplier != null) debitNote.supplierName = supplier.partnerName!!

                if (debitNote.noteType.equals(NoteTypes.DISCOUNT.name)) debitNote.source = "VAULT" else debitNote.source = companyTenantmapping.tenantName
            }
            return PageableDebitNote(res.totalElements, res.totalPages, res.content)
        }
    }

    fun getVendorDebitNotes(page: Int?, size: Int?, partnerId: Long, debitNoteNumber: String?, creditNoteNumber: String?,
                            invoiceNumber: String?, status: NoteStatus?, type: NoteTypes?, from: LocalDate?, to: LocalDate?,
                            source: String?, tenant: String, customerType: Boolean, ds: String? = null, invoiceId: String?): PaginationDto {
        log.debug("Inside getSupplierDebitNotes $source : $status : $type : invoice id : $invoiceId")
        var custType: PartnerType = PartnerType.VENDOR
        if (customerType) custType = PartnerType.CUSTOMER

        var sortBy = Sort.by(Sort.Direction.ASC, "createdOn").and(Sort.by(Sort.Direction.DESC, "amountReceivable"))
        val pagination = PageRequest.of(page ?: 0, size ?: 10, sortBy)

        val res: Page<DebitNote>

        var tenants = companyService.findTenants(ds ?: tenant)
        if (tenants.isEmpty()) throw RequestException("Your tenant: $tenant is not mapped to any Company")
        var companyTenantMapping = companyService.getCompanyTenantMappingObject(ds?:tenant)?: throw RequestException("No tenant Mapping found")
        var fromDate: LocalDateTime? = null
        if (from != null) {
            fromDate = getUTCDateTime(from.atStartOfDay())
        }

        var toDate: LocalDateTime? = null
        if (to != null) {
            toDate = getUTCDateTime(to.atTime(23, 59, 59))
        }

        if (creditNoteNumber != null) res = debitNoteRepo.getPaginatedDebitNotesForCreditNoteNumber(creditNoteNumber, partnerId, status, pagination)
        else if (!invoiceNumber.isNullOrEmpty() || !invoiceId.isNullOrEmpty()) res = debitNoteRepo.getDebitNotesForInvoiceNumber(invoiceNumber, status, type, fromDate, toDate, source, partnerId, invoiceId, pagination)
        else if (debitNoteNumber != null) res = debitNoteRepo.getDebitNoteForDebitNoteNumber(debitNoteNumber, partnerId, status, pagination)
        else {
            res = debitNoteRepo.findAll(SupplierDebitNoteSpecification(partnerId, status, type, source, tenants, from, to, debitNoteNumber, custType), pagination)
        }
        val supplierList = supplierProxy.supplier(listOf(partnerId))
        val supplier = if (supplierList.isNotEmpty()) supplierList[0] else null

        val creditNotes = creditNoteRepo.findAllById(res.content.map { it.creditNoteId }).filter { it.slabTaskDetail != null }
        val slabTaskDetailIdToCreditNoteMap = creditNotes.associateBy { it.slabTaskDetail?.id }
        val creditNoteIdToCnMap = creditNotes.associateBy { it.id }

        var details = mapOf<Long, SlabDiscountCreditNoteDetailDto>()
        if (slabTaskDetailIdToCreditNoteMap.isNotEmpty())
            details = slabTaskDetailRepo.getSlabCreditNoteDetail(slabTaskDetailIdToCreditNoteMap.keys.toMutableList()).associateBy { it.slabTaskDetailId }

        res.content.forEach { dn ->
            dn.createdOn = getISTDateTime(dn.createdOn)
            dn.updatedOn = getISTDateTime(dn.updatedOn)
            dn.source = companyTenantMapping.tenantName
            dn.bkInvoices = invoiceDebitNoteRepo.getInvoicesForDebitNote(dn.id)
            dn.purchaseReturns = debitNotePrRepo.getByDebitNoteNumber(dn.debitNoteNumber, tenants).toMutableList()
            dn.purchaseReturns.forEach {
                if (it.debitNoteNumber == dn.debitNoteNumber) dn.purchaseTransactionNumber = it.purchaseTransactionNumber
            }
            if (supplier != null) dn.supplierName = supplier.partnerName!!
            log.debug("vendor name: ${dn.supplierName} : ${supplier?.partnerName}")

            if (dn.noteType == NoteTypes.SLAB_DISCOUNT_DN) {
                val creditNote = creditNoteIdToCnMap[dn.creditNoteId]!!
                val creditNoteDetail = details[creditNote.slabTaskDetail?.id]!!

                val netSales = creditNoteDetail.netSales - creditNoteDetail.netSaleReturn
                val taxableAmount = netSales * BigDecimal.valueOf(creditNoteDetail.discountPercent / 10000.0)
                val sgstOrCgst = taxableAmount * BigDecimal.valueOf(creditNoteDetail.cgst / 100.0)
                val igst = taxableAmount * BigDecimal.valueOf(creditNoteDetail.igst / 100.0)

                dn.slabDiscountDetail = SlabDiscountCreditNoteDto(
                        period = SlabTaskUtils.getPeriodString(creditNoteDetail.startDate, creditNoteDetail.endDate),
                        saleAmount = creditNoteDetail.netSales,
                        saleReturnAmount = creditNoteDetail.netSaleReturn,
                        netSales = netSales,
                        discountPercent = creditNoteDetail.discountPercent / 100.0,
                        taxableValue = taxableAmount,
                        partnerId = dn.partnerId!!,
                        partnerDetailId = dn.partnerDetailId!!,
                        cgst = sgstOrCgst,
                        sgst = sgstOrCgst,
                        igst = igst,
                        total = taxableAmount + sgstOrCgst + sgstOrCgst + igst,
                        createdOn = dn.createdOn?.toLocalDate()!!,
                        gstPercent = if (igst != BigDecimal.valueOf(0)) 18 else 9,
                        compName = "",
                        irn = creditNote.irn,
                        qrCode = creditNote.qrCode
                )
            }
        }

        return PaginationDto(res.totalElements, res.totalPages, res.hasPrevious(), res.hasNext(), res.content)
    }

    @Transactional
    fun createCustomerDebitNotes(saleReturnDebitNoteDto: SaleReturnDebitNoteDto, type: NoteTypes, ds: Boolean = false): MutableList<DebitNote> {
        log.debug("SR : ${ObjectMapper().writeValueAsString(saleReturnDebitNoteDto)}")
        log.debug("DS : $ds ")

        var debitNotes: MutableList<DebitNote> = mutableListOf()
        var dsCompanyMapping: CompanyTenantMapping? = null
        if(ds){
            dsCompanyMapping = companyService.getTenantByPDI(saleReturnDebitNoteDto.data.partnerDetailIdOfDarkStore?:throw RequestException("Pdi of not mapped to darkstore"))
        }
        var tenants = companyService.findTenants(dsCompanyMapping?.tenant ?: saleReturnDebitNoteDto.data.tenant)
        log.debug("tenants : $tenants ")


        if (tenants.isNullOrEmpty()) return throw RequestException("No tenants found!")

        var company =
                companyRepo.getCompanyByTenant(if (ds) tenants[0]!! else saleReturnDebitNoteDto.data.tenant)
                        ?: throw RequestException("Company mapping not found for ${saleReturnDebitNoteDto.data.tenant}")

        // check for b3 and b2b
        var duplicateCheckTenants = listOf(if (ds) tenants[0]!! else saleReturnDebitNoteDto.data.tenant)
        var dn = debitNoteRepo.getByReturnOrderId(duplicateCheckTenants.toMutableList(), saleReturnDebitNoteDto.data.id!!, PartnerType.CUSTOMER, type, saleReturnDebitNoteDto.data.returnReferenceType
                ?: ReturnReferenceType.RETURN_ORDER, InvoiceType.valueOf(saleReturnDebitNoteDto.data.client))
        if (dn != null) {
            log.debug("Duplicate Customer DebitNote, ReturnOrderId : ${saleReturnDebitNoteDto.data.id!!}")
            throw RequestException("Duplicate DN")
        }

        try {
            var invoicePresentInDb = true
            var bkInvoice = bkInvoiceRepo.getByInvoiceId(saleReturnDebitNoteDto.data.invoiceNo, saleReturnDebitNoteDto.data.invoiceId, tenants)


            if (bkInvoice == null) {

                var storeInvoice: StoreInvoice

                if (ds && saleReturnDebitNoteDto.data.client == "RIO") {

                    var storeOrderInvoice = storeProxy.getRetailerInvoice(saleReturnDebitNoteDto.data.externalOrderId
                            ?: "", saleReturnDebitNoteDto.data.tenant)
                            ?: throw RequestException("Store invoice not found for DarkStore return : externalOrderId : ${saleReturnDebitNoteDto.data.externalOrderId} ! ")
                    storeInvoice = invoiceService.createStoreInvoice(storeOrderInvoice, saleReturnDebitNoteDto.data.createdByName
                            ?: "")


                } else {
                    storeInvoice = orderProxy.getStoreInvoice(saleReturnDebitNoteDto.data.invoiceNo, saleReturnDebitNoteDto.data.tenant)
                            ?: throw RequestException("Invoice not found in DB!")
                }
                invoicePresentInDb = false
                if (saleReturnDebitNoteDto.data.client == "PE" || saleReturnDebitNoteDto.data.client == "ML" || saleReturnDebitNoteDto.data.client == "CS")
                    bkInvoice = invoiceService.createInvoiceObjPE("system", storeInvoice, InvoiceStatus.PAID, saleReturnDebitNoteDto.data.tenant).second
                else {
                    var tenantPartnerInfo = supplierProxy.supplierByTenant(saleReturnDebitNoteDto.data.tenant)
                    if (tenantPartnerInfo.isNullOrEmpty()) throw RequestException("Partner info not found for Tenant ${saleReturnDebitNoteDto.data.tenant} ")
                    bkInvoice = if (ds)
                        invoiceService.createInvoiceObjRIO(storeInvoice, saleReturnDebitNoteDto.data.tenant, InvoiceStatus.PAID, "system", tenantPartnerInfo[0]!!.id!!).second
                    else
                        invoiceService.createInvoiceObjRIO(storeInvoice, tenants[0]!!, InvoiceStatus.PAID, "system", saleReturnDebitNoteDto.data.partnerDetailIdOfDarkStore!!).second


                }
            }

            var supplierList = supplierProxy.supplier(null,
                    if (ds)
                        saleReturnDebitNoteDto.data.partnerDetailIdOfRetailer
                    else
                        saleReturnDebitNoteDto.data.partnerId)


            var supplier = if (!supplierList.isNullOrEmpty()) supplierList.get(0) else throw RequestException("Could not get Supplier List for supplier ${saleReturnDebitNoteDto.data.partnerId} for tenant " +
                    "${saleReturnDebitNoteDto.data.tenant}")

            var itemList: MutableList<DebitNoteDetails> = mutableListOf()
            var amount = BigDecimal.ZERO

            var items = saleReturnDebitNoteDto.data.items?.groupBy { Pair(it.batchNumber, it.ucode) }

            if (saleReturnDebitNoteDto.data.client == "PE" || saleReturnDebitNoteDto.data.client == "ML" || saleReturnDebitNoteDto.data.client == "CS") {
                bkInvoice.items!!.forEach { i ->

                    if (items?.containsKey(Pair(i.batch, i.ucode)) == true) {

                        var saleItemObj = items?.get(Pair(i.batch, i.ucode))
                        var returnQty = items?.get(Pair(i.batch, i.ucode))?.size
                        var taxableValue = (i.abtMrp!! - (i.abtMrp!! * (i.discountPercentage!!.divide(BigDecimal(100), 5, RoundingMode.CEILING)))!!).multiply(returnQty!!.toBigDecimal())
                        log.debug("taxableValue : $taxableValue")
                        var netGstAmt = ((maxOf(i.igst!!, (i.sgst!! + i.cgst!!)).divide(BigDecimal(100), 5, RoundingMode.CEILING)).multiply(taxableValue))
                        var invoiceAmt = (taxableValue + netGstAmt).setScale(5, RoundingMode.CEILING)
                        var discountAmt = (i.discountPercentage!!.divide(BigDecimal(100), 5, RoundingMode.CEILING)).multiply(i.abtMrp)

                        // SCS-9029 using bk item created on, moved to event dto updated on, same as debitnote created on
                        itemList.add(DebitNoteDetails(null, saleReturnDebitNoteDto.data.updatedOn, saleReturnDebitNoteDto.data.updatedOn, i.itemId, i.ucode, i.batch, i.amount, i.quantity, i.epr, i.trueEpr, i.discountPercentage, i.discountAmount, i.cgst,
                                i.sgst, i.igst, saleItemObj?.get(0)?.hsnCode, invoiceAmt, netGstAmt, i.abtMrp, i.name, taxableValue, i.invoiceNumber, returnQty, null, null, null, null, null, i.expiry, null))
                        amount = amount.plus(invoiceAmt)

                    }
                }
            } else if (saleReturnDebitNoteDto.data.client == "RIO") {
                var invoiceItems = bkInvoice.items?.groupBy { Pair(it.batch, it.ucode) }

                invoiceItems?.forEach {

                    var totalInvoiceQty = 0
                    var invoicetaxableValue = BigDecimal.ZERO
                    it.value.forEach { i ->
                        totalInvoiceQty += i.quantity.plus(i.quantityFree ?: 0)
                        if (saleReturnDebitNoteDto.data.oneSchemeEnabled == false)
                            invoicetaxableValue += i.taxableAmount ?: BigDecimal.ZERO

                    }
                    if (saleReturnDebitNoteDto.data.oneSchemeEnabled == true)
                        invoicetaxableValue += it.value[0].effectivePerItemCost ?: BigDecimal.ZERO


                    if (items?.containsKey(it.key) == true) {

                        var saleItemObj = items?.get(it.key)
                        var returnQty = items?.get(it.key)?.size
                        var dnTaxablerate =
                            if (saleReturnDebitNoteDto.data.oneSchemeEnabled == false)
                                invoicetaxableValue.divide(totalInvoiceQty.toBigDecimal(), 5, RoundingMode.CEILING)
                            else
                                invoicetaxableValue

                        var dnTaxableValue = dnTaxablerate * returnQty!!.toBigDecimal()
                        var netGST = ((it.value[0].gst)?.divide(BigDecimal(100), 5, RoundingMode.CEILING))?.multiply(
                            dnTaxableValue
                        )
                        var dnValue = netGST?.plus(dnTaxableValue)?.setScale(5, RoundingMode.CEILING)


                        itemList.add(
                            DebitNoteDetails(
                                null,
                                saleReturnDebitNoteDto.data.updatedOn,
                                saleReturnDebitNoteDto.data.updatedOn,
                                it.value[0].itemId,
                                it.value[0].ucode,
                                it.value[0].batch,
                                it.value[0].amount,
                                it.value[0].quantity,
                                it.value[0].epr,
                                it.value[0].trueEpr,
                                it.value[0].discountPercentage,
                                it.value[0].discountAmount,
                                it.value[0].cgst,
                                it.value[0].sgst,
                                it.value[0].igst,
                                saleItemObj?.get(0)?.hsnCode,
                                dnValue,
                                netGST,
                                it.value[0].abtMrp,
                                it.value[0].name,
                                dnTaxableValue,
                                it.value[0].invoiceNumber,
                                returnQty,
                                null,
                                null,
                                null,
                                null,
                                null,
                                it.value[0].expiry,
                                null,
                                it.value[0].schemePercent,
                                it.value[0].quantityFree,
                                it.value[0].ptr,
                                it.value[0].schemeType,
                                it.value[0].gst,
                                it.value[0].quantityOrdered,
                                it.value[0].quantityScheme,
                                totalInvoiceQty,
                                invoicetaxableValue,
                                dnTaxablerate,
                                null,
                                null,
                                null,
                                null,
                                null,
                                null,
                                false,
                                BigDecimal.ZERO,
                                it.value[0].ucodeSchemePercent
                            )
                        )
                        amount = amount.plus(dnValue ?: BigDecimal.ZERO)
                    }
                }
            }

            val dn = DebitNote(
                    id = 0,
                    createdOn = now(),
                    updatedOn = now(),
                    supplierId = supplier!!.partnerId!!,
                    supplierName = supplier.partnerName!!,
                    purchaseTransactionNumber = saleReturnDebitNoteDto.data.transactionId,
                    debitNoteNumber = documentMasterService.getDocumentNumber(saleReturnDebitNoteDto.data.createdByName
                            ?: "", company.companyCode,
                            if (type == NoteTypes.SR_ACCEPTED) {
                                if (saleReturnDebitNoteDto.data.client == "RIO")
                                    DocumentType.DN_SR_ACCEPTED_B2B
                                else
                                    DocumentType.DN_SR_ACCEPTED_B2C
                            } else {

                                if (saleReturnDebitNoteDto.data.client == "RIO")
                                    DocumentType.DN_SR_EXPIRED_B2B
                                else
                                    DocumentType.DN_SR_EXPIRED_B2C
                            }),
                    createdBy = saleReturnDebitNoteDto.data.createdByName,
                    updatedBy = saleReturnDebitNoteDto.data.createdByName,
                    amountReceivable = amount,
                    amountReceived = BigDecimal(0),
                    remarks = null,
                    creditNoteId = null,
                    creditNoteNumber = null,
                    status = NoteStatus.PENDING,
                    noteType = type,
                    bkInvoices = mutableListOf(bkInvoice),
                    tenant = if (ds) tenants[0]!! else saleReturnDebitNoteDto.data.tenant,
                    partnerId = supplier.partnerId,
                    partnerDetailId = if (ds) saleReturnDebitNoteDto.data.partnerDetailIdOfRetailer else saleReturnDebitNoteDto.data.partnerId,
                    client = if (saleReturnDebitNoteDto.data.client.equals("PE")) InvoiceType.PE else if (saleReturnDebitNoteDto.data.client.equals("RIO")) InvoiceType.RIO else if (saleReturnDebitNoteDto.data.client.equals("ML")) InvoiceType.ML else if (saleReturnDebitNoteDto.data.client.equals("CS")) InvoiceType.CS else InvoiceType.VENDOR,
                    type = PartnerType.CUSTOMER,
                    returnOrderId = saleReturnDebitNoteDto.data.id,
                    consolidatedStoreInvoiceId = saleReturnDebitNoteDto.data.consolidatedStoreInvoiceId,
                    consolidatedStoreInvoiceInvoiceId = saleReturnDebitNoteDto.data.consolidatedStoreInvoiceInvoiceId,
                    returnDuringDelivery = saleReturnDebitNoteDto.data.returnType == DebitNoteReturnEventType.PARTIAL_CANCELLATION,
                    returnEventType = saleReturnDebitNoteDto.data.returnType ?: DebitNoteReturnEventType.REGULAR,
                    returnReferenceType = saleReturnDebitNoteDto.data.returnReferenceType
                            ?: ReturnReferenceType.RETURN_ORDER,
                    invoiceAt = bkInvoice.createdOn
            )

            debitNotes.add(save(saleReturnDebitNoteDto.data.createdByName
                    ?: saleReturnDebitNoteDto.data.createdBy!!, dn, itemList, invoicePresentInDb))

            var dnPrObj = debitNotePrRepo.save(DebitNotePr(0, now(), now(), saleReturnDebitNoteDto.data.createdBy, saleReturnDebitNoteDto.data.createdBy, dn.debitNoteNumber!!,
                    saleReturnDebitNoteDto.data.invoiceId!!, saleReturnDebitNoteDto.data.invoiceNo, amount, if (ds) tenants[0]
                    ?: "-" else saleReturnDebitNoteDto.data.tenant))

            var dnPrObjCopy = dnPrObj.copy()

            //create purchase invoice for dark store
            if (ds) {
                var bkInvoice = bkInvoiceRepo.getByInvoiceIdForDS("DS-${saleReturnDebitNoteDto.data.externalOrderId}", tenants[0]!!)

                debitNotes.forEach { dnObj ->
                    var copyDnObj = dnObj.copy()

                    var copyItem = dnObj.debitItems.map { it.copy() }
                    log.debug("item list : ${ObjectMapper().writeValueAsString(copyItem)}")
                    copyDnObj.id = 0
                    var tenantPartnerInfo = supplierProxy.supplierByTenant(saleReturnDebitNoteDto.data.tenant)
                    if (tenantPartnerInfo.isNullOrEmpty()) throw RequestException("Partner info not found for Tenant ${saleReturnDebitNoteDto.data.tenant} ")
                    copyDnObj.type = PartnerType.VENDOR
                    copyDnObj.client = InvoiceType.VENDOR
                    copyDnObj.noteType = NoteTypes.PURCHASE_RETURN
                    copyDnObj.supplierId = tenantPartnerInfo.get(0)!!.partnerId!!
                    copyDnObj.partnerId = tenantPartnerInfo.get(0)!!.partnerId
                    copyDnObj.partnerDetailId = tenantPartnerInfo.get(0)!!.id
                    copyDnObj.supplierName = tenantPartnerInfo.get(0)!!.name!!
                    copyDnObj.debitNoteNumber = "DS${copyDnObj.debitNoteNumber}"
                    copyDnObj.returnDuringDelivery = false // reverting the flag on copy DN
                    copyDnObj.sourceNoteType = dnObj.noteType
                    var dn = debitNoteRepo.save(copyDnObj)

                    if (!copyItem.isNullOrEmpty()) {

                        copyItem.forEach {
                            it.id = 0
                            it.debitnoteId = dn.id
                            it.debitNoteNumber = dn.debitNoteNumber
                            if (bkInvoice != null) it.invoiceNumber = bkInvoice.invoiceNum
                        }
                        dn.debitItems = debitNoteDetailRepo.saveAll(copyItem)
                    }

                    dnPrObjCopy.id = 0
                    dnPrObjCopy.debitNoteNumber = "${copyDnObj.debitNoteNumber}"
                    debitNotePrRepo.save(dnPrObjCopy)

                }


            }
        } catch (e: Throwable) {
            log.error("Unable to create Debit note for purchase return ${saleReturnDebitNoteDto.data}", e)
            throw e
        }

        createAutoCN(debitNotes, company.id, saleReturnDebitNoteDto.data.returnType, dsCompanyMapping?.tenant?:null, saleReturnDebitNoteDto.data.tenant, company.enableRioAutoCn)
        return debitNotes
    }

    fun createAutoCN(dn: MutableList<DebitNote>, companyId: Long, returnEventType: DebitNoteReturnEventType? = DebitNoteReturnEventType.REGULAR, ds: String? = null, eventTenant: String? = "", isRioEnabled: Boolean = false, rioReturnDebitNoteDto: RioReturnEventDto? = null, isCopyEvent: Boolean? = false) {

        log.debug("Inside auto CN: DN :{} : ds: {}", returnEventType, ds )
        dn.forEach { debitNote ->
            val isAutoSettlement = (returnEventType == DebitNoteReturnEventType.PARTIAL_CANCELLATION || returnEventType == DebitNoteReturnEventType.FULL_CANCELLATION || returnEventType == DebitNoteReturnEventType.WMS_CANCELLATION || returnEventType == DebitNoteReturnEventType.OMS_CANCELLATION)
            if (debitNote.client == InvoiceType.PE || debitNote.client == InvoiceType.ML || debitNote.client == InvoiceType.INTRA_TRANSFER || debitNote.client == InvoiceType.INTER_TRANSFER || (debitNote.client == InvoiceType.RIO || debitNote.client == InvoiceType.CS)) {
                var checkSystemConfig: SystemConfig? = null
                log.debug("inside if")
                if (!(debitNote.client == InvoiceType.RIO && isAutoSettlement) && (debitNote.client != InvoiceType.INTER_TRANSFER && debitNote.client != InvoiceType.INTRA_TRANSFER) && (debitNote.noteType == NoteTypes.SR_EXPIRED || debitNote.noteType == NoteTypes.SR_ACCEPTED || debitNote.noteType == NoteTypes.SR_DAMAGED || debitNote.noteType == NoteTypes.NSR_ACCEPTED || debitNote.noteType == NoteTypes.NSR_EXPIRED || debitNote.noteType == NoteTypes.NSR_DAMAGED)) {
                    log.debug("configClient : {}" ,debitNote.client)
                    val noteType = debitNote.noteType
                    checkSystemConfig = systemConfigService.getPartnerConfig(debitNote.partnerId!!, debitNote.supplierName, companyId, noteType,debitNote.tenant, debitNote.partnerDetailId!!)

                    if ((!checkSystemConfig.isAutoCN && !AUTO_CN_ENABLED_CLIENTS.contains(debitNote.client)) ||
                        (checkSystemConfig.isAutoCN && checkSystemConfig.cnConversionLimit < debitNote.amountReceivable.toDouble())) {
                        log.debug("auto cn config : {} : max conversion limit : {} : proof cn amount : {}", checkSystemConfig.isAutoCN, checkSystemConfig.cnConversionLimit, debitNote.amountReceivable)
                        return
                    }
                }
                log.debug("FSC obj : ${ObjectMapper().writeValueAsString(checkSystemConfig)}")
                debitNote.debitItems.forEach {
                    it.consumed = true
                    if (debitNote.noteType == NoteTypes.SR_EXPIRED) {

                        val mrp = it.amount * it.returnQty!!

                        val percentage = it.invoiceAmount?.divide(mrp.toBigDecimal(), 5, RoundingMode.CEILING)?.multiply(BigDecimal(100))?.toDouble()
                                ?: 100.0
                        if (checkSystemConfig != null && !checkSystemConfig.isDynamicPercentage) {
                            if (checkSystemConfig.conversionPercentage.toDouble() > percentage)
                                it.conversion = percentage
                            else
                                it.conversion = checkSystemConfig.conversionPercentage.toDouble()
                        } else {
                            it.conversion = percentage
                        }

                    } else {
                        it.conversion = if (checkSystemConfig != null && debitNote.noteType == NoteTypes.SR_ACCEPTED) checkSystemConfig.conversionPercentage.toDouble() else 100.0
                    }
                    if (rioReturnDebitNoteDto != null) {
                        it.conversion = rioReturnDebitNoteDto.cnConversionPercentage?.toDouble()
                    }
                    if (rioReturnDebitNoteDto == null)
                        it.creditItemAmount = it.invoiceAmount
                }
                val tenant = if (debitNote.client == InvoiceType.RIO && eventTenant!!.isNotEmpty()) {
                    eventTenant
                } else {
                    debitNote.tenant
                }
                val creditNote = CreditNote(0, now(), now(), "SYSTEM", "", NoteStatus.PENDING, debitNote.noteType, if (rioReturnDebitNoteDto == null || isCopyEvent == true) debitNote.amountReceivable else rioReturnDebitNoteDto?.totalCnValue
                        ?: BigDecimal.ZERO, debitNote.supplierId, debitNote.supplierName, debitNote.remarks, null, null, mutableListOf(debitNote), CreditNoteClosureType.SETTLEMENT, debitNote.partnerId, debitNote.partnerDetailId, tenant, null, debitNote.type, debitNote.client, null, null, null, null, null, 100.0, remainingAmount = if (rioReturnDebitNoteDto == null || isCopyEvent == true) debitNote.amountReceivable else rioReturnDebitNoteDto?.totalCnValue
                        ?: BigDecimal.ZERO)
                log.debug("creditnotes obj : {} ", ObjectMapper().writeValueAsString(creditNote))
                val cn = creditNoteService.save("SYSTEM", creditNote, tenant, PartnerType.CUSTOMER, ds, false, rioReturnDebitNoteDto, isCopyEvent)
                if (debitNote.client == InvoiceType.RIO && isAutoSettlement && debitNote.bkInvoices.isNotEmpty()) {
                    createAutoSettlement(debitNote, creditNote, returnEventType, tenant, ds)
                }
                if (rioReturnDebitNoteDto != null)
                    eventPublisherUtil.createVaultCnPdfEvent(CreditNotePdfEventDto(rioReturnDebitNoteDto.packageId,cn.creditNoteNumber?:"",cn.amount,"",cn.noteType.name, cn.tenant!!,cn.partnerDetailId!!,null))

            }

        }

    }

    private fun createAutoSettlement(
            debitNote: DebitNote,
            creditNote: CreditNote,
            returnEventType: DebitNoteReturnEventType?,
            tenant: String,
            ds: String? = null,
            isCancellationEvent: Boolean = false
    ) {
        val company = companyService.getCompanyTenantMappingObject(ds?:tenant) ?: throw RequestException("No tenant Mapping found")
        val systemConfig = systemConfigService.getPartnerConfig(debitNote.partnerId!!, debitNote.supplierName, company.companyId, debitNote.noteType, tenant, debitNote.partnerDetailId!!)
        //full and partial cancellation cn should get autosettle desipte of cn limit
        if(!isCancellationEvent && (!systemConfig.isAutoSettlement || (systemConfig.isAutoSettlement && systemConfig.cnAutoSettlementLimit < creditNote.amount.toDouble()))){
            return
        }
        val bkInvoice = debitNote.bkInvoices[0]
        val remainingAmount = (bkInvoice.amount - bkInvoice.paidAmount).toBigDecimal()
        if (bkInvoice.status in listOf(
                        InvoiceStatus.PARTIAL_PAID,
                        InvoiceStatus.PENDING
                ) && remainingAmount > BigDecimal.ZERO
        ) {
            val bkInvoiceCopy = bkInvoice.copy()
            val eligibleCNAmount = remainingAmount.min(debitNote.amountReceivable)
            bkInvoiceCopy.paidAmount = bkInvoiceCopy.paidAmount.plus(eligibleCNAmount.toDouble())
            creditNote.amountUsed = eligibleCNAmount
            var remarks = "Settled for Ds Partial Delivery"
            if (returnEventType == DebitNoteReturnEventType.FULL_CANCELLATION) remarks =
                    "Settled for full delivery cancellation"
            if (returnEventType == DebitNoteReturnEventType.WMS_CANCELLATION) remarks = "Settled for wms cancellation"
            if (returnEventType == DebitNoteReturnEventType.OMS_CANCELLATION) remarks = "Settled for oms cancellation"
            val settlement = Settlement(
                0,
                null,
                null,
                null,
                debitNote.partnerId!!,
                debitNote.supplierName,
                remainingAmount.toDouble(),
                eligibleCNAmount.toDouble(),
                remarks,
                null,
                mutableListOf(bkInvoiceCopy),
                mutableListOf(creditNote),
                PaymentType.CREDITNOTE,
                "",
                LocalDate.now(),
                debitNote.partnerId,
                null,
                PartnerType.CUSTOMER,
                tenant
                    ?: "",
                null,
                null,
                null,
                false,
                reversed = false,
                advancePayment = mutableListOf(),
                chargeInvoice = mutableListOf(),
                charge = false,
                receipt = null,
                paymentSource = AdvancePaymentSource.SYSTEM,
                retailerDebitNotes = mutableListOf(),
                uuid = UUIDUtil.generateUuid()
            )
            settlementService.save("SYSTEM", settlement, ds)
        }
    }

    @Transactional
    fun createDebitNotes(purchaseReturnDebitNoteDto: PurchaseReturnDebitNoteDto): DebitNote? {
        var tenants = companyTenantMappingRepo.getAllTenantByTenant(purchaseReturnDebitNoteDto.tenant)

        if (tenants.isNullOrEmpty()) throw RequestException("No tenants found!")

        val suppId = purchaseReturnDebitNoteDto.purchaseReturns[0].supplierId
        val partnerId = supplierProxy.getMasterId(suppId)
                ?: throw RequestException("Could not get Partner detail for supplier $suppId for tenant ${purchaseReturnDebitNoteDto.tenant}")

        val supplierList = supplierProxy.supplier(listOf(partnerId))

        val supplier = if (supplierList.isNotEmpty()) supplierList[0]
        else throw RequestException("Could not get Supplier List for supplier $suppId for tenant ${purchaseReturnDebitNoteDto.tenant}")

        var amount = 0.0
        var returnQty = 0

        val invoices = mutableListOf<BkInvoice>()
        val dnPrs = mutableListOf<DebitNotePr>()
        val itemList: MutableList<DebitNoteDetails> = mutableListOf()
        var invoicePresentInDb = true
        purchaseReturnDebitNoteDto.purchaseReturns.forEach { pr ->

            if (pr.prType != "PR_SALES") {

                val mercuryInvoice = invoiceProxy.getByInvoiceId(pr.invoiceId!!, purchaseReturnDebitNoteDto.tenant)
                        ?: throw RequestException("Invoice id: ${pr.invoiceId} not found")

                if (mercuryInvoice.purchaseType !in arrayOf("JIT", "PROCUREMENT", "RECTIFICATION")) {
                    return null
                }

                var bkInvoice = bkInvoiceRepo.getByInvoiceId(pr.invoiceId.toString(), pr.invoiceNo!!, tenants)
                if (bkInvoice == null) {
                    val mercuryItems = mutableListOf<BkItem>()
                    mercuryInvoice.items.forEach { item ->
                        val bkItem = BkItem(0, null, null, item.id!!, item.code!!, item.batch!!,
                                item.mrp!!, item.totalQuantity, item.effectivePurchaseRate!!.toBigDecimal(), item.effectivePurchaseRate!!.toBigDecimal(),
                                item.discount!!.toBigDecimal(), item.itemDiscountAmount!!.toBigDecimal(), item.cgst!!.toBigDecimal(),
                                item.sgst!!.toBigDecimal(), item.igst!!.toBigDecimal(), item.hsn, item.mrp!!.toBigDecimal(),
                                BigDecimal.ZERO, item.abettedMrp!!.toBigDecimal(), item.name, item.mrp!!.toBigDecimal(), mercuryInvoice.invoiceNo,
                                null, item.expiryDate, item.schemeDiscountPercentage!!.toBigDecimal(), item.schemeQuantity,
                                null, null, null, item.quantity, item.schemeQuantity.toBigDecimal(), null,
                                item.purchaseRate!!.toBigDecimal(), item.schemeDiscount!!.toBigDecimal())

                        mercuryItems.add(bkItem)
                    }
                    bkInvoice = BkInvoice(0, mercuryInvoice.createdOn, mercuryInvoice.updatedOn, null, null, pr.invoiceId.toString(),
                            pr.invoiceNo, pr.supplierId, "", mercuryInvoice.totalAmount, 0.0, mercuryInvoice.dueDate,
                            InvoiceStatus.PENDING, mercuryItems, null, mercuryInvoice.purchaseType, partnerId,
                            mercuryInvoice.partnerDetailId, purchaseReturnDebitNoteDto.tenant, PartnerType.VENDOR, InvoiceType.VENDOR)
                    invoicePresentInDb = false
                }

                var bkItemMap = bkInvoice.items.groupBy { Pair(it.batch, it.ucode) }

                pr.defects.forEach { d ->
                    returnQty = d.returnQuantity

                    if (bkItemMap.containsKey(Pair(d.batch, d.ucode))) {
                        var item = bkItemMap.get(Pair(d.batch, d.ucode))!!
                        var itm = item.get(0)
                        if (!pr.calculated) {

                            val igst = itm.igst?.toDouble()
                            val sgst = itm.sgst?.toDouble()
                            val cgst = itm.cgst?.toDouble()
                            val discountPerStrip = itm.discountAmount?.div(itm.quantity.toBigDecimal())
                                    ?: BigDecimal.ZERO
                            var taxableValue = (itm.purchaseRate?.minus(discountPerStrip!!))?.times(returnQty.toBigDecimal())
                                    ?: BigDecimal.ZERO
                            var netGstAmt = if (igst == 0.0) ((sgst?.plus(cgst!!))?.times(taxableValue!!.toDouble()))?.div(100) else (igst?.times(taxableValue!!.toDouble()))?.div(100)
                            netGstAmt = netGstAmt ?: 0.0
                            var invoiceAmt = (taxableValue?.toDouble()?.plus(netGstAmt))

                            itemList.add(DebitNoteDetails(null, purchaseReturnDebitNoteDto.purchaseReturns[0].updatedOn, purchaseReturnDebitNoteDto.purchaseReturns[0].updatedOn, itm.itemId, itm.ucode, itm.batch, itm.amount, itm.quantity, itm.epr, itm.trueEpr,
                                    itm.discountPercentage, itm.discountAmount, itm.cgst, itm.sgst, itm.igst, itm.hsn, invoiceAmt?.toBigDecimal(), netGstAmt.toBigDecimal(), itm.abtMrp, itm.name, taxableValue, itm.invoiceNumber,
                                    returnQty, null, null, null, null, null, itm.expiry, null, null, null, null, null,
                                    null, null, null, null, null, null, null, itm.purchaseRate, pr.id!!))
                        } else {

                            var abtMrp = (d.mrp?.toBigDecimal()?.divide(BigDecimal.ONE + ((d.igst?.plus(d.sgst
                                    ?: 0.0)?.plus(d.cgst ?: 0.0))?.toBigDecimal()
                                    ?: BigDecimal.ZERO), 5, RoundingMode.CEILING))
                            var netGstAmt = d.igstAmount?.plus(d.sgstAmount ?: 0.0)?.plus(d.cgstAmount ?: 0.0)
                            var taxableValue = netGstAmt?.let { d.amount?.minus(it) }
                            itemList.add(DebitNoteDetails(null, purchaseReturnDebitNoteDto.purchaseReturns[0].updatedOn, purchaseReturnDebitNoteDto.purchaseReturns[0].updatedOn, d.id!!, d.ucode, d.batch, d.mrp
                                    ?: 0.0, itm.quantity, d.epr!!.toBigDecimal(), d.epr!!.toBigDecimal(),
                                    d.discountPercentage?.toBigDecimal(), d.discountAmount?.toBigDecimal(), d.cgst?.toBigDecimal(), d.sgst?.toBigDecimal(), d.igst?.toBigDecimal(), itm.hsn, d.amount?.toBigDecimal(), netGstAmt?.toBigDecimal(), abtMrp, d.name, taxableValue?.toBigDecimal(), itm.invoiceNumber,
                                    returnQty, null, null, null, null, null, itm.expiry, null, null, null, null, null,
                                    null, null, null, null, null, null, null, itm.purchaseRate, pr.id!!))

                        }
                    }
                }
                invoices.add(bkInvoice)
                val prAmount = if (!pr.calculated)
                    calculateInvoicePrAmount(pr, mercuryInvoice!!
                    ) else {
                    itemList.sumByDouble { it.invoiceAmount!!.toDouble() }
                }

                log.debug("after :: calculateInvoicePrAmount execute :: amount $amount prAmount $prAmount")
                amount += prAmount

                log.debug("final debit note amount: $amount")
                dnPrs.add(DebitNotePr(0, pr.createdOn, pr.updatedOn, pr.createdBy, pr.updatedBy, pr.debitNoteNumber!!,
                        pr.id!!.toString(), pr.invoiceNo, BigDecimal(prAmount), purchaseReturnDebitNoteDto.tenant))
            } else {
                createPrSaleInvoice(pr, supplier!!, purchaseReturnDebitNoteDto.tenant)
            }
        }

        if (purchaseReturnDebitNoteDto.purchaseReturns[0].prType == "PR_SALES") return null

        val dn = DebitNote(
                id = 0,
                createdOn = now(),
                updatedOn = now(),
                supplierId = suppId,
                supplierName = supplier!!.partnerName!!,
                purchaseTransactionNumber = null,
                debitNoteNumber = purchaseReturnDebitNoteDto.purchaseReturns[0].debitNoteNumber!!,
                createdBy = purchaseReturnDebitNoteDto.user,
                updatedBy = purchaseReturnDebitNoteDto.user,
                amountReceivable = amount.toBigDecimal(),
                amountReceived = BigDecimal(0),
                remarks = null,
                creditNoteId = null,
                creditNoteNumber = null,
                status = NoteStatus.PENDING,
                noteType = NoteTypes.PURCHASE_RETURN,
                bkInvoices = invoices,
                tenant = purchaseReturnDebitNoteDto.tenant,
                partnerId = partnerId,
                partnerDetailId = suppId,
                invoiceAt = invoices[0].createdOn
        )

        log.debug("going to save debit note")
        val debitNote = save(purchaseReturnDebitNoteDto.user, dn, itemList, invoicePresentInDb)

        debitNotePrRepo.saveAll(dnPrs)
        return debitNote
    }

    private fun calculateInvoicePrAmount(purchaseIssue: PurchaseIssue, invoice: MercuryInvoice): Double {
        log.debug("Inside calculateInvoicePrAmount :: $purchaseIssue : MercuryInvoice:: $invoice")
        var sumAmt = 0.0
        //var amt = 0.0
        purchaseIssue.defects.forEach { prItem ->
            val inItem = invoice.items.filter { it.code == prItem.ucode && it.batch?.toUpperCase() == prItem.batch.toUpperCase() }.first()
            log.debug("calculateInvoicePrAmount :: inItem $inItem")
            var amt = (inItem.purchaseRate!! * prItem.returnQuantity) * ((100.0 - (inItem.discount ?: 0.0)) / 100)

            log.debug("amount before gst - $amt")

            var gst = if (invoice.interstate) inItem.igst!!
            else inItem.cgst!! + inItem.sgst!!

            var gstAmount = amt * (gst / 100)
            amt += gstAmount
            sumAmt += amt
            log.debug("sum amount $sumAmt")
            log.debug("purchaseRate ${inItem.purchaseRate} : returnQuantity ${prItem.returnQuantity} : discount ${inItem.discount} : gst $gst : gstAmount $gstAmount : amt $amt")
        }

        return sumAmt
    }

    fun getDebitNotesForCreditNote(id: Long): List<DebitNote> {
        log.debug("Inside get debit notes for credit note: $id")

        return debitNoteRepo.getDebitNotesForCreditNote(id)
    }

    fun getDebitNotesForCreditNoteNumber(creditNoteNumber: String): List<DebitNote> {
        log.debug("Inside get debit notes for credit note: $creditNoteNumber")

        return debitNoteRepo.getDebitNotesForCreditNoteNumber(creditNoteNumber)
    }

    fun getVendorDebitNotesData(partnerIds: List<Long>?, partnerId: Long?, partnerDetailId: Long?, startAt: LocalDate?, endAt: LocalDate?, noteType: NoteTypes?, tenant: String, page: Int?, size: Int?, customerType: Boolean, ds: String? = null, onClick: Boolean?, firmType: List<Int>?, client: List<InvoiceType>): PaginationDto {
        log.debug("Inside getVendorInvoicesData $partnerIds: $tenant : ds :$ds")

        var custType: PartnerType = PartnerType.VENDOR
        if (customerType) custType = PartnerType.CUSTOMER

        val today: LocalDateTime = if (endAt != null) {
            getUTCDateTime(endAt.atTime(23, 59, 59))
        } else {
            getISTDateTime(LocalDate.now().atTime(23, 59, 59))!!
        }
        val from: LocalDateTime = if (startAt != null) {
            getUTCDateTime(startAt.atTime(0, 0, 0))
        } else {
            getUTCDateTime(today.minusYears(10))
        }

        var status = if (onClick == true)
            NoteStatus.PENDING
        else
            null

        var tenants = companyService.findTenants(ds ?: tenant)
        if (tenants.isEmpty()) throw RequestException("Your tenant: $tenant is not mapped to any Company")

        val pagination = PageRequest.of(page ?: 0, size ?: 10)
        var partnerIdFiltered: List<Long>?
        var getAllFlag: Boolean = false

        if (partnerId != null) {
            partnerIdFiltered = if (partnerIds != null) {
                if (partnerIds.contains(partnerId)) listOf<Long>(partnerId) else listOf<Long>()
            } else {
                listOf<Long>(partnerId)
            }
        } else {
            if (!partnerIds.isNullOrEmpty()) {
                partnerIdFiltered = partnerIds
            } else {
                partnerIdFiltered = listOf<Long>()
                getAllFlag = true
            }
        }

        var res = if (partnerIdFiltered.isNullOrEmpty()) {
            if (getAllFlag) {
                debitNoteReadRepo.getVendorDebitNotesData(from, today, noteType, partnerDetailId, tenants, status, custType, client, pagination)
            } else {
                Page.empty<VendorDebitNoteDto>()
            }
        } else {
            debitNoteReadRepo.getVendorDebitNotesDataWithVendorIds(partnerIdFiltered, noteType, partnerDetailId, from, today, tenants, status, custType, client, pagination)
        }

        var supplierIdList = res.map { it.vendorId }.toMutableList()
        var supplierMap = supplierProxy.supplier(supplierIdList).associateBy { it?.partnerId }
        var vendorMap = mutableMapOf<Long, String>()
        if (customerType && !supplierIdList.isNullOrEmpty()) {
            var vendorClient = debitNoteReadRepo.getVendorClient(from, today, custType, tenants, supplierIdList)
            vendorClient.forEach {
                var listOfClient = vendorMap.get(it.vendorId)
                if (listOfClient == null) {
                    vendorMap[it.vendorId!!] = it.client ?: ""
                } else {
                    vendorMap[it.vendorId!!] = listOfClient + "/" + it.client
                }

            }
        }

        res.forEach { vendorDebitNoteDto ->
            if (vendorDebitNoteDto.vendorId != null) {
                val supplier = supplierMap.get(vendorDebitNoteDto.vendorId)
                if (supplier != null) vendorDebitNoteDto.vendorName = supplier.partnerName!!
                if (customerType) {
                    vendorDebitNoteDto.client = vendorMap.get(vendorDebitNoteDto.vendorId ?: 0L)
                }
            }
        }

        return PaginationDto(res.totalElements, res.totalPages, res.hasPrevious(), res.hasNext(), res)
    }

    fun getAggregatedDebitNoteData(partnerIds: List<Long>?, partnerId: Long?, partnerDetailId: Long?, startAt: LocalDate?, endAt: LocalDate?, noteType: NoteTypes?, tenant: String, customerType: Boolean, ds: String? = null, firmType: List<Int>?, client: List<InvoiceType>): AggregatedDebitNoteDataDto {
        log.debug("Inside getAggregatedDebitNoteData $partnerIds : $tenant")
        var custType: PartnerType = PartnerType.VENDOR
        if (customerType) custType = PartnerType.CUSTOMER

        val today: LocalDateTime = if (endAt != null) {
            getUTCDateTime(endAt.atTime(23, 59, 59))
        } else {
            getUTCDateTime(LocalDate.now().atTime(23, 59, 59))
        }
        val from: LocalDateTime = if (startAt != null) {
            getUTCDateTime(startAt.atTime(0, 0, 0))
        } else {
            getUTCDateTime(today.minusYears(10))
        }

        val tenants = companyService.findTenants(ds ?: tenant)
        if (tenants.isEmpty()) throw RequestException("Your tenant: $tenant is not mapped to any Company")
        log.debug("$from : $today : $tenant : $tenants : $partnerIds")

        var res: AggregatedDebitNoteDataDto
        val mapper = jacksonObjectMapper()
        var debitNoteAggregatedDataCacheKey = "bookkeeper_debit_note_aggregated_data_${tenant}_${custType}_${ds}"
        if (partnerIds.isNullOrEmpty() && noteType == null && partnerId == null && startAt == null && endAt == null && firmType.isNullOrEmpty()) {
            // using cache only for summary page - not for other filter lookups
            var cacheResponseString = redisUtilityService.getfromRedis(debitNoteAggregatedDataCacheKey)
            if (!cacheResponseString.isNullOrEmpty()) {
                var cacheResponseDTO: AggregatedDebitNoteDataDto = mapper.readValue(cacheResponseString)
                return cacheResponseDTO
            }
        }

        var partnerIdFiltered: List<Long>?
        var getAllFlag: Boolean = false

        if (partnerId != null) {
            if (partnerIds != null) {
                partnerIdFiltered = if (partnerIds.contains(partnerId)) listOf<Long>(partnerId) else listOf<Long>()
            } else {
                partnerIdFiltered = listOf<Long>(partnerId)
            }
        } else {
            if (!partnerIds.isNullOrEmpty()) {
                partnerIdFiltered = partnerIds
            } else {
                partnerIdFiltered = listOf<Long>()
                getAllFlag = true
            }
        }


        try {
            if (partnerIdFiltered.isNullOrEmpty()) {
                if (getAllFlag) {
                    res = debitNoteRepo.getAggregatedDebitNoteData(from, today, noteType, custType, tenants, partnerDetailId, client)
                    if (noteType == null && startAt == null && endAt == null) {
                        redisUtilityService.setToRedis(debitNoteAggregatedDataCacheKey, mapper.writeValueAsString(res), null)
                        log.debug("Cache miss for debitnote aggregated data $debitNoteAggregatedDataCacheKey. Cache set at ${now()}")
                    }
                } else {
                    res = AggregatedDebitNoteDataDto()
                }
            } else {
                res = debitNoteRepo.getAggregatedDebitNoteDataWithPartnerIds(partnerIdFiltered, from, today, noteType, custType, tenants, partnerDetailId, client)
            }
        } catch (e: Exception) {
            res = AggregatedDebitNoteDataDto(totalPendingDebitNotes = 0, totalPendingDebitNoteAmount = BigDecimal.ZERO,
                    totalClosedDebitNotes = 0, totalRecordedDebitNotes = 0L)
        }

        return res
    }

// debit note url

    fun getDebitNoteURL(tenant: String, createdBy: String?, customerType: Boolean, ds: String? = null, firmType: List<Int>?, startAt: LocalDate?, endAt: LocalDate?, client: List<InvoiceType>): CreateResultData {
        log.debug("Inside getDebitNoteURL")
        var tenants = companyService.findTenants(ds ?: tenant!!)
        if (tenants.isNullOrEmpty()) throw RequestException("no tenant mapping found for $tenant !")
        var custType: PartnerType = PartnerType.VENDOR
        if (customerType) custType = PartnerType.CUSTOMER
        if (tenants.isNullOrEmpty()) return CreateResultData(200, "No Data", null)

        val to: LocalDateTime = if (endAt != null) {
            endAt.atTime(23, 59, 59)
        } else {
            LocalDate.now().atTime(23, 59, 59)
        }
        val from: LocalDateTime = if (startAt != null) {
            startAt.atTime(0, 0, 0)
        } else {
            to.minusYears(10)
        }


        var list: MutableList<VendorDebitNoteDto?> = mutableListOf()
        if (client == null && from != null && to != null) {
            list = debitNoteRepo.getVendorDebitNotesDataForUrl(custType, tenants, from, to)
        } else if (client != null && from != null && to != null) {
            list = debitNoteRepo.getVendorDebitNotesDataForUrlWithPidANDDate(custType, tenants, client, from, to)
        } else if (client != null && from == null && to == null) {
            list = debitNoteRepo.getVendorDebitNotesDataForUrlWithPid(custType, tenants, client)
        }


        if (list.isNullOrEmpty())
            throw RequestException("Data is not available")

        var prefix = "DebitList-"


        try {
            var file = vendorFileService.saveReport(tenants[0]!!, VendorDataLinks(null, now(), null, "IN_PROGRESS", VendorDataEnum.DEBIT, null, createdBy, tenants[0]!!, customerType))

            writeDebitData(file.id!!, list, tenant, prefix, customerType)

        } catch (e: Exception) {
            e.printStackTrace()
        }
        return CreateResultData(200, "Success", null)

    }


    @Async
    fun writeDebitData(id: Long, successList: MutableList<VendorDebitNoteDto?>, tenant: String, prefix: String?, customerType: Boolean) {
        log.debug("Inside writeDebitData: writting data into csv")

        var csvPrinter: CSVPrinter
        var bytes = ByteArrayOutputStream()

        if (customerType) {
            csvPrinter = CSVPrinter(bytes.bufferedWriter(), CSVFormat.DEFAULT
                    .withHeader("Customer Id", "Customer Name", AccountConstants.CUSTOMER_TYPE, "No Of Debit Notes", "No Of Recorded Debit Notes", "No Of Pending Debit Notes", "Pending Debit Notes AMT"))
        } else {
            csvPrinter = CSVPrinter(bytes.bufferedWriter(), CSVFormat.DEFAULT
                    .withHeader("Vendor Id", "Vendor Name", "No Of Debit Notes", "No Of Recorded Debit Notes", "No Of Pending Debit Notes", "Pending Debit Notes AMT"))
        }
        var supplierList: List<Supplier?>? = null
        successList!!.forEach {
            if (it != null) {
                //var vendorName: String? = null
                if (it.vendorId != null)
                // vendorName =  partnerService.getSupplierName(it.vendorId!!, companyMappingObj.companyId!!,type)!!
                    supplierList = supplierProxy.supplier(listOf(it.vendorId))
                val supplier = if (supplierList!!.isNotEmpty()) supplierList!![0] else null
                if (supplier != null) it.vendorName = supplier.partnerName!!
                if (customerType) {
                    csvPrinter.printRecord(it.vendorId, it.vendorName, it.client, it.totalDebitNotes, it.totalRecordedDebitNotes, it.totalPendingDebitNotes, it.totalPendingDebitNoteAmount)
                } else
                    csvPrinter.printRecord(it.vendorId, it.vendorName, it.totalDebitNotes, it.totalRecordedDebitNotes, it.totalPendingDebitNotes, it.totalPendingDebitNoteAmount)
            }
        }

        csvPrinter.flush()
        val uploaded = s3FileUtilityService.uploadFile(bytes.toByteArray(), "CSV", prefix)
        vendorFileService.update(id, uploaded ?: "Failed")
    }


    fun getDebitDownload(createdBy: String, tenant: String, customerType: Boolean?, ds: String? = null): CreateResultData {

        var tenants = companyService.findTenants(ds ?: tenant)
        var file = debitDataLinksRepo.getDebitLink(createdBy, tenants[0], customerType)
        if (file.isNotEmpty()) {
            return CreateResultData(200, "Success", file[0]!!.link)
        }

        return CreateResultData(200, "Success", null)
    }


    // debit note detail url
    fun getDebitNoteDetailURL(partnerId: Long?, tenant: String, createdBy: String?, from: LocalDate?, to: LocalDate?, status: NoteStatus?, customerType: Boolean, ds: String? = null): CreateResultData {
        log.debug("Inside getDebitNoteDetailURL")
        var custType: PartnerType = PartnerType.VENDOR
        if (customerType) custType = PartnerType.CUSTOMER
        var tenants = companyService.findTenants(ds ?: tenant)
        if (tenants.isEmpty()) throw RequestException("Your tenant: $tenant is not mapped to any Company")

        val list = debitNoteRepo.findAll(SupplierDebitNoteSpecification(partnerId, status, null, null, tenants, from, to, null, custType))

        if (list.isNullOrEmpty())
            throw RequestException("Data is not available")

        var prefix = "DebitDetailList-"


        try {
            var file = vendorFileService.saveReport(tenants[0]!!, VendorDataLinks(null, now(), null, "IN_PROGRESS", VendorDataEnum.DEBIT_DETAIL, null, createdBy, tenants[0]!!, customerType))

            writeDebitDetailData(file.id!!, list, tenants[0]!!, prefix, customerType)

        } catch (e: Exception) {
            e.printStackTrace()
        }
        return CreateResultData(200, "Success", null)

    }


    @Async
    fun writeDebitDetailData(id: Long, successList: MutableList<DebitNote?>, tenant: String, prefix: String?, customerType: Boolean) {
        log.debug("Inside writeDebitDetailData: writing data into csv")

        val csvPrinter: CSVPrinter
        var bytes = ByteArrayOutputStream()

        if (customerType) {
            csvPrinter = CSVPrinter(bytes.bufferedWriter(), CSVFormat.DEFAULT
                    .withHeader("Debit Note No.", "Created On", "SR Txn No.", "Invoice No.", "Source", "Debit Note Type", "Debit Note AMT", "Credit Note No.", "Document Status"))
        } else {
            csvPrinter = CSVPrinter(bytes.bufferedWriter(), CSVFormat.DEFAULT
                    .withHeader("Debit Note No.", "Created On", "Invoice No.", "Source", "Debit Note Type", "Debit Note AMT", "Credit Note No.", "Document Status"))
        }

        successList!!.forEach {
            if (it != null) {
                var tenants = companyService.findTenants(tenant)
                if (tenants.isEmpty()) throw RequestException("Your tenant: $tenant is not mapped to any Company")
                val invoiceNum = invoiceDebitNoteRepo.getInvoicesForDebitNote(it.id)
                var s = StringBuffer()
                var invNum = StringBuffer()
                var idd: Long? = null

                it.debitItems.forEach { i -> idd = i.id }
                invoiceNum.forEach { inv ->
                    s.append(inv.invoiceNum + ",")
                }

                it.debitItems.forEach { i -> invNum.append(i.invoiceNumber + ",") }
                if (customerType && it.noteType.name.equals(NoteTypes.DISCOUNT.name)) {
                    csvPrinter.printRecord(it.debitNoteNumber, it.createdOn?.toLocalDate(), idd, invNum, tenants[0]!!, it.noteType, it.amountReceivable, it.creditNoteNumber, it.status)
                } else if (customerType) {
                    csvPrinter.printRecord(it.debitNoteNumber, it.createdOn?.toLocalDate(), idd, s, tenants[0]!!, it.noteType, it.amountReceivable, it.creditNoteNumber, it.status)
                } else if (it.noteType.name.equals(NoteTypes.DISCOUNT.name)) {
                    csvPrinter.printRecord(it.debitNoteNumber, it.createdOn?.toLocalDate(), invNum, tenants[0]!!, it.noteType, it.amountReceivable, it.creditNoteNumber, it.status)
                } else {
                    csvPrinter.printRecord(it.debitNoteNumber, it.createdOn?.toLocalDate(), s, tenants[0]!!, it.noteType, it.amountReceivable, it.creditNoteNumber, it.status)
                }
            }
        }

        csvPrinter.flush()
        val uploaded = s3FileUtilityService.uploadFile(bytes.toByteArray(), "CSV", prefix)
        vendorFileService.update(id, uploaded ?: "Failed")
    }


    fun getDebitDetailDownload(createdBy: String, tenant: String, customerType: Boolean, ds: String? = null): CreateResultData {

        var tenants = companyService.findTenants(tenant)
        if (tenants.isEmpty()) throw RequestException("Your tenant: $tenant is not mapped to any Company")
        var file = debitDataLinksRepo.getDebitDetailLink(tenants[0]!!, createdBy, customerType)
        if (file.isNotEmpty()) {
            return CreateResultData(200, "Success", file[0]!!.link)
        }

        return CreateResultData(200, "Success", null)
    }

    fun getDebitNotePrByDebitNoteNumber(debitNoteNumber: String, tenant: String, ds: String? = null): List<DebitNotePr> {
        log.debug("getting debit note pr - $debitNoteNumber : $tenant")

        var tenants = companyService.findTenants(ds ?: tenant)
        if (tenants.isNullOrEmpty()) throw RequestException("tenant: $tenant is not mapped to any company")

        val dnprs = debitNotePrRepo.getByDebitNoteNumber(debitNoteNumber, tenants)
        log.debug("tenants: $tenants , purchase returns : $dnprs ")
        return dnprs
    }

    @Transactional
    fun createDiscountDebitNote(discountDebitNoteDto: DiscountDebitNoteDto, user: String): DebitNote {
        log.debug("Inside create discount debit note $discountDebitNoteDto")

        var company = companyRepo.getCompanyByTenant(discountDebitNoteDto.tenant)
                ?: throw RequestException("Company mapping not found for ${discountDebitNoteDto.tenant}")

        var debitNoteNumber: String
        if (discountDebitNoteDto.type == PartnerType.CUSTOMER) {
            debitNoteNumber = documentMasterService.getDocumentNumber(user, company.companyCode, DocumentType.CX_DN_DISCOUNT)
        } else {
            debitNoteNumber = documentMasterService.getDocumentNumber(user, company.companyCode, DocumentType.VN_DN_DISCOUNT)
        }
        var invoiceObject = bkInvoiceRepo.getByInvoiceNum(discountDebitNoteDto.invoiceNumber!!, mutableListOf(discountDebitNoteDto.tenant))

        val items = mutableListOf<DebitNoteDetails>()
        if (invoiceObject[0]?.apiVersion == APIVersionType.V1) {
            discountDebitNoteDto.items.forEach { item ->

                val oldItem = bkItemRepo.get(item.itemId)
                        ?: throw RequestException("Item with id: ${item.itemId} not found in item table")

                val debitNoteDetails = DebitNoteDetails(
                        id = null,
                        createdOn = now(),
                        updatedOn = now(),
                        itemId = item.itemId,
                        ucode = item.ucode,
                        batch = item.batch,
                        amount = oldItem.amount,
                        quantity = oldItem.quantity,
                        epr = oldItem.epr,
                        trueEpr = oldItem.trueEpr,
                        discountAmount = item.discountAmount,
                        discountPercentage = item.discountPercentage,
                        cgst = oldItem.cgst,
                        sgst = oldItem.sgst,
                        igst = oldItem.igst,
                        hsn = oldItem.hsn,
                        invoiceAmount = oldItem.invoiceAmount,
                        netGstAmt = oldItem.netGstAmt,
                        abtMrp = oldItem.abtMrp,
                        name = item.name,
                        taxableAmount = BigDecimal.ZERO,
                        invoiceNumber = oldItem.invoiceNumber,
                        returnQty = item.discountQuantity!!.toInt(),
                        conversion = 100.0,
                        cnTaxableValue = BigDecimal.ZERO,
                        cnBillValue = BigDecimal.ZERO,
                        cnMrpValue = BigDecimal.ZERO,
                        cnNetGST = BigDecimal.ZERO,
                        debitnoteId = null,
                        expiry = oldItem.expiry,
                        schemePercent = oldItem.schemePercent,
                        quantityFree = oldItem.quantityFree,
                        ptr = oldItem.ptr,
                        schemeType = oldItem.schemeType,
                        gst = oldItem.gst,
                        quantityOrdered = oldItem.quantityOrdered,
                        quantityScheme = oldItem.quantityScheme,
                        totalInvoiceQty = 0,
                        invoiceTaxableValue = BigDecimal.ZERO,
                        dnTaxableRate = BigDecimal.ZERO,
                        consumed = true,
                        creditItemAmount = oldItem.invoiceAmount
                )


                items.add(debitNoteDetails)
            }
        } else {
            var invoiceItems: RioInvoiceItemsDto? = invoiceService.getRioInvoiceItemData(invoiceObject[0]?.distributorPdi!!, invoiceObject[0]?.partnerDetailId!!, discountDebitNoteDto.invoiceNumber!!)
                    ?: throw RequestException("no item data found for")
            var pair = invoiceItems?.invoiceItems?.groupBy { Pair(it.batch, it.ucode) }
            discountDebitNoteDto.items.forEach { item ->

                val debitNoteDetails = DebitNoteDetails(
                    id = null,
                    createdOn = now(),
                    updatedOn = now(),
                    itemId = item.itemId,
                    ucode = item.ucode,
                    batch = item.batch,
                    amount = (pair?.get(Pair(item.batch,item.ucode))!![0]?.invoiceItemAmount?:BigDecimal.ZERO).toDouble(),
                    quantity =  pair.get(Pair(item.batch,item.ucode))!![0]?.billedQty?:0,
                    epr = BigDecimal.ZERO,
                    trueEpr = BigDecimal.ZERO,
                    discountAmount = item.discountAmount,
                    discountPercentage = item.discountPercentage,
                    cgst = pair.get(Pair(item.batch,item.ucode))!![0]?.cgst?: BigDecimal.ZERO,
                    sgst = pair.get(Pair(item.batch,item.ucode))!![0]?.sgst?: BigDecimal.ZERO,
                    igst = pair.get(Pair(item.batch,item.ucode))!![0]?.igst?: BigDecimal.ZERO,
                    hsn = pair.get(Pair(item.batch,item.ucode))!![0]?.hsnCode,
                    invoiceAmount = pair.get(Pair(item.batch,item.ucode))!![0]?.invoiceItemAmount,
                    netGstAmt = pair.get(Pair(item.batch,item.ucode))!![0]?.netGSTAmount,
                    abtMrp = BigDecimal.ZERO,
                    name = item.name,
                    taxableAmount = BigDecimal.ZERO,
                    invoiceNumber = discountDebitNoteDto.invoiceNumber,
                    returnQty = item.discountQuantity!!.toInt(),
                    conversion = 100.0,
                    cnTaxableValue = BigDecimal.ZERO,
                    cnBillValue = BigDecimal.ZERO,
                    cnMrpValue = BigDecimal.ZERO,
                    cnNetGST = BigDecimal.ZERO,
                    debitnoteId = null,
                    expiry = null,
                    schemePercent = BigDecimal.ZERO,
                    quantityFree = 0,
                    ptr = pair.get(Pair(item.batch,item.ucode))!![0]?.ptr,
                    schemeType = null,
                    gst = pair.get(Pair(item.batch,item.ucode))!![0]?.gst,
                    quantityOrdered = pair.get(Pair(item.batch,item.ucode))!![0]?.billedQty,
                    quantityScheme = null,
                    totalInvoiceQty = 0,
                    invoiceTaxableValue = BigDecimal.ZERO,
                    dnTaxableRate = BigDecimal.ZERO,
                    consumed = true,
                    creditItemAmount = pair.get(Pair(item.batch,item.ucode))!![0]?.invoiceItemAmount
                )


                items.add(debitNoteDetails)
            }
        }

        val debitNote = DebitNote(
                id = 0,
                createdOn = now(),
                updatedOn = now(),
                createdBy = user,
                updatedBy = user,
                supplierId = discountDebitNoteDto.partnerId!!,
                supplierName = discountDebitNoteDto.partnerName,
                amountReceivable = discountDebitNoteDto.amountReceivable,
                status = discountDebitNoteDto.status,
                noteType = NoteTypes.DISCOUNT,
                debitItems = items,
                partnerId = discountDebitNoteDto.partnerId,
                partnerDetailId = discountDebitNoteDto.partnerDetailId,
                tenant = discountDebitNoteDto.tenant,
                type = discountDebitNoteDto.type,
                client = discountDebitNoteDto.client,
                debitNoteNumber = debitNoteNumber,
                creditNoteId = null,
                creditNoteNumber = null,
                purchaseTransactionNumber = null,
                remarks = null,
                bkInvoices = mutableListOf()
        )

        return save(user, debitNote, items)
    }

    @Transactional
    fun createNsrDebitNote(nsrDebitNoteDto: NsrDebitNoteDto, user: String): DebitNote {
        log.debug("Inside create NSR debit note $nsrDebitNoteDto")

        var company = companyRepo.getCompanyByTenant(nsrDebitNoteDto.tenant)
                ?: throw RequestException("Company mapping not found for ${nsrDebitNoteDto.tenant}")

        val debitNoteNumber = documentMasterService.getDocumentNumber(user, company.companyCode, DocumentType.DEBIT_NOTE_NSR)

        val items = mutableListOf<DebitNoteDetails>()
        nsrDebitNoteDto.items.forEach { item ->

            val debitNoteDetails = DebitNoteDetails(
                    id = null,
                    createdOn = now(),
                    updatedOn = now(),
                    itemId = 0,
                    ucode = item.ucode,
                    batch = item.batch,
                    amount = item.mrp.toDouble(),
                    quantity = 0,
                    epr = BigDecimal.ZERO,
                    trueEpr = BigDecimal.ZERO,
                    discountAmount = item.conversionAmount,
                    discountPercentage = item.conversionPercent,
                    cgst = BigDecimal.ZERO,
                    sgst = BigDecimal.ZERO,
                    igst = BigDecimal.ZERO,
                    hsn = item.reason,
                    invoiceAmount = BigDecimal.ZERO,
                    netGstAmt = BigDecimal.ZERO,
                    abtMrp = BigDecimal.ZERO,
                    name = item.name,
                    taxableAmount = BigDecimal.ZERO,
                    invoiceNumber = null,
                    returnQty = item.quantity.toInt(),
                    conversion = 0.0,
                    cnTaxableValue = BigDecimal.ZERO,
                    cnBillValue = BigDecimal.ZERO,
                    cnMrpValue = BigDecimal.ZERO,
                    cnNetGST = BigDecimal.ZERO,
                    debitnoteId = null,
                    expiry = item.expiry,
                    schemePercent = null,
                    quantityFree = null,
                    ptr = null,
                    schemeType = null,
                    gst = BigDecimal.ZERO,
                    quantityOrdered = null,
                    quantityScheme = null,
                    totalInvoiceQty = null,
                    invoiceTaxableValue = null,
                    dnTaxableRate = null
            )


            items.add(debitNoteDetails)
        }

        val debitNote = DebitNote(
                id = 0,
                createdOn = now(),
                updatedOn = now(),
                createdBy = user,
                updatedBy = user,
                supplierId = nsrDebitNoteDto.partnerId!!,
                supplierName = nsrDebitNoteDto.partnerName,
                amountReceivable = nsrDebitNoteDto.amountReceivable,
                status = nsrDebitNoteDto.status,
                noteType = NoteTypes.NSR,
                debitItems = items,
                partnerId = nsrDebitNoteDto.partnerId,
                partnerDetailId = nsrDebitNoteDto.partnerDetailId,
                tenant = nsrDebitNoteDto.tenant,
                type = nsrDebitNoteDto.type,
                client = nsrDebitNoteDto.client,
                debitNoteNumber = debitNoteNumber,
                creditNoteId = null,
                creditNoteNumber = null,
                purchaseTransactionNumber = null,
                remarks = null,
                bkInvoices = mutableListOf()
        )

        return save(user, debitNote, items)
    }

    fun getDebitNoteByDebitNoteNumber(debitNoteNumber: String, partnerId: Long): DebitNote? {
        log.debug("Inside getDebitNoteByDebitNoteNumber : $debitNoteNumber : $partnerId")

        val pagination = PageRequest.of(0, 10)
        val res = debitNoteRepo.getDebitNoteForDebitNoteNumber(debitNoteNumber, partnerId, null, pagination)

        var dn: DebitNote? = null

        if (res.content.isNotEmpty()) {
            dn = res.content[0]
        }
        return dn
    }

    fun getDebitNoteDetailsByDebitNoteNumber(debitNoteNumber: String): List<DebitNoteDetails> {
        log.debug("Inside getDebitNoteDetailsByDebitNoteNumber : $debitNoteNumber")

        return debitNoteDetailRepo.getDebitNoteDetailsByDebitNoteNumber(debitNoteNumber)
    }

    fun createStIcsDN(bkInvoice: BkInvoice, noteType: NoteTypes) {

        val itemList: MutableList<DebitNoteDetails> = mutableListOf()
        var amount = BigDecimal.ZERO

        bkInvoice.items.forEach { itm ->
            itemList.add(DebitNoteDetails(null, itm.createdOn, itm.updatedOn, itm.itemId, itm.ucode, itm.batch, itm.amount, itm.quantity, itm.epr, itm.trueEpr,
                    itm.discountPercentage, itm.discountAmount, itm.cgst, itm.sgst, itm.igst, itm.hsn, itm.invoiceAmount, itm.netGstAmt, itm.abtMrp, itm.name, itm.taxableAmount, itm.invoiceNumber,
                    itm.quantity, null, null, null, null, null, itm.expiry, null, null, null, null, null,
                    null, null, null, null, null, null, null, itm.purchaseRate, null))

            amount = amount.plus(itm.invoiceAmount!!)
        }

        var company = companyRepo.getCompanyByTenant(bkInvoice.tenant!!)
                ?: throw RequestException("Company mapping not found for ${bkInvoice.tenant}")


        var invoiceId = try {
            bkInvoice.invoiceId!!.toLong()
        } catch (e: Exception) {
            0L
        }
        val dn = DebitNote(
                id = 0,
                createdOn = now(),
                updatedOn = now(),
                supplierId = bkInvoice.partnerId!!,
                supplierName = bkInvoice.supplierName!!,
                purchaseTransactionNumber = null,
                debitNoteNumber = documentMasterService.getDocumentNumber(bkInvoice.createdBy
                        ?: "", company.companyCode, if (noteType == NoteTypes.ICS_RETURN) DocumentType.DN_ICS_RETURN else DocumentType.DN_ST_RETURN),
                createdBy = bkInvoice.createdBy ?: "",
                updatedBy = bkInvoice.createdBy ?: "",
                amountReceivable = amount,
                amountReceived = BigDecimal(0),
                remarks = null,
                creditNoteId = null,
                creditNoteNumber = null,
                status = NoteStatus.PENDING,
                noteType = noteType,
                bkInvoices = mutableListOf(bkInvoice),
                tenant = bkInvoice.tenant!!,
                partnerId = bkInvoice.partnerId!!,
                partnerDetailId = bkInvoice.partnerDetailId!!,
                client = if (noteType == NoteTypes.ICS_RETURN) InvoiceType.INTER_TRANSFER else InvoiceType.INTRA_TRANSFER,
                type = PartnerType.CUSTOMER,
                returnOrderId = invoiceId,
                invoiceAt = bkInvoice.createdOn
        )
        var debitNote = save(bkInvoice.createdBy ?: "", dn, itemList, false)
        createAutoCN(mutableListOf(debitNote), company.id, null, null, bkInvoice.tenant)
    }

    fun prepareS3URLForDebitNotes(returnOrderId: Long, tenant: String): PurchaseReturnPdfDto {
        var srAcceptDN = debitNoteRepo.getByReturnOrderId(mutableListOf(tenant), returnOrderId, PartnerType.CUSTOMER, NoteTypes.SR_ACCEPTED, ReturnReferenceType.RETURN_ORDER,InvoiceType.RIO)
        var srExpiredDN = debitNoteRepo.getByReturnOrderId(mutableListOf(tenant), returnOrderId, PartnerType.CUSTOMER, NoteTypes.SR_EXPIRED, ReturnReferenceType.RETURN_ORDER,InvoiceType.RIO)
        var purchaseReturnPdfDto = PurchaseReturnPdfDto(returnOrderId = returnOrderId, s3Urls = mutableListOf())
        if (srAcceptDN != null) {
            var srAcceptDNPdf = getDNPdf(srAcceptDN)
            if (!srAcceptDNPdf.isNullOrEmpty()) {
                purchaseReturnPdfDto.s3Urls.add(srAcceptDNPdf)
            }
        }
        if (srExpiredDN != null) {
            var srExpiredDNPdf = getDNPdf(srExpiredDN)
            if (!srExpiredDNPdf.isNullOrEmpty()) {
                purchaseReturnPdfDto.s3Urls.add(srExpiredDNPdf)
            }
        }
        return purchaseReturnPdfDto
    }

    fun prepareS3URLForDebitNotesByDnNumber(dnNumber: String, tenant: String): FileUrl {
        val dn = debitNoteRepo.getByDebitNoteNumber(tenant, dnNumber,NoteTypes.PURCHASE_RETURN)
                ?: throw RequestException("DN not found")
        var prDNPdf = getDNPdf(dn)
        if (!prDNPdf.isNullOrEmpty()) {
            return FileUrl(prDNPdf,"","")
        }
        throw RequestException("Failed to generate PDF")
    }

    fun getDNPdf(dn: DebitNote?): String? {
        if(dn == null){
            return null
        }
        return reportProxy.printDebitNote(getDnDetailsForPdf(dn))
    }

    fun getDnDetailsForPdf(dn: DebitNote): DebitNotePdfDto {
        // reusing the dto from SR CN pdf item part
        var saleReturnItems = mutableListOf<SaleReturnItemsDto>()
        var taxableAmt = BigDecimal.ZERO
        var totalTax = BigDecimal.ZERO
        var taxPer = BigDecimal.ZERO
        var allTaxPerc = mutableMapOf<String, BigDecimal>()
        allTaxPerc.put("5cgst", BigDecimal.ZERO)
        allTaxPerc.put("5sgst", BigDecimal.ZERO)
        allTaxPerc.put("5igst", BigDecimal.ZERO)
        allTaxPerc.put("12cgst", BigDecimal.ZERO)
        allTaxPerc.put("12sgst", BigDecimal.ZERO)
        allTaxPerc.put("12igst", BigDecimal.ZERO)
        allTaxPerc.put("18cgst", BigDecimal.ZERO)
        allTaxPerc.put("18sgst", BigDecimal.ZERO)
        allTaxPerc.put("18igst", BigDecimal.ZERO)
        allTaxPerc.put("28cgst", BigDecimal.ZERO)
        allTaxPerc.put("28sgst", BigDecimal.ZERO)
        allTaxPerc.put("28igst", BigDecimal.ZERO)
        allTaxPerc.put("cgstTotal", BigDecimal.ZERO)
        allTaxPerc.put("sgstTotal", BigDecimal.ZERO)
        allTaxPerc.put("igstTotal", BigDecimal.ZERO)
        allTaxPerc.put("5Taxable", BigDecimal.ZERO)
        allTaxPerc.put("12Taxable", BigDecimal.ZERO)
        allTaxPerc.put("18Taxable", BigDecimal.ZERO)
        allTaxPerc.put("28Taxable", BigDecimal.ZERO)
        allTaxPerc.put("0Taxable", BigDecimal.ZERO)
        var invoiceNum = if(dn.apiVersion == APIVersionType.V1) {
            if (dn.consolidatedStoreInvoiceInvoiceId == null) dn.debitItems[0].invoiceNumber else dn.consolidatedStoreInvoiceInvoiceId
        }else{
            ""
        }
        if(dn.apiVersion == APIVersionType.V2){
            var detailsObj = retailIoProxy.getRioDebitItemData(retailIoVersion,retailIoSource,retailIoKey,dn.logisticsPackageId?:"",true)
            invoiceNum = detailsObj.invoiceNumber
            val warehouse = supplierProxy.getPartnerGeneralData(listOf(dn.partnerDetailId!!)) ?: throw RequestException("Error in partner API for warehouse ${dn.partnerDetailId}")
            val darkstorePartnerDetailId = companyService.getCompanyTenantMappingObject(dn.tenant)?.partnerDetailId ?: throw RequestException("Error in partner API for darkstore ${dn.tenant}")
            val darkstore = supplierProxy.getPartnerGeneralData(listOf(darkstorePartnerDetailId)) ?: throw RequestException("Error in partner API for darkstore $darkstorePartnerDetailId")
            val isPRDNinterState: Boolean = gstUtil.getInterStateFlag(warehouse, darkstore)

            detailsObj.items.forEach { i ->
                val totalTaxPercent = (i.cgstPercent?:0.0) + (i.sgstPercent?:0.0)+(i.igstPercent?:0.0)
                var cgst = if (isPRDNinterState) BigDecimal.ZERO else BigDecimal(totalTaxPercent.toDouble()/2).setScale(2, RoundingMode.CEILING)
                var sgst = if (isPRDNinterState) BigDecimal.ZERO else BigDecimal(totalTaxPercent.toDouble()/2).setScale(2, RoundingMode.CEILING)
                var igst= if (isPRDNinterState) BigDecimal(totalTaxPercent.toDouble()).setScale(2, RoundingMode.CEILING) else BigDecimal.ZERO

                taxableAmt += i.cnTaxableAmount!!.toBigDecimal()
                totalTax += i.cnNetGstAmount!!.toBigDecimal()
                taxPer = ((i.cgstPercent?:0.0) + (i.sgstPercent?:0.0) + (i.igstPercent?:0.0)).toBigDecimal()

                var cgstTaxValue: BigDecimal
                var sgstTaxValue: BigDecimal
                var igstTaxValue: BigDecimal
                var taxableVal: BigDecimal
                var cntaxablval = i.cnTaxableAmount!!.toBigDecimal()

                if ((cgst + sgst).toInt() == 5) {
                    cgstTaxValue = allTaxPerc.get("5cgst")!!
                    sgstTaxValue = allTaxPerc.get("5sgst")!!
                    taxableVal = allTaxPerc.get("5Taxable")!!
                    allTaxPerc.put(
                        "5cgst",
                        cgstTaxValue + i.cnNetGstAmount!!.toBigDecimal().divide(BigDecimal(2)).setScale(2, RoundingMode.CEILING)
                    )
                    allTaxPerc.put(
                        "5sgst",
                        sgstTaxValue + i.cnNetGstAmount!!.toBigDecimal().divide(BigDecimal(2)).setScale(2, RoundingMode.CEILING)
                    )
                    allTaxPerc.put("5Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))

                } else if (igst.toInt() == 5) {
                    igstTaxValue = allTaxPerc.get("5igst")!!
                    taxableVal = allTaxPerc.get("5Taxable")!!
                    allTaxPerc.put("5igst", igstTaxValue + i.cnNetGstAmount!!.toBigDecimal())
                    allTaxPerc.put("5Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))
                } else if ((cgst + sgst).toInt() == 12) {
                    cgstTaxValue = allTaxPerc.get("12cgst")!!
                    sgstTaxValue = allTaxPerc.get("12sgst")!!
                    taxableVal = allTaxPerc.get("12Taxable")!!
                    allTaxPerc.put(
                        "12cgst",
                        cgstTaxValue + i.cnNetGstAmount!!.toBigDecimal().divide(BigDecimal(2)).setScale(2, RoundingMode.CEILING)
                    )
                    allTaxPerc.put(
                        "12sgst",
                        sgstTaxValue + i.cnNetGstAmount!!.toBigDecimal().divide(BigDecimal(2)).setScale(2, RoundingMode.CEILING)
                    )
                    allTaxPerc.put("12Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))

                } else if (igst.toInt() == 12) {
                    igstTaxValue = allTaxPerc.get("12igst")!!
                    taxableVal = allTaxPerc.get("12Taxable")!!
                    allTaxPerc.put("12igst", igstTaxValue + i.cnNetGstAmount!!.toBigDecimal())
                    allTaxPerc.put("12Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))
                } else if ((cgst + sgst).toInt() == 18) {
                    cgstTaxValue = allTaxPerc.get("18cgst")!!
                    sgstTaxValue = allTaxPerc.get("18sgst")!!
                    taxableVal = allTaxPerc.get("18Taxable")!!
                    allTaxPerc.put(
                        "18cgst",
                        cgstTaxValue + i.cnNetGstAmount!!.toBigDecimal().divide(BigDecimal(2)).setScale(2, RoundingMode.CEILING)
                    )
                    allTaxPerc.put(
                        "18sgst",
                        sgstTaxValue + i.cnNetGstAmount!!.toBigDecimal().divide(BigDecimal(2)).setScale(2, RoundingMode.CEILING)
                    )
                    allTaxPerc.put("18Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))

                } else if (igst.toInt() == 18) {
                    igstTaxValue = allTaxPerc.get("18igst")!!
                    taxableVal = allTaxPerc.get("18Taxable")!!
                    allTaxPerc.put("18igst", igstTaxValue + i.cnNetGstAmount!!.toBigDecimal())
                    allTaxPerc.put("18Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))
                } else if ((cgst + sgst).toInt() == 28) {
                    cgstTaxValue = allTaxPerc.get("28cgst")!!
                    sgstTaxValue = allTaxPerc.get("28sgst")!!
                    taxableVal = allTaxPerc.get("28Taxable")!!
                    allTaxPerc.put(
                        "28cgst",
                        cgstTaxValue + i.cnNetGstAmount!!.toBigDecimal().divide(BigDecimal(2)).setScale(2, RoundingMode.CEILING)
                    )
                    allTaxPerc.put(
                        "28sgst",
                        sgstTaxValue + i.cnNetGstAmount!!.toBigDecimal().divide(BigDecimal(2)).setScale(2, RoundingMode.CEILING)
                    )
                    allTaxPerc.put("28Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))

                } else if (igst.toInt() == 28) {
                    igstTaxValue = allTaxPerc.get("28igst")!!
                    taxableVal = allTaxPerc.get("28Taxable")!!
                    allTaxPerc.put("28igst", igstTaxValue + i.cnNetGstAmount!!.toBigDecimal())
                    allTaxPerc.put("28Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))
                } else {
                    taxableVal = allTaxPerc.get("0Taxable")!!
                    allTaxPerc.put("0Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))
                }
                saleReturnItems.add(
                    SaleReturnItemsDto(
                        id = "1",
                        createdOn = "${dn.createdOn}",
                        ucode = "${i.itemCode}",
                        batch = i.batchNumber,
                        amount = "${i.mrp}",
                        quantity = "${i.returnQuantity}",
                        epr = "${i.mrp}",
                        trueEpr = "${i.mrp}",
                        discountPercentage = "${i.discountPercentage}",
                        discountAmount = "${i.discountAmount}",
                        cgst = "${cgst}?:0.0}",
                        cgstAmt = if(cgst.toDouble() > 0.0) String.format("%.2f", (i.netGstAmount?:0.0)/2) else "0.0",
                        sgst = "${sgst?:0.0}",
                        sgstAmt = if(sgst.toDouble() > 0.0) String.format("%.2f", (i.netGstAmount?:0.0)/2) else "0.0",
                        igst = "${igst?:0.0}",
                        igstAmt = if(igst.toDouble() > 0.0) String.format("%.2f", i.netGstAmount) else "0.0",
                        hsn = i.hsnCode,
                        invoiceAmount = "${i.payableAmount}",
                        netGstAmt = "${i.netGstAmount}",
                        abtMrp = "${i.abettedMrp}",
                        name = i.itemName,
                        taxableAmount = "${i.taxableAmount}",
                        invoiceNumber = detailsObj.invoiceNumber,
                        returnQty = "${i.returnQuantity}",
                        conversion = "${i.conversionPercentage}",
                        cnTaxableValue = "${i.cnTaxableAmount}",
                        cnNetGST = "${i.cnNetGstAmount}",
                        cnBillValue = "${i.cnPayableAmount}",
                        expiry =i.expiryDate.toString() ,
                        invoiceDate = if(dn.invoiceAt != null) "${dn.invoiceAt!!.toLocalDate()}" else "-",
                        returnDiscount = "0.0",
                        baseBillingRate = (i.effectivePerItemCost?:0.0).toString()
                    )
                )
            }
        }else{
            dn.debitItems.forEach { i ->
                var cgstAmt = (i.cgst?.divide(100.toBigDecimal()))?.multiply(i.taxableAmount)
                var igstAmt = (i.igst?.divide(100.toBigDecimal()))?.multiply(i.taxableAmount)
                var sgstAmt = (i.sgst?.divide(100.toBigDecimal()))?.multiply(i.taxableAmount)
                var cgst = i.cgst ?: BigDecimal.ZERO
                var igst = i.igst ?: BigDecimal.ZERO
                var sgst = i.sgst ?: BigDecimal.ZERO

                taxableAmt += i.cnTaxableValue ?: BigDecimal.ZERO
                totalTax += i.cnNetGST ?: BigDecimal.ZERO
                taxPer = cgst + igst + sgst

                var cgstTaxValue: BigDecimal
                var sgstTaxValue: BigDecimal
                var igstTaxValue: BigDecimal
                var taxableVal: BigDecimal
                var cntaxablval = i.taxableAmount
                if ((cgst + sgst).intValueExact() == 5) {
                    cgstTaxValue = allTaxPerc.get("5cgst")!!
                    sgstTaxValue = allTaxPerc.get("5sgst")!!
                    taxableVal = allTaxPerc.get("5Taxable")!!
                    allTaxPerc.put("5cgst", cgstTaxValue + cgstAmt!!)
                    allTaxPerc.put("5sgst", sgstTaxValue + sgstAmt!!)
                    allTaxPerc.put("5Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))
                } else if (igst.intValueExact() == 5) {
                    igstTaxValue = allTaxPerc.get("5igst")!!
                    taxableVal = allTaxPerc.get("5Taxable")!!
                    allTaxPerc.put("5igst", igstTaxValue + igstAmt!!)
                    allTaxPerc.put("5Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))
                } else if ((cgst + sgst).intValueExact() == 12) {
                    cgstTaxValue = allTaxPerc.get("12cgst")!!
                    sgstTaxValue = allTaxPerc.get("12sgst")!!
                    taxableVal = allTaxPerc.get("12Taxable")!!
                    allTaxPerc.put("12cgst", cgstTaxValue + cgstAmt!!)
                    allTaxPerc.put("12sgst", sgstTaxValue + sgstAmt!!)
                    allTaxPerc.put("12Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))

                } else if (igst.intValueExact() == 12) {
                    igstTaxValue = allTaxPerc.get("12igst")!!
                    taxableVal = allTaxPerc.get("12Taxable")!!
                    allTaxPerc.put("12igst", igstTaxValue + igstAmt!!)
                    allTaxPerc.put("12Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))
                } else if ((cgst + sgst).intValueExact() == 18) {
                    cgstTaxValue = allTaxPerc.get("18cgst")!!
                    sgstTaxValue = allTaxPerc.get("18sgst")!!
                    taxableVal = allTaxPerc.get("18Taxable")!!
                    allTaxPerc.put("18cgst", cgstTaxValue + cgstAmt!!)
                    allTaxPerc.put("18sgst", sgstTaxValue + sgstAmt!!)
                    allTaxPerc.put("18Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))

                } else if (igst.intValueExact() == 18) {
                    igstTaxValue = allTaxPerc.get("18igst")!!
                    taxableVal = allTaxPerc.get("18Taxable")!!
                    allTaxPerc.put("18igst", igstTaxValue + igstAmt!!)
                    allTaxPerc.put("18Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))
                } else if ((cgst + sgst).intValueExact() == 28) {
                    cgstTaxValue = allTaxPerc.get("28cgst")!!
                    sgstTaxValue = allTaxPerc.get("28sgst")!!
                    taxableVal = allTaxPerc.get("28Taxable")!!
                    allTaxPerc.put("28cgst", cgstTaxValue + cgstAmt!!)
                    allTaxPerc.put("28sgst", sgstTaxValue + sgstAmt!!)
                    allTaxPerc.put("28Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))

                } else if (igst.intValueExact() == 28) {
                    igstTaxValue = allTaxPerc.get("28igst")!!
                    taxableVal = allTaxPerc.get("28Taxable")!!
                    allTaxPerc.put("28igst", igstTaxValue + igstAmt!!)
                    allTaxPerc.put("28Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))
                } else {
                    // check this if condition is required
                    if (dn.noteType == NoteTypes.SR_ACCEPTED || dn.noteType == NoteTypes.ICS_RETURN || dn.noteType == NoteTypes.ST_RETURN) {
                        taxableVal = allTaxPerc.get("0Taxable")!!
                        allTaxPerc.put("0Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))
                    }
                }
                saleReturnItems.add(
                    SaleReturnItemsDto(
                        id = "${i.id}",
                        createdOn = "${i.createdOn}",
                        ucode = "${i.ucode}",
                        batch = i.batch,
                        amount = "${i.amount}",
                        quantity = "${i.quantity}",
                        epr = "${i.epr}",
                        trueEpr = "${i.trueEpr}",
                        discountPercentage = if (dn.client.name == "RIO") "${i.dnTaxableRate}" else "${i.discountPercentage}",
                        discountAmount = "${i.discountAmount}",
                        cgst = "${i.cgst}",
                        cgstAmt = String.format("%.2f", cgstAmt),
                        sgst = "${i.sgst}",
                        sgstAmt = String.format("%.2f", sgstAmt),
                        igst = "${i.igst}",
                        igstAmt = String.format("%.2f", igstAmt),
                        hsn = i.hsn,
                        invoiceAmount = "${i.invoiceAmount}",
                        netGstAmt = "${i.netGstAmt}",
                        abtMrp = "${i.abtMrp}",
                        name = i.name,
                        taxableAmount = "${i.taxableAmount}",
                        invoiceNumber = i.invoiceNumber,
                        returnQty = "${i.returnQty}",
                        conversion = "${i.conversion}",
                        cnTaxableValue = "${i.taxableAmount}",
                        cnNetGST = "${i.cnNetGST}",
                        cnBillValue = "${i.cnBillValue}",
                        invoiceDate = dn.invoiceAt.toString(),
                        expiry = i.expiry.toString(),
                        baseBillingRate = (i.baseBillingRate ?: BigDecimal.ZERO).toString(),
                        returnDiscount = (i.returnDiscount ?: 0.0).toString()
                    )
                )
            }
        }

        var tenantMapping = companyService.getCompanyTenantMappingObject(dn.tenant)
            ?: throw RequestException("Source ${dn.tenant} info not found! ")

        var supplierList = supplierProxy.supplier(null, tenantMapping.partnerDetailId)
        var supplier = if (supplierList.isNotEmpty()) supplierList.get(0) else throw RequestException(
            "Could not get Supplier List for partnerId ${dn.partnerDetailId} for tenant " +
                    "${dn.tenant} ,while sale return credit note PDF creation."
        )
        var sourceSupplierList = supplierProxy.supplier(null, dn.partnerDetailId!!.toLong())
        var sourceSupplier = if (sourceSupplierList.isNotEmpty()) sourceSupplierList.get(0) else throw RequestException(
            "Could not get Source Supplier List for partnerId ${tenantMapping.partnerDetailId} for tenant " +
                    "${dn.tenant} ,while sale return credit note PDF creation."
        )

        var toStateId = sourceSupplier?.partnerDetailList!!.get(0)?.stateId


        var fromStateId = supplier?.partnerDetailList!!.get(0)?.stateId

        var allPartnerDetail = supplier?.partnerDetailList?.associateBy { it!!.id }

        var partnerDetail = allPartnerDetail?.get(tenantMapping.partnerDetailId!!.toLong())

        var totalCgst =
            allTaxPerc.get("5cgst")!! + allTaxPerc.get("12cgst")!! + allTaxPerc.get("18cgst")!! + allTaxPerc.get("28cgst")!!
        var totalSgst =
            allTaxPerc.get("5sgst")!! + allTaxPerc.get("12sgst")!! + allTaxPerc.get("18sgst")!! + allTaxPerc.get("28sgst")!!
        var totalIgst =
            allTaxPerc.get("5igst")!! + allTaxPerc.get("12igst")!! + allTaxPerc.get("18igst")!! + allTaxPerc.get("28igst")!!
        var totalTaxable =
            allTaxPerc.get("5Taxable")!! + allTaxPerc.get("12Taxable")!! + allTaxPerc.get("18Taxable")!! + allTaxPerc.get(
                "28Taxable"
            )!! + allTaxPerc.get("0Taxable")!!

        var headers = DebitNoteHeaderDto(
            debitNoteNumber = dn.debitNoteNumber,
            debitNoteDate = "${dn.createdOn?.toLocalDate()}",
            returnType = "PURCHASE RETURN",
            fromName = supplier!!.partnerName,
            fromAddress = (partnerDetail?.address1?:"") + "," + (partnerDetail?.address2?:""),
            fromGst = (partnerDetail?.gst?:""),
            fromState = (partnerDetail?.state?:""),
            toName = sourceSupplier.partnerName,
            toAddress = "${sourceSupplier!!.partnerDetailList?.get(0)?.address1?:""} \n ${sourceSupplier!!.partnerDetailList?.get(0)?.address2?:""}",
            toState = sourceSupplier!!.partnerDetailList?.get(0)?.state?:"",
            toGst = sourceSupplier!!.partnerDetailList?.get(0)?.gst?:"",
            fromStateId = "$fromStateId",
            toStateId = "$toStateId",
            colMrp = if (dn.noteType == NoteTypes.SR_ACCEPTED || dn.noteType == NoteTypes.ICS_RETURN || dn.noteType == NoteTypes.ST_RETURN) "DN Taxable Value" else "MRP",
            colMrpValue = if (dn.noteType == NoteTypes.SR_ACCEPTED || dn.noteType == NoteTypes.ICS_RETURN || dn.noteType == NoteTypes.ST_RETURN) "DN Tax Amount" else "MRP Value",
            taxPer = "${taxPer}",
            tax = "${totalTax}",
            dnValue = "${taxableAmt + totalTax}",
            taxableValue = "${taxableAmt}",
            invoiceNo = invoiceNum,
            invoiceDate = "${dn.invoiceAt?.toLocalDate()}",
            conversion = "${if(dn.apiVersion==APIVersionType.V1){dn.debitItems[0].conversion}else{"100"}}",
            eighteenPercCGSTTaxValue = "${allTaxPerc.get("18cgst")}",
            eighteenPercIGSTTaxValue = "${allTaxPerc.get("18igst")}",
            eighteenPercSGSTTaxValue = "${allTaxPerc.get("18sgst")}",
            eighteenPercTaxValue = "${allTaxPerc.get("18Taxable")}",
            fivePercCGSTTaxValue = "${allTaxPerc.get("5cgst")}",
            fivePercIGSTTaxValue = "${allTaxPerc.get("5igst")}",
            fivePercSGSTTaxValue = "${allTaxPerc.get("5sgst")}",
            fivePercTaxValue = "${allTaxPerc.get("5Taxable")}",
            totalCGSTTaxValue = "$totalCgst",
            totalIGSTTaxValue = "$totalIgst",
            totalSGSTTaxValue = "$totalSgst",
            totalTaxValue = "$totalTaxable",
            twelvePercCGSTTaxValue = "${allTaxPerc.get("12cgst")}",
            twelvePercIGSTTaxValue = "${allTaxPerc.get("12igst")}",
            twelvePercSGSTTaxValue = "${allTaxPerc.get("12sgst")}",
            twelvePercTaxValue = "${allTaxPerc.get("12Taxable")}",
            twentyEightPercCGSTTaxValue = "${allTaxPerc.get("28cgst")}",
            twentyEightPercIGSTTaxValue = "${allTaxPerc.get("28igst")}",
            twentyEightPercSGSTTaxValue = "${allTaxPerc.get("28sgst")}",
            twentyEightPercTaxValue = "${allTaxPerc.get("28Taxable")}",
            qr = null,
            irn = null,
            zeroPercTaxValue = "${allTaxPerc.get("0Taxable")}",
            creditNoteAmountInWords ="" ,
            cin = "",
            toDL = "",
            fssai = "",
            fromDL =""
        )
        return DebitNotePdfDto(headers, saleReturnItems, dn.client.name)
    }


    fun createDntoCnWithFSC() {

        var page = 0
        var hasNext: Boolean
        var tenantCompanyMap = mutableMapOf<String,Long>()
        var systemConfigPartnerCompanyMap = mutableMapOf<Pair<Long,Pair<Long,String>>,SystemConfig?>()
            do {
                var allOpenSaleReturnDn = debitNoteRepo.getAllDebitNotes(PageRequest.of(page,200))

                allOpenSaleReturnDn.content.forEach loop@{ debitNote ->
                    var companyId = tenantCompanyMap.get(debitNote.tenant)
                    if(companyId == null){
                        var cmt = companyService.getCompanyTenantMappingObject(debitNote.tenant)
                        tenantCompanyMap.put(debitNote.tenant,cmt?.companyId!!)
                        companyId = cmt?.companyId!!
                    }
                    var checkSystemConfig = systemConfigPartnerCompanyMap.get(Pair(companyId!!,Pair(debitNote.partnerId!!,debitNote.noteType.name)))
                    if(checkSystemConfig == null) {
                        var fscObj = systemConfigService.getPartnerConfig(debitNote.partnerId!!, debitNote.supplierName, companyId!!, debitNote.noteType,debitNote.tenant, debitNote.partnerDetailId!!)
                        systemConfigPartnerCompanyMap.put(Pair(companyId!!,Pair(debitNote.partnerId!!,debitNote.noteType.name)),fscObj)
                        checkSystemConfig = fscObj

                    }
                    if (checkSystemConfig == null || !checkSystemConfig.isAutoCN || debitNote.createdOn!!.toLocalDate().plusDays(checkSystemConfig.conversionDay.toLong()) > LocalDate.now()) {
                        return@loop
                    }

                    try {
                        debitNote.debitItems.forEach {
                            it.consumed = true
                            if (debitNote.noteType == NoteTypes.SR_EXPIRED) {
                                var mrp = it.amount * it.returnQty!!

                                var percentage = it.invoiceAmount?.divide(mrp.toBigDecimal(), 5, RoundingMode.CEILING)?.multiply(BigDecimal(100))?.toDouble()
                                        ?: 100.0
                                if(!checkSystemConfig.isDynamicPercentage){
                                    if(checkSystemConfig.conversionPercentage.toDouble() > percentage)
                                        it.conversion = percentage
                                    else
                                    it.conversion = checkSystemConfig.conversionPercentage.toDouble()
                                }else {
                                    it.conversion = percentage
                                }
                            } else {
                                it.conversion = checkSystemConfig.conversionPercentage.toDouble()
                            }
                            it.creditItemAmount = it.invoiceAmount
                        }
                        var tenant =  debitNote.tenant
                        var ds: String? = null
                        if(debitNote.tenant.substring(0,2) == "ds") {
                            ds = tenant
                        }
                        var creditNote = CreditNote(0, now(), now(), "SYSTEM", "", NoteStatus.PENDING, debitNote.noteType, debitNote.amountReceivable, debitNote.supplierId, debitNote.supplierName, debitNote.remarks, null, null, mutableListOf(debitNote), CreditNoteClosureType.SETTLEMENT, debitNote.partnerId, debitNote.partnerDetailId, tenant, null, debitNote.type, debitNote.client, null, null, null, null, null, 100.0, remainingAmount = debitNote.amountReceivable)
                        log.debug("creditnotes obj : ${ObjectMapper().writeValueAsString(creditNote)}")
                        creditNoteService.save("SYSTEM", creditNote, tenant, PartnerType.CUSTOMER, ds,true)
                    }catch (e:Exception){
                        log.debug(e.printStackTrace().toString())
                    }
                }
                hasNext = allOpenSaleReturnDn.hasNext()
                page++
            }while(hasNext)

        }


    fun createPrSaleInvoice(pr:PurchaseIssue,supplier:Supplier,tenant: String){

            val mercuryItems = mutableListOf<BkItem>()
            var totalAmt = 0.0
            pr.defects.forEach { item ->


                var abtMrp = (item.mrp?.toBigDecimal()?.divide((BigDecimal.ONE + ((item.igst?.plus(item.sgst?:0.0)?.plus(item.cgst?:0.0))?.toBigDecimal()
                        ?: BigDecimal.ZERO).divide(BigDecimal(100),3, RoundingMode.CEILING)),3, RoundingMode.CEILING))
                log.debug("abtMpr : ${abtMrp}")
                //amt - taxAmt
                var netGstAmt = item.igstAmount?.plus(item.sgstAmount?:0.0)?.plus(item.cgstAmount?:0.0)
                var taxableValue = netGstAmt?.let { item.amount?.minus(it) }
                log.debug("taxableValue : $taxableValue")

                val bkItem = BkItem(0, null, null, item.id!!, item.ucode!!, item.batch!!,item.mrp?:0.0,item.returnQuantity,BigDecimal(item.epr?:0.0),
                        BigDecimal(item.epr?:0.0),BigDecimal(item.discountPercentage?:0.0),BigDecimal(item.discountAmount?:0.0),item.cgst?.toBigDecimal(),item.sgst?.toBigDecimal(),item.igst?.toBigDecimal(),null,item.amount?.toBigDecimal()
                , item.igstAmount?.plus(item.sgstAmount?:0.0)?.plus(item.cgstAmount?:0.0)?.let { BigDecimal(it) },abtMrp,item.name,taxableValue?.toBigDecimal(),pr.debitNoteNumber,null,null,null,null,
                        null, null,(item.igst?.plus(item.sgst?:0.0)?.plus(item.cgst?:0.0))?.toBigDecimal(),
                        item.returnQuantity,null,null,item.epr?.toBigDecimal(),null,item.epr?.toBigDecimal(),null,
                        (item.amount?.div(item.returnQuantity))?.toBigDecimal(), SaleUnitPriceType.valueOf(item.discountOn))
                totalAmt += item.amount?:0.0

                mercuryItems.add(bkItem)
            }

           var bkInvoice = BkInvoice(0, now(), now(), "SYSTEM", "SYSTEM", pr.id.toString(),
                    pr.debitNoteNumber, pr.supplierId, supplier.partnerName, totalAmt, 0.0,null,
                    InvoiceStatus.PENDING, mercuryItems, null,"PR_SALES",supplier.partnerId,
                   supplier.partnerDetailList?.get(0)?.id, tenant, PartnerType.CUSTOMER, InvoiceType.PR_SALES)


        bkInvoice.items.forEach { bkItem ->
           log.debug("item: $bkItem")
            bkItem.bkInvoice = bkInvoice
        }

        invoiceService.saveBkInvoice("SYSTEM",bkInvoice,supplier.partnerId!!)


    }

    fun createDebitNoteForSlabTask(creditNote: CreditNote, companyCode: String,isSlabDn:Boolean) {
        val taskId =
            if(isSlabDn)
                creditNote.slabTaskDetail?.slabTask?.id
        else
                creditNote.flatTaskDetail?.flatDiscountTask?.id

        log.debug("Creating SD/CN debit note for partnerId: ${creditNote.partnerId} in taskId: $taskId")
        var debitNote = DebitNote(
            id = 0,
            createdOn = now(),
            updatedOn = now(),
            supplierId = creditNote.supplierId,
            supplierName = creditNote.supplierName,
            purchaseTransactionNumber = null,
            debitNoteNumber = documentMasterService.getDocumentNumber(creditNote.createdBy!!, companyCode, DocumentType.CX_DN_DISCOUNT),
            createdBy = creditNote.createdBy,
            updatedBy = creditNote.createdBy,
            amountReceivable = creditNote.amount,
            remarks = if(isSlabDn) "Created by slab task" else "Created by bulk discount task",
            creditNoteId = creditNote.id,
            creditNoteNumber = creditNote.creditNoteNumber,
            status = NoteStatus.RECORDED,
            noteType = when (creditNote.noteType) {
                NoteTypes.SLAB_DISCOUNT_CN -> NoteTypes.SLAB_DISCOUNT_DN
                NoteTypes.OFFLINE_PROMOTION_DISCOUNT_CN -> NoteTypes.OFFLINE_PROMOTION_DISCOUNT_DN
                NoteTypes.OTC_MARGIN_LEAKAGE_DISCOUNT_CN -> NoteTypes.OTC_MARGIN_LEAKAGE_DISCOUNT_DN
                NoteTypes.SALES_INCENTIVE_DISCOUNT_CN -> NoteTypes.SALES_INCENTIVE_DISCOUNT_DN
                NoteTypes.FINANCE_DISCOUNT_CN -> NoteTypes.FINANCE_DISCOUNT_DN
                else -> NoteTypes.PURCHASE_SLAB_DISCOUNT_DN
            },
            partnerId = creditNote.partnerId,
            partnerDetailId = creditNote.partnerDetailId,
            tenant = creditNote.tenant!!,
            type = PartnerType.CUSTOMER,
            client = creditNote.client
        )
        debitNote = debitNoteRepo.save(debitNote)

        creditNote.debitNotes = mutableListOf(debitNote)
        val updatedCreditNote = creditNoteRepo.save(creditNote)

        log.debug("SD/FD CN debit note created for partnerid: ${creditNote.partnerId} in taskId: $taskId")

        log.debug("Updating ledger for partnerId: ${creditNote.partnerId} in taskId: $taskId}")
        val ledgerDto = VendorLedgerDto(
            transactionDate = LocalDate.now(),
            vendorId = updatedCreditNote.partnerId!!,
            vendorName = updatedCreditNote.supplierName,
            ledgerEntryType = LedgerEntryType.CREDIT,
            documentType = DocumentType.CX_CN_DISCOUNT,
            documentNumber = updatedCreditNote.creditNoteNumber!!,
            referenceNumber = updatedCreditNote.debitNotes?.get(0)?.debitNoteNumber,
            externalReferenceNumber = null,
            particulars = if(isSlabDn) "Slab discount credit note" else "Discount CN ${creditNote.noteType.name} " ,
            debitAmount = BigDecimal.ZERO,
            creditAmount = updatedCreditNote.amount,
            tenant = updatedCreditNote.tenant!!,
            partnerDetailId = updatedCreditNote.partnerDetailId,
            partnerId = updatedCreditNote.partnerId,
            type = updatedCreditNote.type,
            client = updatedCreditNote.client
        )
        if(creditNote.client != InvoiceType.EASY_SOL) {
            partnerService.addVendorLedgerEntry(creditNote.createdBy!!, ledgerDto)
        }
        log.debug("Ledger updated for partnerId: ${creditNote.partnerId} in taskId: $taskId")
    }

    fun getAllFirms(): List<FirmDTO?>? {
        var firms = supplierProxy.getFirmTypes(true,null,null)
        return firms
    }

    fun createDebitNoteForAdvanceCN(creditNote: CreditNote, companyCode: String) {
        log.debug("Creating Advance debit note for partnerId: ${creditNote.partnerId}")
        var debitNote = DebitNote(
            id = 0,
            createdOn = now(),
            updatedOn = now(),
            supplierId = creditNote.supplierId,
            supplierName = creditNote.supplierName,
            purchaseTransactionNumber = null,
            debitNoteNumber = documentMasterService.getDocumentNumber(creditNote.createdBy!!, companyCode, DocumentType.DN_ADVANCE_PAYMENT),
            createdBy = creditNote.createdBy,
            updatedBy = creditNote.createdBy,
            amountReceivable = creditNote.amount,
            remarks = "Created by Advance Payment CN",
            creditNoteId = creditNote.id,
            creditNoteNumber = creditNote.creditNoteNumber,
            status = NoteStatus.RECORDED,
            noteType = NoteTypes.ADVANCE_PAYMENT,
            partnerId = creditNote.partnerId,
            partnerDetailId = creditNote.partnerDetailId,
            tenant = creditNote.tenant!!,
            type = PartnerType.CUSTOMER,
            client = creditNote.client
        )
        debitNote = debitNoteRepo.save(debitNote)

        creditNote.debitNotes = mutableListOf(debitNote)
        val updatedCreditNote = creditNoteRepo.save(creditNote)

        log.debug("Updating ledger for partnerId: ${creditNote.partnerId}")
        val ledgerDto = VendorLedgerDto(
            transactionDate = LocalDate.now(),
            vendorId = updatedCreditNote.partnerId!!,
            vendorName = updatedCreditNote.supplierName,
            ledgerEntryType = LedgerEntryType.CREDIT,
            documentType = DocumentType.CN_ADVANCE_PAYMENT,
            documentNumber = updatedCreditNote.creditNoteNumber!!,
            referenceNumber = updatedCreditNote.debitNotes?.get(0)?.debitNoteNumber,
            externalReferenceNumber = null,
            particulars = "Advance Payment credit note",
            debitAmount = BigDecimal.ZERO,
            creditAmount = updatedCreditNote.amount,
            tenant = updatedCreditNote.tenant!!,
            partnerDetailId = updatedCreditNote.partnerDetailId,
            partnerId = updatedCreditNote.partnerId,
            type = updatedCreditNote.type,
            client = updatedCreditNote.client
        )

        partnerService.addVendorLedgerEntry(creditNote.createdBy!!, ledgerDto)
        log.debug("Ledger updated for partnerId: ${creditNote.partnerId} in taskId")
    }

    @Transactional
    fun createDebitNoteForRio(rioDebitnoteEventDto: RioDebitNoteEventDto) {
        log.debug("Creating Rio debit note for partnerId: ${rioDebitnoteEventDto.retailerId}")

        val distributorPartnerMapping = supplierProxy.getDistributorPartnerMapping(
            rioDebitnoteEventDto.distributorId
        )
        val distributorDetails = DistributorTenantOrderTypeDTO(
            tenant = distributorPartnerMapping.tenant,
            locationSameAsWarehouse = distributorPartnerMapping.distributorPdi == distributorPartnerMapping.warehousePdi,
            fulfilmentType = if (
                distributorPartnerMapping.distributorPdi == distributorPartnerMapping.warehousePdi
            ) {
                OrderType.B2B
            } else {
                OrderType.B2B2B
            }
        )
        var distributorTenant = distributorDetails.tenant
        var orderType = distributorDetails.fulfilmentType
        var customerType = orderType==OrderType.B2B2B
        var dsTenant: CompanyTenantMapping? = null
        if(customerType){
            dsTenant = companyService.getTenantByPDI(rioDebitnoteEventDto.distributorId)
        }
        var tenant = companyService.getCompanyTenantMappingObject(dsTenant?.tenant?:distributorTenant)?.tenant
        var company=
            companyRepo.getCompanyByTenant(tenant?:"") ?: throw RequestException("Company mapping not found for ${tenant}")


        var supplier = supplierProxy.supplier(null, rioDebitnoteEventDto.retailerId)

        var partnerId = supplier[0]?.partnerId
        var partnerName = supplier[0]?.partnerName
        var returnEventType =
            if(rioDebitnoteEventDto.returnEventType == "FULL_CANCELLATION") {
                DebitNoteReturnEventType.FULL_CANCELLATION
            }
            else if (rioDebitnoteEventDto.returnEventType == "PARTIAL") {
                DebitNoteReturnEventType.PARTIAL_CANCELLATION
            } else{
                DebitNoteReturnEventType.OMS_CANCELLATION
            }

        var dn = debitNoteRepo.getByPackageId(mutableListOf(tenant), rioDebitnoteEventDto.logisticsPackageId!!, PartnerType.CUSTOMER, NoteTypes.SR_ACCEPTED,ReturnReferenceType.RETURN_RIO_ORDER)
        if (dn != null) {
            log.debug("Duplicate Customer rio DebitNote, ReturnOrderId : ${rioDebitnoteEventDto.logisticsPackageId!!}")
            throw RequestException("Duplicate DN")
        }
        log.debug("rioDebitnoteEventDto.roundOffAmount!! ${rioDebitnoteEventDto.roundOffAmount}")
        var debitNote = DebitNote(
            id = 0,
            createdOn = now(),
            updatedOn = now(),
            supplierId = partnerId!!,
            supplierName = partnerName!!,
            purchaseTransactionNumber = null,
            debitNoteNumber = documentMasterService.getDocumentNumber("SYSTEM", company.companyCode, DocumentType.DN_SR_ACCEPTED_B2B),
            createdBy = "SYSTEM",
            updatedBy = "SYSTEM",
            amountReceivable = rioDebitnoteEventDto.debitNoteAmount,
            remarks = "created by RIO events",
            creditNoteId = null,
            creditNoteNumber = null,
            status = NoteStatus.RECORDED,
            noteType = NoteTypes.SR_ACCEPTED,
            partnerId = partnerId,
            partnerDetailId = rioDebitnoteEventDto.retailerId,
            tenant = tenant!!,
            type = PartnerType.CUSTOMER,
            client = InvoiceType.RIO,
            distributorPdi = rioDebitnoteEventDto.distributorId,
            returnReferenceType = ReturnReferenceType.RETURN_RIO_ORDER,
            returnEventType = returnEventType,
            apiVersion = APIVersionType.V2,
            logisticsPackageId = rioDebitnoteEventDto.logisticsPackageId,
            invoiceAt = rioDebitnoteEventDto.invoiceDate?.atStartOfDay(),
            baseDNAmount = rioDebitnoteEventDto.baseCreditNoteAmount,
            roundOffAmount = rioDebitnoteEventDto.roundOffAmount,
            debitItems = mutableListOf()
        )
        debitNote = debitNoteRepo.save(debitNote)
        var dnPrObj = debitNotePrRepo.save(DebitNotePr(0, debitNote.createdOn, debitNote.updatedOn, debitNote.createdBy, debitNote.createdBy, debitNote.debitNoteNumber!!,
            "", "", debitNote.amountReceivable, tenant))
        var dnPrObjCopy = dnPrObj.copy()
        var creditNote = CreditNote(
            id = 0,
            createdOn = now(),
            updatedOn = now(),
            createdBy = "SYSTEM",
            updatedBy = "SYSTEM",
            status = NoteStatus.PENDING,
            noteType = NoteTypes.SR_ACCEPTED,
            amount = rioDebitnoteEventDto.creditNoteAmount,
            supplierId = partnerId,
            supplierName = partnerName,
            remarks = "Created via excess payment",
            settlementId = null,
            invoiceId = null,
            debitNotes = null,
            closureType = CreditNoteClosureType.SETTLEMENT,
            partnerId = partnerId,
            partnerDetailId = rioDebitnoteEventDto.retailerId,
            tenant = tenant,
            creditNoteNumber = documentMasterService.getDocumentNumber("SYSTEM", company?.companyCode!!, DocumentType.CN_SR_ACCEPTED_B2B),
            type = PartnerType.CUSTOMER,
            client = InvoiceType.RIO,
            receiptStatus = ReceiptStatus.RECEIVED,
            expectedDate = null,
            vendorCreditNoteDate = null,
            vendorCreditNoteAmount = null,
            vendorCreditNoteNumber = null,
            baseCNAmount = rioDebitnoteEventDto.baseCreditNoteAmount,
            roundOffAmount = rioDebitnoteEventDto.roundOffAmount
        )
        log.debug("Rio CN created for partner id: $partnerId for amount: ${rioDebitnoteEventDto.creditNoteAmount}")
        creditNote = creditNoteRepo.save(creditNote)

        debitNote.creditNoteId = creditNote.id
        debitNote.creditNoteNumber = creditNote.creditNoteNumber
        var bkInvoice = bkInvoiceRepo.getByInvoiceId(rioDebitnoteEventDto.orderId, rioDebitnoteEventDto.invoiceNumber,
            mutableListOf(tenant))

        debitNote = debitNoteRepo.save(debitNote)
        creditNote.debitNotes = mutableListOf(debitNote)
        log.debug("invoice found for rio return : ${bkInvoice?.invoiceNum}")

        creditNoteService.addVendorLedgerEntryForCreditNote("SYSTEM", creditNote,rioDebitnoteEventDto.invoiceNumber)

        if(bkInvoice != null) {
            debitNote.bkInvoices = mutableListOf(bkInvoice)
            createAutoSettlement(debitNote, creditNote, returnEventType, tenant, null, true)
            invoiceDebitNoteRepo.save(InvoiceDebitNote(InvoiceDebitNoteId(bkInvoice.id, debitNote.id)))
        }else{

            log.info("auto settlement skipped, since no invoice found in system.")
        }
        //PR creation for b3 orders one-roof flow
        if(orderType == OrderType.B2B2B){
            var debitNoteCopy = debitNote.copy()
            var tenantPartnerInfo = supplierProxy.supplierByTenant(distributorTenant)
            debitNoteCopy.id = 0
            debitNoteCopy.type = PartnerType.VENDOR
            debitNoteCopy.client = InvoiceType.VENDOR
            debitNoteCopy.noteType = NoteTypes.PURCHASE_RETURN
            debitNoteCopy.supplierId = tenantPartnerInfo.get(0)!!.partnerId!!
            debitNoteCopy.partnerId = tenantPartnerInfo.get(0)!!.partnerId
            debitNoteCopy.partnerDetailId = tenantPartnerInfo.get(0)!!.id
            debitNoteCopy.supplierName = tenantPartnerInfo.get(0)!!.name!!
            debitNoteCopy.debitNoteNumber = "DS-${debitNoteCopy.debitNoteNumber}"
            debitNoteCopy.returnDuringDelivery = false
            debitNoteCopy.debitItems = mutableListOf()
            debitNoteCopy.creditNoteId = null
            debitNoteCopy.creditNoteNumber = null
            debitNoteCopy.status = NoteStatus.PENDING
            debitNoteCopy.sourceNoteType = debitNote.noteType
            val savedPR = debitNoteRepo.save(debitNoteCopy)
            dnPrObjCopy.id = 0
            dnPrObjCopy.debitNoteNumber = debitNoteCopy.debitNoteNumber
            debitNotePrRepo.save(dnPrObjCopy)

            // push event to kafka for eway bill
            if(distributorDetails.locationSameAsWarehouse != null && distributorDetails.locationSameAsWarehouse != true){
                log.info("Generating ewaybill for debitnote createDebitNoteForRio ${savedPR.id}, ${savedPR.debitNoteNumber}")
                ewayBillSinkPusher.createGenericEwayBillProducer(
                    EWayBillGenericDto(savedPR.id, SourceType.DEBIT_NOTE)
                )
            }
        }
        //send credit note pfd event
        eventPublisherUtil.createVaultCnPdfEvent( CreditNotePdfEventDto(rioDebitnoteEventDto.logisticsPackageId,creditNote.creditNoteNumber?:"",creditNote.amount,"",creditNote.noteType.name,creditNote.tenant!!,creditNote.partnerDetailId!!,null))

    }

    fun getRioReturnItemData(packageId: String, includeItems: Boolean, tenant:String? = null): RioReturnItemsDto?{
        val dnData = debitNoteDetailRepo.getDebitNotesByPackageIdAndTenant(packageId,tenant)
        if(dnData.isNullOrEmpty()) {
            return retailIoProxy.getRioDebitItemData(retailIoVersion, retailIoSource, retailIoKey, packageId, includeItems)
        }else {
            val itemData = retailIoProxy.getRioDebitItemData(retailIoVersion,retailIoSource,retailIoKey,packageId,includeItems)
            itemData.invoiceNumber = dnData[0]?.invoiceNumber
            return itemData
        }
    }

    @Transactional
    fun createRioDebitNotes(rioReturnDebitNoteDto: RioReturnEventDto): MutableList<DebitNote> {

        var debitNotes: MutableList<DebitNote> = mutableListOf()

        var tenants: MutableList<String?> = mutableListOf()
        tenants = when(rioReturnDebitNoteDto.ds){
            false -> companyService.findTenants(rioReturnDebitNoteDto.tenant)
            else -> {
                val companyTenant = companyService.getTenantByPDI(
                    rioReturnDebitNoteDto.partnerDetailIdOfDarkStore
                        ?: throw RequestException("Null darkstore partner detail id")
                )
                companyService.findTenants(companyTenant.tenant)
            }
        }
        if(tenants.isNullOrEmpty()) return throw RequestException("No tenants found!")
        if(rioReturnDebitNoteDto.returnType == DebitNoteReturnEventType.PARTIAL){
            rioReturnDebitNoteDto.returnType = DebitNoteReturnEventType.PARTIAL_CANCELLATION
        }
        var apiVersion: APIVersionType = APIVersionType.V1
        var thea = if(rioReturnDebitNoteDto.ds)
            tenants[0]
        else
            rioReturnDebitNoteDto.tenant
        val type = if(rioReturnDebitNoteDto.noteType == NoteType.ACCEPTED) NoteTypes.SR_ACCEPTED else if(rioReturnDebitNoteDto.noteType == NoteType.DAMAGED) NoteTypes.SR_DAMAGED else NoteTypes.SR_EXPIRED
        var cancellationDn: DebitNote? = null
        var isCopyEvent: Boolean = false
        var isCopyEventInterstate: Boolean = false
        if(rioReturnDebitNoteDto.returnType == DebitNoteReturnEventType.OMS_CANCELLATION || rioReturnDebitNoteDto.returnType == DebitNoteReturnEventType.FULL_CANCELLATION || rioReturnDebitNoteDto.returnType == DebitNoteReturnEventType.PARTIAL_CANCELLATION){
            cancellationDn = debitNoteRepo.getCancelledDnByPackageId(rioReturnDebitNoteDto.packageId!!, PartnerType.CUSTOMER, type,rioReturnDebitNoteDto.returnType!!)?:throw RequestException("Package id ${rioReturnDebitNoteDto.packageId} not available for cancellation type ${rioReturnDebitNoteDto.returnType}")
        }

        var company=
            companyRepo.getCompanyByTenant(thea!!) ?: throw RequestException("Company mapping not found for ${thea}")


       if(rioReturnDebitNoteDto.eventType == EventType.NSR){

           var dn = createNsrDebitNoteV2(rioReturnDebitNoteDto,company,thea)
           createAutoCN(mutableListOf(dn),company.id,rioReturnDebitNoteDto.returnType, null, thea,company.enableRioAutoCn,rioReturnDebitNoteDto)

           return mutableListOf(dn)
       }


        if(cancellationDn == null) {
            var dn = debitNoteRepo.getByPackageId(
                tenants,
                rioReturnDebitNoteDto.packageId!!,
                PartnerType.CUSTOMER,
                type,
                rioReturnDebitNoteDto.returnReferenceType ?: ReturnReferenceType.RETURN_ORDER
            )
            if (dn != null) {
                log.debug("Duplicate Customer rio DebitNote, ReturnOrderId : ${rioReturnDebitNoteDto.id!!}")
                throw RequestException("Duplicate DN")
            }
        }else{
            isCopyEvent = true
            isCopyEventInterstate = rioReturnDebitNoteDto.interstate ?: false
        }

        val listOfDebitNoteIds: MutableList<Long> = mutableListOf()


        try {
                    var invoicePresentInDb = true

            var supplierList = supplierProxy.supplier(null,
                (rioReturnDebitNoteDto.partnerId?:0).toLong())


            var supplier = if (!supplierList.isNullOrEmpty()) supplierList.get(0) else throw RequestException("Could not get Supplier List for supplier ${rioReturnDebitNoteDto.partnerId} for tenant " +
                    "$thea")

            var itemList: MutableList<DebitNoteDetails> = mutableListOf()
            var amount = BigDecimal.ZERO

            var items = rioReturnDebitNoteDto.items
            var rioItems: RioReturnItemsDto? = if(isCopyEvent){
                getRioReturnItemData(rioReturnDebitNoteDto.packageId, true, thea)
            }else{
                null
            }
            if(!isCopyEvent){
                items.forEach {
                    var abtMrp = calculateAbtMrp(it)
                    itemList.add(
                        DebitNoteDetails(
                            null,
                            rioReturnDebitNoteDto.updatedOn?: now(),
                            rioReturnDebitNoteDto.updatedOn?: now(),
                            it.id?:0,
                            it.ucode?:"",
                            it.batchNumber?:"",
                            it.mrp?.toDouble()?:0.00,
                            it.returnQty?:0,
                            it.epr ?: BigDecimal.ZERO,
                            it.epr ?: BigDecimal.ZERO,
                            it.discountPercentage?: BigDecimal.ZERO,
                            it.discountAmount?: BigDecimal.ZERO,
                            it.cgst?: BigDecimal.ZERO,
                            it.sgst?: BigDecimal.ZERO,
                            it.igst?: BigDecimal.ZERO,
                            it.hsnCode,
                            it.dnNetValue?.plus(it.dnTaxableValue?: BigDecimal.ZERO),
                            it.dnNetValue?: BigDecimal.ZERO,
                            abtMrp,
                            it.name?:"",
                            it.dnTaxableValue?: BigDecimal.ZERO,
                            when(rioReturnDebitNoteDto.ds) {
                                true -> it.darkStoreInvoiceId
                                false -> it.invoiceNumber
                            },
                            it.returnQty?:0,
                            rioReturnDebitNoteDto.cnConversionPercentage?.toDouble()?:0.00,
                            it.cnTaxableValue?: BigDecimal.ZERO,
                            it.cnGST?: BigDecimal.ZERO,
                            it.cnBillValue?: BigDecimal.ZERO,
                            null,
                            it.expiryDate?: LocalDate.now(),
                            it.cnMrpValue?: BigDecimal.ZERO,
                            it.schemePercent?: BigDecimal.ZERO,
                            it.quantityFree?:0,
                            it.ptr?: BigDecimal.ZERO,
                            it.schemeType?:"",
                            it.dnGst?: BigDecimal.ZERO,
                            it.quantityOrdered?:0,
                            it.quantityScheme?: BigDecimal.ZERO,
                            it.totalInvoiceQty?.toInt()?:0,
                            it.invoiceTaxableValue?: BigDecimal.ZERO,
                            it.dnTaxableValue?: BigDecimal.ZERO,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            false,
                            it.cnTotalValue?: BigDecimal.ZERO,
                            it.ucodeSchemePercent?: BigDecimal.ZERO,
                            it.invoiceDate,
                            it.showGstSeparately?:false,
                            it.baseBillingRate?:0.00,
                            it.returnDiscount?:0.00
                        )
                    )

                }
            }else {
                rioItems?.items?.forEach {
                    var isCopyEventTaxValue: BigDecimal = (it.cgstPercent?.toBigDecimal()?: BigDecimal.ZERO) + (it.sgstPercent?.toBigDecimal()?: BigDecimal.ZERO) + (it.igstPercent?.toBigDecimal()?: BigDecimal.ZERO)
                    itemList.add(
                        DebitNoteDetails(
                            id = null,
                            now(),
                            now(),
                            0,
                            it.itemCode?:"",
                            it.batchNumber?:"",
                            it.mrp?.toDouble()?:0.00,
                            it.returnQuantity?:0,
                            it.effectivePerItemCost?.toBigDecimal()?:BigDecimal.ZERO,
                            it.effectivePerItemCost?.toBigDecimal()?: BigDecimal.ZERO,
                            it.discountPercentage?.toBigDecimal()?: BigDecimal.ZERO,
                            it.discountAmount?.toBigDecimal()?: BigDecimal.ZERO,
                            if (isCopyEventInterstate) BigDecimal.ZERO else BigDecimal(isCopyEventTaxValue.toDouble()/2).setScale(2, RoundingMode.CEILING),
                            if (isCopyEventInterstate) BigDecimal.ZERO else BigDecimal(isCopyEventTaxValue.toDouble()/2).setScale(2, RoundingMode.CEILING),
                            if (isCopyEventInterstate) BigDecimal(isCopyEventTaxValue.toDouble()).setScale(2, RoundingMode.CEILING) else BigDecimal.ZERO,
                            it.hsnCode,
                            it.taxableAmount?.toBigDecimal(),
                            it.cnNetGstAmount?.toBigDecimal()?: BigDecimal.ZERO,
                            it.abettedMrp?.toBigDecimal()?: BigDecimal.ZERO,
                            it.itemName?:"",
                            it.taxableAmount?.toBigDecimal()?: BigDecimal.ZERO,
                            when(rioReturnDebitNoteDto.ds) {
                                true -> rioReturnDebitNoteDto.items[0].darkStoreInvoiceId
                                false -> rioReturnDebitNoteDto.items[0].invoiceNumber
                            },
                            it.returnQuantity?:0,
                            it.conversionPercentage,
                            it.cnTaxableAmount?.toBigDecimal()?: BigDecimal.ZERO,
                            it.netGstAmount?.toBigDecimal()?: BigDecimal.ZERO,
                            it.cnPayableAmount?.toBigDecimal()?: BigDecimal.ZERO,
                            null,
                            it.expiryDate?: LocalDate.now(),
                            it.cnPayableAmount?.toBigDecimal()?: BigDecimal.ZERO,
                            BigDecimal.ZERO,
                            0,
                             BigDecimal.ZERO,
                            "",
                            BigDecimal.ZERO,
                            it.returnQuantity,
                            BigDecimal.ZERO,
                            it.returnQuantity?.toInt()?:0,
                            it.taxableAmount?.toBigDecimal()?: BigDecimal.ZERO,
                            it.cnTaxableAmount?.toBigDecimal()?: BigDecimal.ZERO,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            false,
                            it.mrp?.toBigDecimal()?: BigDecimal.ZERO,
                            BigDecimal.ZERO,
                            null,
                            false,
                            0.00,
                            it.discountAmount?:0.00

                        )
                    )
                }
            }

            val dn = DebitNote(
                id = 0,
                createdOn = now(),
                updatedOn = now(),
                supplierId = supplier!!.partnerId!!,
                supplierName = supplier.partnerName!!,
                purchaseTransactionNumber = rioReturnDebitNoteDto.transactionId,
                debitNoteNumber = documentMasterService.getDocumentNumber(rioReturnDebitNoteDto.createdByName?:"", company.companyCode,
                    if(type == NoteTypes.SR_ACCEPTED){
                            DocumentType.DN_SR_ACCEPTED_B2B
                    } else{

                            DocumentType.DN_SR_EXPIRED_B2B
                    }),
                createdBy = rioReturnDebitNoteDto.createdByName,
                updatedBy = rioReturnDebitNoteDto.createdByName,
                amountReceivable = if(isCopyEvent){
                    cancellationDn?.amountReceivable!!
                }else{
                    rioReturnDebitNoteDto.totalDnValue!!
                     },
                amountReceived = BigDecimal(0),
                remarks = null,
                creditNoteId = null,
                creditNoteNumber = null,
                status = NoteStatus.PENDING,
                noteType = type,
                bkInvoices = mutableListOf(),
                tenant = thea,
                partnerId = supplier.partnerId,
                partnerDetailId = rioReturnDebitNoteDto.partnerId!!.toLong(),
                client = InvoiceType.RIO,
                type = PartnerType.CUSTOMER,
                returnOrderId = rioReturnDebitNoteDto.id,
                consolidatedStoreInvoiceId = rioReturnDebitNoteDto.consolidatedStoreInvoiceId,
                consolidatedStoreInvoiceInvoiceId = rioReturnDebitNoteDto.consolidatedStoreInvoiceInvoiceId,
                returnDuringDelivery = rioReturnDebitNoteDto.returnType == DebitNoteReturnEventType.PARTIAL_CANCELLATION,
                returnEventType = rioReturnDebitNoteDto.returnType?: DebitNoteReturnEventType.REGULAR,
                returnReferenceType = rioReturnDebitNoteDto.returnReferenceType?: ReturnReferenceType.RETURN_ORDER,
                invoiceAt = if(isCopyEvent){cancellationDn?.invoiceAt}else{rioReturnDebitNoteDto.items?.get(0)?.invoiceDate?.atStartOfDay()},
                logisticsPackageId = rioReturnDebitNoteDto.packageId,
                baseDNAmount = if(isCopyEvent){cancellationDn?.baseDNAmount}else{rioReturnDebitNoteDto.baseCreditNoteAmount},
                roundOffAmount = if(isCopyEvent){cancellationDn?.roundOffAmount}else{rioReturnDebitNoteDto.roundOffAmount},
                apiVersion = apiVersion,
                isCopyDN = isCopyEvent
            )

            debitNotes.add(save(rioReturnDebitNoteDto.createdByName?:rioReturnDebitNoteDto.createdBy!!, dn, itemList,invoicePresentInDb, isCopyEvent))
            val dnPrObjInvoiceId = if(isCopyEvent){""}else{if(rioReturnDebitNoteDto.items.isNotEmpty()){rioReturnDebitNoteDto.items[0].invoiceId?:""}else{""}}
            val dnPrObjInvoiceNum = if(isCopyEvent){""}else{if(rioReturnDebitNoteDto.items.isNotEmpty()){rioReturnDebitNoteDto.items[0].invoiceNumber?:""}else{""}}
            var dnPrObj = debitNotePrRepo.save(DebitNotePr(0, rioReturnDebitNoteDto.createdOn, rioReturnDebitNoteDto.updatedOn, rioReturnDebitNoteDto.createdBy, rioReturnDebitNoteDto.createdBy, dn.debitNoteNumber!!,
                dnPrObjInvoiceId, dnPrObjInvoiceNum, amount, thea))

            var dnPrObjCopy = dnPrObj.copy()
            log.info("copied dn")
            var debitNoteBool = true
            if(rioReturnDebitNoteDto.ds) {
//                var bkInvoice = bkInvoiceRepo.getByInvoiceIdForDS("DS-${rioReturnDebitNoteDto.items[0].invoiceId}",thea!!)
                log.info("creating thea dn")
                debitNotes.forEach { dnObj ->
                    var copyDnObj = dnObj.copy()

                    var copyItem = dnObj.debitItems.map { it.copy() }
                    copyDnObj.id = 0
                    var tenantPartnerInfo = supplierProxy.supplierByTenant(rioReturnDebitNoteDto.tenant)
                    if (tenantPartnerInfo.isNullOrEmpty()) throw RequestException("Partner info not found for Tenant ${rioReturnDebitNoteDto.tenant} ")
                    val warehouse = supplierProxy.getPartnerGeneralData(listOf(tenantPartnerInfo.get(0)!!.id!!)) ?: throw RequestException("Error in partner API for warehouse ${tenantPartnerInfo.get(0)?.id}")
                    val darkstorePartnerDetailId = companyService.getCompanyTenantMappingObject(copyDnObj.tenant)?.partnerDetailId ?: throw RequestException("Error in partner API for darkstore ${copyDnObj.tenant}")
                    val darkstore = supplierProxy.getPartnerGeneralData(listOf(darkstorePartnerDetailId)) ?: throw RequestException("Error in partner API for darkstore $darkstorePartnerDetailId")
                    val isPRDNinterState: Boolean = gstUtil.getInterStateFlag(warehouse, darkstore)
                    copyDnObj.type = PartnerType.VENDOR
                    copyDnObj.client = InvoiceType.VENDOR
                    copyDnObj.noteType = NoteTypes.PURCHASE_RETURN
                    copyDnObj.supplierId = tenantPartnerInfo.get(0)!!.partnerId!!
                    copyDnObj.partnerId = tenantPartnerInfo.get(0)!!.partnerId
                    copyDnObj.partnerDetailId = tenantPartnerInfo.get(0)!!.id
                    copyDnObj.supplierName = tenantPartnerInfo.get(0)!!.name!!
                    copyDnObj.debitNoteNumber = "DS-${copyDnObj.debitNoteNumber}"
                    copyDnObj.returnDuringDelivery = false // reverting the flag on copy DN
                    copyDnObj.sourceNoteType = dnObj.noteType
                    var dn =  debitNoteRepo.save(copyDnObj)

                    listOfDebitNoteIds.add(dn.id)

                    if(!copyItem.isNullOrEmpty()){

                        copyItem.forEach {
                            val totalTax = (it.cgst ?: BigDecimal.ZERO) + (it.sgst ?: BigDecimal.ZERO)+(it.igst ?: BigDecimal.ZERO)
                            it.cgst = if (isPRDNinterState) BigDecimal.ZERO else BigDecimal(totalTax.toDouble()/2).setScale(2, RoundingMode.CEILING)
                            it.sgst = if (isPRDNinterState) BigDecimal.ZERO else BigDecimal(totalTax.toDouble()/2).setScale(2, RoundingMode.CEILING)
                            it.igst = if (isPRDNinterState) BigDecimal(totalTax.toDouble()).setScale(2, RoundingMode.CEILING) else BigDecimal.ZERO
                            it.id = 0
                            it.debitnoteId = dn.id
                            it.debitNoteNumber = dn.debitNoteNumber

//                            if(bkInvoice!=null) it.invoiceNumber = bkInvoice.invoiceNum
                        }
                        dn.debitItems =  debitNoteDetailRepo.saveAll(copyItem)
                    }

                    dnPrObjCopy.id = 0
                    dnPrObjCopy.debitNoteNumber = "${copyDnObj.debitNoteNumber}"
                    debitNotePrRepo.save(dnPrObjCopy)

                }
            }

        } catch (e: Throwable) {
            log.error("Unable to create Debit note for purchase return ${rioReturnDebitNoteDto}", e)
            throw e
        }

        createAutoCN(debitNotes,company.id,rioReturnDebitNoteDto.returnType, null, thea,company.enableRioAutoCn,rioReturnDebitNoteDto, isCopyEvent)

        // push event to kafka for eway bill
        val distributorPdi = companyService.getCompanyTenantMappingObject(debitNotes.first().tenant)?.partnerDetailId?:throw RequestException("Distributor not mapped with tenant ${debitNotes.first().tenant}")
        val distributorDetails = invoiceService.getTenantCodeAndCustomerType(distributorPdi)
        if (distributorDetails.locationSameAsWarehouse != null && distributorDetails.locationSameAsWarehouse != true) {
            listOfDebitNoteIds.forEach {
                log.info("Generating ewaybill for debitnote createRioDebitNotes ${debitNotes.first().id}, ${debitNotes.first().debitNoteNumber}")
                ewayBillSinkPusher.createGenericEwayBillProducer(
                    EWayBillGenericDto(it, SourceType.DEBIT_NOTE)
                )
            }
        }

        return debitNotes
    }

    fun createNsrDebitNoteV2(rioReturnDebitNoteDto:RioReturnEventDto,company:Company,tenant:String): DebitNote {
        log.info("Inside create NSR debit note $rioReturnDebitNoteDto")

        var type = if(rioReturnDebitNoteDto.noteType == NoteType.ACCEPTED) NoteTypes.NSR_ACCEPTED else if(rioReturnDebitNoteDto.noteType == NoteType.EXPIRED) NoteTypes.NSR_EXPIRED else NoteTypes.NSR_DAMAGED

        var dn = debitNoteRepo.getByPackageId(mutableListOf(tenant), rioReturnDebitNoteDto.packageId!!, PartnerType.CUSTOMER, type,rioReturnDebitNoteDto.returnReferenceType?:ReturnReferenceType.RETURN_ORDER)
        if (dn != null) {
            log.info("Duplicate Customer rio DebitNote, ReturnOrderId : ${rioReturnDebitNoteDto.id!!}")
            throw RequestException("Duplicate DN")
        }

        val debitNoteNumber = documentMasterService.getDocumentNumber(rioReturnDebitNoteDto.createdBy?:"", company.companyCode, DocumentType.DEBIT_NOTE_NSR)

        val items = mutableListOf<DebitNoteDetails>()
        rioReturnDebitNoteDto.items.forEach {
            var abtMrp = calculateAbtMrp(it)

            val debitNoteDetails =  DebitNoteDetails(
                null,
                rioReturnDebitNoteDto.updatedOn,
                rioReturnDebitNoteDto.updatedOn,
                it.id!!,
                it.ucode!!,
                it.batchNumber!!,
                it.mrp!!.toDouble(),
                it.returnQty!!,
                BigDecimal.ZERO,
                BigDecimal.ZERO,
                it.discountPercentage,
                it.discountAmount,
                it.cgst,
                it.sgst,
                it.igst,
                it.hsnCode,
                it.dnNetValue?.plus(it.dnTaxableValue!!),
                it.dnNetValue,
                abtMrp,
                it.name,
                it.dnTaxableValue,
                it.invoiceNumber,
                it.returnQty,
                rioReturnDebitNoteDto.cnConversionPercentage?.toDouble(),
                it.cnTaxableValue,
                it.cnGST,
                it.cnBillValue,
                null,
                it.expiryDate,
                it.cnMrpValue,
                BigDecimal.ZERO,
                0,
                BigDecimal.ZERO,
                "",
                it.dnGst,
                0,
                BigDecimal.ZERO,
                it.totalInvoiceQty?.toInt(),
                it.invoiceTaxableValue,
                it.dnTaxableValue,
                null,
                null,
                null,
                null,
                null,
                null,
                false,
                it.cnTotalValue,
                BigDecimal.ZERO,
                it.invoiceDate,
                it.showGstSeparately,
                it.baseBillingRate,
                it.returnDiscount
            )

            items.add(debitNoteDetails)
        }
        var supplierList = supplierProxy.supplier(null,
            (rioReturnDebitNoteDto.partnerId?:0).toLong())

        var supplier = if (!supplierList.isNullOrEmpty()) supplierList.get(0) else throw RequestException("Could not get Supplier List for supplier ${rioReturnDebitNoteDto.partnerId} for tenant " +
                "$tenant")
        val debitNote = DebitNote(
            id = 0,
            createdOn = now(),
            updatedOn = now(),
            createdBy = rioReturnDebitNoteDto.createdBy,
            updatedBy = rioReturnDebitNoteDto.createdBy,
            supplierId = supplier?.partnerId!!,
            supplierName = supplier?.partnerName?:"",
            amountReceivable = rioReturnDebitNoteDto.totalDnValue?: BigDecimal.ZERO,
            status = NoteStatus.PENDING,
            noteType = type,
            debitItems = items,
            partnerId = supplier?.partnerId,
            partnerDetailId = rioReturnDebitNoteDto.partnerId!!.toLong(),
            tenant = tenant,
            type = PartnerType.CUSTOMER,
            client = InvoiceType.RIO,
            debitNoteNumber = debitNoteNumber,
            creditNoteId = null,
            creditNoteNumber = null,
            purchaseTransactionNumber = null,
            remarks = null,
            bkInvoices = mutableListOf(),
            apiVersion = APIVersionType.V1,
            logisticsPackageId = rioReturnDebitNoteDto.packageId,
            returnEventType = DebitNoteReturnEventType.REGULAR,
            returnReferenceType = rioReturnDebitNoteDto.returnReferenceType,
            roundOffAmount = rioReturnDebitNoteDto.roundOffAmount,
            baseDNAmount = rioReturnDebitNoteDto.baseCreditNoteAmount
        )


        val savedDebitNote = save(rioReturnDebitNoteDto.createdBy?:"", debitNote, items)
        if(rioReturnDebitNoteDto.ds) {

            val dnPrObjInvoiceId = if(rioReturnDebitNoteDto.items.isNotEmpty()){rioReturnDebitNoteDto.items[0].invoiceId?:""}else{""}
            val dnPrObjInvoiceNum = if(rioReturnDebitNoteDto.items.isNotEmpty()){rioReturnDebitNoteDto.items[0].invoiceNumber?:""}else{""}
            val dnPrObj = debitNotePrRepo.save(DebitNotePr(0, rioReturnDebitNoteDto.createdOn, rioReturnDebitNoteDto.updatedOn, rioReturnDebitNoteDto.createdBy, rioReturnDebitNoteDto.createdBy, debitNote.debitNoteNumber!!,
                dnPrObjInvoiceId, dnPrObjInvoiceNum, rioReturnDebitNoteDto.totalDnValue?: BigDecimal.ZERO, tenant))
            val dnPrObjCopy = dnPrObj.copy()

            val copyDnObj = savedDebitNote.copy()
            val copyItem = savedDebitNote.debitItems.map { it.copy() }
            copyDnObj.id = 0
            val tenantPartnerInfo = supplierProxy.supplierByTenant(rioReturnDebitNoteDto.tenant)

            val warehouse = supplierProxy.getPartnerGeneralData(listOf(tenantPartnerInfo.get(0)!!.id!!)) ?: throw RequestException("Error in partner API for warehouse ${tenantPartnerInfo.get(0)?.id}")
            val darkstorePartnerDetailId = companyService.getCompanyTenantMappingObject(copyDnObj.tenant)?.partnerDetailId ?: throw RequestException("Error in partner API for darkstore ${copyDnObj.tenant}")
            val darkstore = supplierProxy.getPartnerGeneralData(listOf(darkstorePartnerDetailId)) ?: throw RequestException("Error in partner API for darkstore $darkstorePartnerDetailId")
            val isPRDNinterState: Boolean = gstUtil.getInterStateFlag(warehouse, darkstore)

            if (tenantPartnerInfo.isEmpty()) throw RequestException("Partner info not found for Tenant ${rioReturnDebitNoteDto.tenant} ")
            copyDnObj.type = PartnerType.VENDOR
            copyDnObj.client = InvoiceType.VENDOR
            copyDnObj.noteType = NoteTypes.PURCHASE_RETURN
            copyDnObj.supplierId = tenantPartnerInfo.get(0)!!.partnerId!!
            copyDnObj.partnerId = tenantPartnerInfo.get(0)!!.partnerId
            copyDnObj.partnerDetailId = tenantPartnerInfo.get(0)!!.id
            copyDnObj.supplierName = tenantPartnerInfo.get(0)!!.name!!
            copyDnObj.debitNoteNumber = "DS-${copyDnObj.debitNoteNumber}"
            copyDnObj.returnDuringDelivery = false // reverting the flag on copy DN
            copyDnObj.sourceNoteType = savedDebitNote.noteType
            val dns =  debitNoteRepo.save(copyDnObj)

            if(copyItem.isNotEmpty()){
                copyItem.forEach {
                    it.id = 0
                    val totalTax = (it.cgst ?: BigDecimal.ZERO) + (it.sgst ?: BigDecimal.ZERO)+(it.igst ?: BigDecimal.ZERO)
                    it.cgst = if (isPRDNinterState) BigDecimal.ZERO else BigDecimal(totalTax.toDouble()/2).setScale(2, RoundingMode.CEILING)
                    it.sgst = if (isPRDNinterState) BigDecimal.ZERO else BigDecimal(totalTax.toDouble()/2).setScale(2, RoundingMode.CEILING)
                    it.igst = if (isPRDNinterState) BigDecimal(totalTax.toDouble()).setScale(2, RoundingMode.CEILING) else BigDecimal.ZERO

                    it.debitnoteId = dns.id
                    it.debitNoteNumber = dns.debitNoteNumber
                }
                dns.debitItems =  debitNoteDetailRepo.saveAll(copyItem)
            }
            dnPrObjCopy.id = 0
            dnPrObjCopy.debitNoteNumber = "${copyDnObj.debitNoteNumber}"
            debitNotePrRepo.save(dnPrObjCopy)

            // push event to kafka for eway bill
            var distributorPdi = companyService.getCompanyTenantMappingObject(dns.tenant)?.partnerDetailId?:throw RequestException("Distributor not mapped with tenant ${dns.tenant}")
            val distributorDetails = invoiceService.getTenantCodeAndCustomerType(distributorPdi)
            if(distributorDetails.locationSameAsWarehouse != null && distributorDetails.locationSameAsWarehouse != true){
                log.info("Generating ewaybill for debitnote createNsrDebitNoteV2 ${dns.id}, ${dns.debitNoteNumber}")
                ewayBillSinkPusher.createGenericEwayBillProducer(
                    EWayBillGenericDto(dns.id, SourceType.DEBIT_NOTE)
                )
            }
        }
        return savedDebitNote
    }

    private fun calculateAbtMrp(it: RioItemReturnEventDto): BigDecimal? =
        BigDecimal(it.mrp!!.toDouble() / (1 + ((it.cgst ?: BigDecimal.ZERO) + (it.sgst ?: BigDecimal.ZERO) + (it.igst ?: BigDecimal.ZERO)).toDouble() / 100)).setScale(
            2,
            BigDecimal.ROUND_HALF_UP
        )
}
