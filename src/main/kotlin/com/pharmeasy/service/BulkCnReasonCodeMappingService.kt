package com.pharmeasy.service

import com.pharmeasy.data.BulkCNReasonCodeMapping
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.BulkCnReasonCodeDto
import com.pharmeasy.model.PaginationDto
import com.pharmeasy.repo.BulkCNReasonCodeMappingRepo
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class BulkCnReasonCodeMappingService (private val bulkCNReasonCodeMappingRepo: BulkCNReasonCodeMappingRepo){

    fun getBulkCNReasonCodeMapping(reasonCode: Long): BulkCNReasonCodeMapping {
        return bulkCNReasonCodeMappingRepo.findByReasonCode(reasonCode) ?: throw RequestException("No mapping found for reason code $reasonCode")
    }

    @Transactional
    fun createBulkCNReasonCodeMapping(bulkCnReasonCodeDto: BulkCnReasonCodeDto, user: String) {
        val existingMapping = bulkCNReasonCodeMappingRepo.findByReasonCodeOrReasonCategory(bulkCnReasonCodeDto.reasonCode, bulkCnReasonCodeDto.bulkCnCategory)
        if(existingMapping != null){
            throw RequestException("Mapping already exists for reason code ${bulkCnReasonCodeDto.reasonCode}")
        }
        val bulkCNReasonCodeMapping = BulkCNReasonCodeMapping(
            bulkCnReasonCodeDto.reasonCode,
            bulkCnReasonCodeDto.bulkCnCategory,
            bulkCnReasonCodeDto.bulkCnCategoryDescription
        )
        bulkCNReasonCodeMappingRepo.save(bulkCNReasonCodeMapping)
    }

    fun getAllBulkCNReasonCodeMapping(page: Int?, size: Int?): PaginationDto {
        val pagination = PageRequest.of(page ?: 0, size?:10)
        val res = bulkCNReasonCodeMappingRepo.getBulkCNReasonCodeMapping(pagination)
        return PaginationDto(res.totalElements, res.totalPages, res.hasPrevious(), res.hasNext(), res)
    }
}