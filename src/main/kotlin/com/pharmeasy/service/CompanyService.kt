package com.pharmeasy.service

import com.pharmeasy.data.*
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.CompanyObjectDto
import com.pharmeasy.repo.*
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class CompanyService {
    companion object {
        private val log = LoggerFactory.getLogger(CheckerService::class.java)
    }

    @Autowired
    private lateinit var companyTenantMappingRepo: CompanyTenantMappingRepo

    @Autowired
    private lateinit var companyDarkStoreRefRepo: CompanyDarkStoreRefRepo

    @Autowired
    private lateinit var companyRepo: CompanyRepo


    fun findTenants(tenant: String,ds:Boolean=false): MutableList<String?> {
           return companyTenantMappingRepo.getAllTenantByTenant(tenant)
    }

    @Transactional
    fun getCompanyTenantMappingObject(tenant: String,ds: Boolean = false):CompanyTenantMapping?{
        return companyTenantMappingRepo.getByTenant(tenant)
    }

    @Transactional
    fun getTenantForDsTenant(tenant: String):CompanyTenantMapping?{

        var companyCode: String? = companyTenantMappingRepo.getCompanyCodeByTenant(tenant) ?: throw RequestException("Dark Store company not mapped to $tenant !")

        var mappingCompanyCode = companyDarkStoreRefRepo.getCompanyListForDs(companyCode!!)
        return mappingCompanyCode[0]?.let { companyTenantMappingRepo.getTheaTenantObjByCompanyCode(it) }

    }

    @Transactional
    fun getTenantByPDI(partnerDetailId: Long): CompanyTenantMapping {
        return companyTenantMappingRepo.getTenantByPDI(partnerDetailId)?:(throw RequestException("PDI:$partnerDetailId not mapped to company"))
    }

    @Transactional
    fun enableOrDisableAutomail(tenant: List<String>, status: Boolean, userName: String){
        var company =   companyRepo.getCompanyByTenants(tenant);
        company.forEach{
            it.isAutomail = status
            it.updatedBy = userName
        }
        companyRepo.saveAll(company)
    }

    @Transactional
    fun getCompanyAccountDetailsByTenant(tenant: String, ds:Boolean=false): Company {
        val companyTenantObject = getCompanyTenantMappingObject(tenant, ds)
        val companyId = companyTenantObject?.companyId ?:(throw RequestException("Tenant not mapped to company"))
        return companyRepo.getOne(companyId)
    }

    fun getCompanyByTenants(tenant: List<String>): List<CompanyTenantMapping>{
        return companyTenantMappingRepo.getCompanyTenantMappingByTenants(tenant)
    }

    fun getEnabledRetailerDnByTenant(tenant: String, ds:Boolean=false): Boolean{
        val companyTenantObject = getCompanyTenantMappingObject(tenant, ds)
        val companyId = companyTenantObject?.companyId ?:(throw RequestException("Tenant not mapped to company"))
        val company = companyRepo.getOne(companyId)
        return company.isRetailerDnEnabled?:false
    }

    fun getCompanyObjectByTenant(tenant: String): CompanyObjectDto {
        return companyRepo.getCompanyObjectByTenant(tenant)?:throw IllegalArgumentException("Tenant $tenant not found")
    }

    fun getCompanyCodeByTenant(tenant: String): String? {
        return companyTenantMappingRepo.getCompanyCodeByTenant(tenant)
    }

    fun getCompanyByTenant(tenant: String): Company? {
        return companyRepo.getCompanyByTenant(tenant)
    }
}
