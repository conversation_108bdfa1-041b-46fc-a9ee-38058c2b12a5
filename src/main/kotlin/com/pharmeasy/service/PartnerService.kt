package com.pharmeasy.service

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.pharmeasy.data.*
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.*
import com.pharmeasy.proxy.SupplierProxy
import com.pharmeasy.repo.*
import com.pharmeasy.repo.read.VendorLedgerReadRepo
import com.pharmeasy.resource.LedgerResource
import com.pharmeasy.specification.LedgerSpecification
import com.pharmeasy.stream.PaperServicePusher
import com.pharmeasy.type.*
import com.pharmeasy.util.DateUtils
import com.pharmeasy.util.DateUtils.getISTDateTime
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVPrinter
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.*
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.io.ByteArrayOutputStream
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@Service
class PartnerService(@Value("\${app.vault.template.ledger_template}") val ledgerTemplate: String,
                     @Value("\${app.outstanding_round_off_value}") val roundOffLimit: BigDecimal) {

    companion object {
        private val log = LoggerFactory.getLogger(PartnerService::class.java)
    }

    @Autowired
    private lateinit var vendorLedgerRepo: VendorLedgerRepo

    @Autowired
    private lateinit var vendorLedgerReadRepo: VendorLedgerReadRepo

    @Autowired
    private lateinit var partnerRepo: PartnerRepo

    @Autowired
    private lateinit var s3FileUtilityService: S3FileUtilityService

    @Autowired
    private lateinit var vendorFileService: VendorFileService

    @Autowired
    private lateinit var vendorDataLinkRepo: VendorDataLinksRepo

    @Autowired
    private lateinit var bkInvoiceRepo: BkInvoiceRepo

    @Autowired
    private lateinit var vendorRepo: VendorRepo

    @Autowired
    private lateinit var supplierProxy : SupplierProxy

    @Autowired
    private lateinit var ledgerResource: LedgerResource

    @Autowired
    private lateinit var companyTenantMappingRepo: CompanyTenantMappingRepo

    @Autowired
    private lateinit var companyService: CompanyService

    @Autowired
    private lateinit var redisUtilityService: RedisUtilityService

    @Autowired
    private lateinit var notificationService: VaultEmailNotificationService

    @Autowired
    private lateinit var receiptService: ReceiptService

    @Autowired
    private lateinit var invoiceService: InvoiceService

    @Autowired
    private lateinit var notificationMailService: NotificationMailService

    @Autowired
    private lateinit var tradeCreditPaymentService: TradeCreditPaymentService

    @Autowired
    private lateinit var paperServicePusher: PaperServicePusher

    @Autowired
    private lateinit var retailerDebitNoteService: RetailerDebitNoteService

    @Autowired
    private lateinit var retailerDebitNoteSettlementMappingService: RetailerDebitNoteSettlementMappingService

    @Autowired
    private lateinit var compressionUtil: CompressionUtil

    @Autowired
    private lateinit var documentMasterService: DocumentMasterService

    @Autowired
    private lateinit var adjustmentService: AdjustmentService

    @Autowired
    private lateinit var settlementService: SettlementService

    @Transactional
    fun addVendorLedgerEntry(user: String, vendorLedgerDto: VendorLedgerDto): VendorLedger {
        log.info("Inside addVendorLedgerEntry: $vendorLedgerDto")

        var companyMappingObj = companyTenantMappingRepo.getByTenant(vendorLedgerDto.tenant)

        if(companyMappingObj == null) throw RequestException("no tenant mapping found for ${vendorLedgerDto.tenant} ")
        var vendor = partnerRepo.getVendorBalanceWithLock(vendorLedgerDto.partnerId?:vendorLedgerDto.vendorId,companyMappingObj?.companyId?:0L,vendorLedgerDto.type)

        if (vendor == null) {

            val newVendor = Partner(
                    id = 0,
                    createdOn = LocalDateTime.now(),
                    updatedOn = LocalDateTime.now(),
                    createdBy = user,
                    updatedBy = user,
                    vendorId = vendorLedgerDto.partnerDetailId?:vendorLedgerDto.vendorId,
                    vendorName = vendorLedgerDto.vendorName,
                    // Todo: Need to fill partner Id here
                    partnerId = vendorLedgerDto.partnerId,
                    partnerDetailId = vendorLedgerDto.vendorId,
                    debitBalance = BigDecimal.ZERO,
                    creditBalance = BigDecimal.ZERO,
                    balance = BigDecimal.ZERO,
                    version = 0,
                    companyId = companyMappingObj?.companyId!!,
                    type = vendorLedgerDto.type
            )
            vendor = partnerRepo.save(newVendor)
        }
        val vendorBalance = when (vendorLedgerDto.ledgerEntryType) {
            LedgerEntryType.DEBIT -> {
                if (vendorLedgerDto.type == PartnerType.VENDOR)
                    vendor.balance.subtract(vendorLedgerDto.debitAmount)
                else
                    vendor.balance.add(vendorLedgerDto.debitAmount)
            }

            LedgerEntryType.CREDIT -> {
                if (vendorLedgerDto.type == PartnerType.VENDOR)
                    vendor.balance.add(vendorLedgerDto.creditAmount)
                else
                    vendor.balance.subtract(vendorLedgerDto.creditAmount)
            }
        }

        val debitBalance = vendor.debitBalance.add(vendorLedgerDto.debitAmount)
        val creditBalance = vendor.creditBalance.add(vendorLedgerDto.creditAmount)
        updateVendorBalance(user, vendor, vendorBalance, debitBalance, creditBalance)

        val vl = VendorLedger(
            id = 0,
            createdOn = LocalDateTime.now(),
            updatedOn = LocalDateTime.now(),
            createdBy = user,
            updatedBy = user,
            transactionDate = vendorLedgerDto.transactionDate,
            vendorId = vendorLedgerDto.vendorId,
            vendorName = vendorLedgerDto.vendorName,
            ledgerEntryType = vendorLedgerDto.ledgerEntryType,
            documentNumber = vendorLedgerDto.documentNumber,
            documentType = vendorLedgerDto.documentType,
            referenceNumber = vendorLedgerDto.referenceNumber,
            externalReferenceNumber = vendorLedgerDto.externalReferenceNumber,
            particulars = vendorLedgerDto.particulars,
            debitAmount = vendorLedgerDto.debitAmount,
            creditAmount = vendorLedgerDto.creditAmount,
            balance = vendorBalance,
            partnerId = vendorLedgerDto.partnerId,
            partnerDetailId = vendorLedgerDto.partnerDetailId,
            tenant = vendorLedgerDto.tenant,
            companyId = companyMappingObj?.companyId!!,
            type = vendorLedgerDto.type,
            client = vendorLedgerDto.client,
            remark = vendorLedgerDto.remark,
            transactionTimestamp = vendorLedgerDto.transactionalTimestamp
    )

        return vendorLedgerRepo.save(vl)
    }

    @Transactional
    fun updateVendorBalance(user: String, partner: Partner, balance: BigDecimal, debitBalance: BigDecimal, creditBalance: BigDecimal): Partner {
        log.debug("Updating vendor balance for ${partner.vendorId} with amount - $balance")

        partner.updatedOn = LocalDateTime.now()
        partner.updatedBy = user
        partner.balance = balance
        partner.debitBalance = debitBalance
        partner.creditBalance = creditBalance
        return partnerRepo.save(partner)
    }


    fun getVendorLedgerData(vendorId: List<Long>?, partnerId: Long?, from: LocalDate?, to: LocalDate?, amt1: BigDecimal?, amt2: BigDecimal?, tenant: String, page: Int?, size: Int?, customerType: Boolean, ds: String? = null, useRedis: Boolean ,firmType:List<Int>?,partnerDetailId: Long?,client: List<InvoiceType>): Any {
        var custType:PartnerType = PartnerType.VENDOR
        if(customerType) custType = PartnerType.CUSTOMER
        var tenants=companyService.findTenants(ds?:tenant)
        if(tenants.isNullOrEmpty()) return PaginationDto(0,0,false,false,null)

        var companyMapping = companyService.getCompanyTenantMappingObject(ds?:tenant)
        val size = size ?: 10
        var pageable = PageRequest.of(page ?: 0, size)
        val fromDate = from?.atStartOfDay()
        val toDate = to?.atTime(23, 59, 59)
        var startday: LocalDateTime
        if (from == null)
            startday = LocalDate.now().withDayOfMonth(1).atStartOfDay()
        else
            startday = from.minusDays(1).atTime(23, 59, 59)

        var ledgerDataPage : Slice<LedgerData?>
        var vendorIdFiltered: List<Long>?
        var getAllFlag: Boolean = false
        if(partnerId != null) {
            if(vendorId.isNullOrEmpty()) {
                vendorIdFiltered = listOf<Long>(partnerId)
            } else {
                vendorIdFiltered = if(vendorId.contains(partnerId)) listOf<Long>(partnerId) else listOf<Long>()
            }
        }else {
            if(vendorId.isNullOrEmpty()) {
                vendorIdFiltered = listOf<Long>()
                getAllFlag = true
            } else {
                vendorIdFiltered = vendorId
            }
        }

        val mapper = jacksonObjectMapper()
        var cacheKey = "bookkeeper_customer_ledger_data_${custType}_${tenant}"

        if(customerType && amt1==null && vendorId.isNullOrEmpty() && partnerId==null && amt2==null && useRedis && firmType==null && partnerDetailId==null) {
            try {
                var cacheResponseString = redisUtilityService.getfromRedis(cacheKey)
                if (!cacheResponseString.isNullOrEmpty()) {
                    var cacheResponseDTO: PaginationDto = mapper.readValue(cacheResponseString)
                    return cacheResponseDTO
                }
            }catch (e:Exception){
                log.debug("Error while getting data from key: $cacheKey")
            }
        }

        if(vendorIdFiltered.isNullOrEmpty()) {
            if (customerType && amt1!=null) {
                ledgerDataPage = vendorLedgerReadRepo.getCustomerLedgerViewDataWOCustomerFilterPage(fromDate, toDate, amt1, amt2, pageable,
                    companyMapping?.companyId!!,custType,partnerDetailId,client)
            }
            else if(customerType && amt1==null){
                ledgerDataPage = vendorLedgerReadRepo.getCustomerLedgerViewDataWOCustomerFilterPageAndAmount(fromDate, toDate, pageable, companyMapping?.companyId!!, custType,partnerDetailId,client)
            }
            else {
                ledgerDataPage = vendorLedgerReadRepo.getVendorLedgerViewDataWOVendorFilterPage(fromDate, toDate, amt1, amt2, pageable,
                    companyMapping?.companyId!!, custType,partnerDetailId,client)
            }
        } else {
            if (customerType) {
                ledgerDataPage = vendorLedgerReadRepo.getCustomerLedgerViewDataCustomerFilterPage(vendorIdFiltered, fromDate, toDate, amt1, amt2, pageable, companyMapping?.companyId!!, custType,partnerDetailId)
            }
            else {
                ledgerDataPage = vendorLedgerReadRepo.getVendorLedgerViewDataVendorFilterPage(vendorIdFiltered, fromDate, toDate, amt1, amt2, pageable, companyMapping?.companyId!!, custType,partnerDetailId)

            }
        }

        var openingBal = BigDecimal(0)
        var closeBal = BigDecimal(0)
        var periodBal = BigDecimal(0)

        var allSupplierList = ledgerDataPage.map { it?.vendorCode }.toMutableList()
        var supplierMap = supplierProxy.supplier(allSupplierList).associateBy { it?.partnerId }
        var vendorMap = mutableMapOf<Long,String>()
        if(customerType && !allSupplierList.isNullOrEmpty()){
            var vendorClient  = vendorLedgerReadRepo.getVendorClient(fromDate, toDate, custType, tenants,allSupplierList)
            vendorClient.forEach {
                var listOfClient = vendorMap.get(it.vendorId)
                if(listOfClient== null){
                    vendorMap[it.vendorId!!] = it.client?:""
                }else{
                    vendorMap[it.vendorId!!] = listOfClient+"/"+it.client
                }

            }
        }
        val beforeLoop = System.currentTimeMillis()
        ledgerDataPage.forEach {
            try{
                if (it != null) {

                    var supplier = supplierMap.get(it.vendorCode)

                    var obp = vendorLedgerReadRepo.findOpeningBalance(it.vendorCode!!, startday, companyMapping?.companyId!!,custType, PageRequest.of(0, 1))
                    var ob = if (obp.hasContent()) obp.content.get(0) ?: BigDecimal(0) else BigDecimal(0)
                    it.openingBalance = ob

                    val cbp = vendorLedgerReadRepo.findOpeningBalance(it.vendorCode!!, toDate?:LocalDateTime.now(), companyMapping?.companyId!!, custType, PageRequest.of(0, 1))
                    val cb = if (cbp.hasContent()) cbp.content[0] ?: BigDecimal.ZERO else BigDecimal.ZERO
                    it.currentBalance = cb
                    if(supplier != null) it.vendorName = supplier.partnerName

                    if (customerType) {
                        it.client = vendorMap.get(it.vendorCode ?: 0L)
                    }

                }
            }catch (e:Exception){

                log.debug("Exception is $e ")
            }
        }

        var ledgerView = VendorLedgerViewDTO(openingBal, periodBal, closeBal, ledgerDataPage.content)
        return SliceImpl<VendorLedgerViewDTO>(mutableListOf(ledgerView) , pageable, ledgerDataPage.hasNext())
    }

    fun getLedgerByVendorId(page: Int?, size: Int?, docNumber: String?, docType: DocumentType?, vendorId: Long, externalReferenceNumber: String?,
                            from: LocalDate?, to: LocalDate?, tenant: String, customerType: Boolean,distributorPDI: Long?, ds: String? = null): Any {
         var sortBy = Sort.by(Sort.Direction.ASC, "transactionTimestamp").and(Sort.by(Sort.Direction.ASC, "id"))
         val pagination = PageRequest.of(page ?: 0, size ?: 10, sortBy)
         var custType:PartnerType = PartnerType.VENDOR
         if(customerType) custType = PartnerType.CUSTOMER

        var companyMapping = if(distributorPDI != null) companyService.getTenantByPDI(distributorPDI) else companyService.getCompanyTenantMappingObject(ds?:tenant)
        var tenants= companyService.findTenants(companyMapping!!.tenant)
        if(tenants.isNullOrEmpty()) return PaginationDto(0,0,false,false,null)

        val res = vendorLedgerRepo.findAll(LedgerSpecification(docNumber, docType, vendorId, externalReferenceNumber,
                from, to, tenants,companyMapping?.companyId,custType), pagination)

        var openingBal = OpeningBal()

        val allPagination = PageRequest.of(0, 1000000000, sortBy)

        if(from != null) {
            var openingBaldata =  vendorLedgerRepo.findAll(LedgerSpecification(docNumber, docType, vendorId, externalReferenceNumber,
                    null, from.minusDays(1), tenants, companyMapping?.companyId,custType),allPagination)

            var openingBalance = openingBaldata.content.toMutableList()

            if(!openingBalance.isNullOrEmpty()) {
                var size = openingBalance.size
                openingBal.credit = openingBalance.get(size-1).creditAmount?: BigDecimal.ZERO
                openingBal.debit = openingBalance.get(size -1).debitAmount?: BigDecimal.ZERO
                openingBal.netBal = openingBalance.get(size-1).balance
            }
        }

        var allData = vendorLedgerRepo.findAll(LedgerSpecification(docNumber, docType, vendorId, externalReferenceNumber,
                from, to, tenants,companyMapping?.companyId,custType),allPagination)

        var creditBal: BigDecimal = BigDecimal.ZERO
        var debitBal : BigDecimal = BigDecimal.ZERO

        allData.forEach{

            creditBal += it.creditAmount?: BigDecimal.ZERO
            debitBal += it.debitAmount?: BigDecimal.ZERO
        }
        var netBal : BigDecimal = BigDecimal(0)

        if(allData.hasContent()) {
            var list = allData.content.toMutableList()
            var size =list.size
            netBal = list.get(size-1).balance
        }


        var closingBal = ClosingBal(debitBal,creditBal,netBal)
        openingBal.credit=null
        openingBal.debit=null

        res.content.forEach {
            if(it.documentType.name.equals(DocumentType.PURCHASE_INVOICE.name)) {
                it.source = companyMapping!!.tenantName
            }else{
                it.source = "VAULT"
            }
            it.transactionTimestamp = getISTDateTime(it.transactionTimestamp)
        }

        return SliceImpl<VendorLedgerInfoViewDTO>(mutableListOf(VendorLedgerInfoViewDTO(openingBal,closingBal,res.content)), allPagination, res.hasNext())
    }

    @Async
    fun writeLedgerData(id: Long, successList: MutableList<LedgerData?>, tenant: String,prefix:String?, startday: LocalDateTime, to: LocalDateTime?,custType:PartnerType, customerType:Boolean, email:String?, from :LocalDateTime?) {
        log.debug("Inside writeLedgerData: writting data into csv")
        val csvPrinter: CSVPrinter
        //getOpeningBal(successList, startday, tenant, to ?: LocalDateTime.now(),custType)
        var bytes = ByteArrayOutputStream()
        if(customerType){
            csvPrinter = CSVPrinter(bytes.bufferedWriter(), CSVFormat.DEFAULT
                    .withHeader("Customer Code","PDI", "Customer Name", AccountConstants.CUSTOMER_TYPE, "Opening Balance", "Period Transaction", "Current Balance"))
        }else{
            csvPrinter = CSVPrinter(bytes.bufferedWriter(), CSVFormat.DEFAULT
                    .withHeader("Vendor Code","PDI","Vendor Name", "Opening Balance", "Period Transaction", "Current Balance"))
        }
        successList!!.forEach {
            if (it != null) {
                if (customerType) {
                    csvPrinter.printRecord(it.vendorCode, it.vendorDetailId,it.vendorName, it.client, it.openingBalance, it.periodTransaction, it.currentBalance)
                } else
                    csvPrinter.printRecord(it.vendorCode,it.vendorDetailId, it.vendorName, it.openingBalance, it.periodTransaction, it.currentBalance)
            }
        }
        csvPrinter.flush()
        log.debug("bytes toByte Array ->  {}", bytes.toByteArray())
        val uploaded = s3FileUtilityService.uploadFile(bytes.toByteArray(), "CSV", prefix)
        log.debug("uploaded $uploaded")
        if(from !=null && to !=null) {
            notificationMailService.emailFile("Ledger Summary Report", from.toLocalDate(), to.toLocalDate(), listOf(email!!), uploaded ?: "Failed")
        }else{
            log.error("From Date and To Date cannot be null")
        }
        vendorFileService.update(id, uploaded ?: "Failed")
    }


    fun getVendorLedgerURL(from: LocalDateTime?, to: LocalDateTime?, tenant: String, createdBy: String?, customerType: Boolean, ds: String? = null, email:String?,firmType:List<Int>?,client: List<InvoiceType>?): CreateResultData {
        log.debug("Inside getVendorInfoSummaryURL")
        var custType:PartnerType = PartnerType.VENDOR
        if(customerType) custType = PartnerType.CUSTOMER
        var startday: LocalDateTime
        var list: MutableList<LedgerData?>? = mutableListOf()
        if (from == null)
            startday = LocalDate.now().withDayOfMonth(1).atStartOfDay()
        else
            startday = from.minusDays(1).toLocalDate().atTime(23, 59, 59)

        var tenants=companyService.findTenants(ds?:tenant)
        if(tenants.isNullOrEmpty()) return CreateResultData(200,"No Data",null)

        var companyMapping = companyService.getCompanyTenantMappingObject(ds?:tenant)



//        val list = vendorLedgerRepo.getVendorLedgerViewData(from?.toLocalDate(),to?.toLocalDate(),null,null,companyMapping?.companyId!!,custType)
        log.debug("from ${from?.toLocalDate()} and to ${to?.toLocalDate()} and company ${companyMapping?.companyId!!} and custType $custType")
        if(customerType && client==null) {
            list = vendorLedgerRepo.getCustomerLedgerViewDataWOCustomerFilterPageSummaryUrl(from?.toLocalDate(), to?.toLocalDate(), null, null, companyMapping?.companyId!!, custType)
        }else if(customerType && client != null){
            list = vendorLedgerRepo.getCustomerLedgerViewFirmDataWOCustomerFilterPageSummaryUrl(from?.toLocalDate(), to?.toLocalDate(), null, null, companyMapping?.companyId!!, custType, client)
        }else{
            list = vendorLedgerRepo.getVendorLedgerViewDataForVendorFilterSummaryUrl(from?.toLocalDate(), to?.toLocalDate(), null, null, companyMapping?.companyId!!, custType)
        }

        if (list.isNullOrEmpty())
            throw RequestException("Data is not available for date range "+"${from?.toLocalDate()}"+" to "+"${to?.toLocalDate()}")

        var prefix = "LedgerList-${from?.toLocalDate()}--${to?.toLocalDate()}"

        try {
            var file = vendorFileService.saveReport(tenants[0]!!, VendorDataLinks(null, LocalDateTime.now(), null, "IN_PROGRESS", VendorDataEnum.LEDGER, null, createdBy,tenants[0]!!,customerType))
            writeLedgerData(file.id!!, list, tenants[0]!!,prefix, startday, to, custType, customerType,email, from)
        } catch (e: Exception) {
            e.printStackTrace()
        }
      return  CreateResultData(200,"Success",null)

    }

    private fun getOpeningBal(list: MutableList<LedgerData?>, startday: LocalDateTime, tenant: String, to: LocalDateTime,customerType: PartnerType) {
        var partnerMasterList =list.map { it?.vendorCode }
        var allSupplierData = supplierProxy.supplier(partnerMasterList).associateBy { it?.partnerId }
        var companyMapping = companyTenantMappingRepo.getByTenant(tenant)

        list.forEach {
            if (it != null) {
                var supplier = allSupplierData.get(it.vendorCode!!)
                var obp = vendorLedgerRepo.findOpeningBalance(it.vendorCode!!, startday.toLocalDate(), companyMapping?.companyId!!, customerType,PageRequest.of(0, 1))
                var ob = if (obp.hasContent()) obp.content.get(0) ?: BigDecimal(0) else BigDecimal(0)
                it.openingBalance = ob

                // find closing balance
                val cbp = vendorLedgerRepo.findOpeningBalance(it.vendorCode!!, to.toLocalDate(), companyMapping?.companyId!!,customerType, PageRequest.of(0, 1))
                val cb = if (cbp.hasContent()) cbp.content[0] ?: BigDecimal.ZERO else BigDecimal.ZERO
                it.currentBalance = cb

                if(supplier != null) it.vendorName = supplier.partnerName

            }
        }
    }

    fun getVendorLedgerDownload(createdBy: String, customerType: Boolean, tenant: String, ds: String? = null): CreateResultData {

        var tenants=companyService.findTenants(ds?:tenant)

        var file=vendorDataLinkRepo.getVendorLedgerLink(createdBy, tenants[0]!!, customerType)
        if (file.isNotEmpty()) {
            return  CreateResultData(200,"Success", file[0]!!.link)
        }

        return  CreateResultData(200,"Success",null)
    }

    fun writeExcelLedgerDataForVendorId(id: Long, from: LocalDate?, to: LocalDate?, successList: MutableList<VendorLedger?>?, tenant: String,prefix:String?, openingBal: OpeningBal, closingBal: ClosingBal, vendorName: String, userEmail: String){
        val company = companyService.getCompanyTenantMappingObject(tenant)
        val supplier = supplierProxy.getPartnerDetails(null, null,null,null,id = company?.partnerDetailId?.toInt(),null, withBankDetail = true)
        var netBal = openingBal.netBal
        val dataMap: MutableList<MutableMap<String, Any?>> = mutableListOf()
        val openCreditBal = if(openingBal.netBal<= BigDecimal.ZERO){openingBal.netBal.toString()}else{""}
        val openDebitBal = if(openingBal.netBal>= BigDecimal.ZERO){openingBal.netBal.toString()}else{""}
        val closingNetBalDebit = if((closingBal.debit ?: BigDecimal.ZERO) <= closingBal.credit){closingBal.netBal}else{""}
        val closingNetBalCredit = if((closingBal.credit ?: BigDecimal.ZERO) < closingBal.debit){closingBal.netBal}else{""}

        successList!!.forEach {
            var ledgerDataMap: MutableMap<String, Any?> = mutableMapOf()
            try {
                if (it != null && it.documentType.name.equals(DocumentType.CREDIT_NOTE_PR.name)) {
                    var ledgerCreditNoteDtoList: List<LedgerCreditNoteDto> = mutableListOf()
                    ledgerCreditNoteDtoList = ledgerResource.getCreditNoteDetails(it.documentNumber)

                    ledgerCreditNoteDtoList.forEach { cnl ->
                        ledgerDataMap["txnDt"] = it.transactionDate.format(DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_DDMMYYY))
                        ledgerDataMap["docType"] = it.documentType.name
                        ledgerDataMap["particulars"] = it.particulars
                        ledgerDataMap["docNo"] = it.documentNumber
                        ledgerDataMap["internalRef"] = cnl.debitNoteNumber
                        ledgerDataMap["externalRef"] = it.externalReferenceNumber
                        ledgerDataMap["debit"] = cnl.creditNoteAmount
                        ledgerDataMap["credit"] = null
                        ledgerDataMap["netBal"] = netBal-cnl.creditNoteAmount
                        netBal -= cnl.creditNoteAmount
                        dataMap.add(ledgerDataMap.toMutableMap())
                    }

                    netBal = it.balance
                    return@forEach
                }
                else if (it != null && it.documentType.name.equals(DocumentType.VENDOR_PAYMENT.name)) {
                    var ledgerPayments: List<LedgerPaymentDto> = mutableListOf()
                    ledgerPayments = ledgerResource.getPaymentDetails(it.documentNumber)

                    ledgerPayments.forEach { lp ->
                        ledgerDataMap["txnDt"] = it.transactionDate.format(DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_DDMMYYY))
                        ledgerDataMap["docType"] = it.documentType.name
                        ledgerDataMap["particulars"] = it.particulars
                        ledgerDataMap["docNo"] = it.documentNumber
                        ledgerDataMap["internalRef"] = lp.invoiceNumber
                        ledgerDataMap["externalRef"] = it.externalReferenceNumber
                        ledgerDataMap["debit"] = lp.paidAmount
                        ledgerDataMap["credit"] = it.creditAmount
                        ledgerDataMap["netBal"] = netBal-lp.paidAmount
                        netBal -= lp.paidAmount
                        dataMap.add(ledgerDataMap.toMutableMap())
                    }

                    netBal = it.balance
                    return@forEach
                }else if (it != null && it.documentType.name.equals(DocumentType.CX_RECEIPT.name)){

                    var ledgerPayments: List<LedgerPaymentDto> = mutableListOf()
                    ledgerPayments = ledgerResource.getPaymentDetails(it.documentNumber)
                    var invoiceNum: String =""
                    var paidAmount: BigDecimal = BigDecimal.ZERO
                    ledgerPayments.forEach { lp ->
                        invoiceNum += lp.invoiceNumber +','
                    }
                    invoiceNum.removeSuffix(",")
                    ledgerDataMap["txnDt"] = it.transactionDate.format(DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_DDMMYYY))
                    ledgerDataMap["docType"] = it.documentType.name
                    ledgerDataMap["particulars"] = it.particulars
                    ledgerDataMap["docNo"] = it.documentNumber
                    ledgerDataMap["internalRef"] = invoiceNum
                    ledgerDataMap["externalRef"] = it.externalReferenceNumber
                    ledgerDataMap["debit"] = null
                    ledgerDataMap["credit"] = it.creditAmount
                    ledgerDataMap["netBal"] = it.balance
                    netBal = it.balance
                    dataMap.add(ledgerDataMap.toMutableMap())
                    return@forEach
                }

            }catch (e:Exception){
                log.error("Error while generating ledger $e", e)
                throw RequestException("Error while generating ledger")
            }
            ledgerDataMap["txnDt"] = it?.transactionDate?.format(DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_DDMMYYY))
            ledgerDataMap["docType"] = it?.documentType?.name
            ledgerDataMap["particulars"] = it?.particulars
            ledgerDataMap["docNo"] = it?.documentNumber
            ledgerDataMap["internalRef"] = it?.referenceNumber
            ledgerDataMap["externalRef"] = it?.externalReferenceNumber
            ledgerDataMap["debit"] = it?.debitAmount
            ledgerDataMap["credit"] = it?.creditAmount
            ledgerDataMap["netBal"] = it?.balance
            dataMap.add(ledgerDataMap.toMutableMap())
            netBal = it?.balance?:BigDecimal.ZERO
        }
        val headerMap: MutableMap<String, String> = mutableMapOf()
        headerMap["companyName"] = supplier?.get(0)?.name?:""
        headerMap["companyAddress"] = supplier?.get(0)?.address1?:""
        headerMap["fromRange"] =
            from?.format(DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_DDMMYYY))?:LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_DDMMYYY))
        headerMap["toRange"] = to?.format(DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_DDMMYYY))?:LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_DDMMYYY))
        headerMap["partnerName"] = "[" + successList[0]?.partnerDetailId + "] - " + vendorName
        headerMap["openingDebitBal"] = openDebitBal
        headerMap["openingCreditBal"] = openCreditBal
        headerMap["debitTotal"] = closingBal.debit.toString()
        headerMap["creditTotal"] = closingBal.credit.toString()
        headerMap["netBalDebit"] = closingNetBalDebit.toString().replace("-","")
        headerMap["netBalCredit"] = closingNetBalCredit.toString().replace("-","")
        try{
            var parameters :MutableMap<String, Any> = mutableMapOf()
            successList[0]?.partnerDetailId?.let { parameters["retailerPdi"] = it}
            parameters["fromDate"] = from?:LocalDate.now()
            parameters["toDate"] = to?:LocalDate.now()
            parameters["userEmail"] = userEmail
            paperServicePusher.createPaperFile(compressionUtil.compress(ExcelDataDTO(headerMap, dataMap,ReferenceType.SEND_EVENT,
                userEmail,ledgerTemplate,parameters).toJson()))
        }catch(e: Exception){
            log.error("Error while sending event to the user $e", e)
            throw RequestException("Error while creating ledger report $e")
        }
    }
    @Async
    fun writeLedgerDataForVendorId(id: Long, successList: MutableList<VendorLedger?>?, tenant: String,prefix:String?, openingBal: OpeningBal, closingBal: ClosingBal) {
        log.debug("Inside writeLedgerDataForVendorId: writting data into csv")

        var companyTenant = companyTenantMappingRepo.getAllTenantByCompany(tenant!!).associateBy { it?.tenant }

        log.debug("company map : ${companyTenant}")

        var bytes = ByteArrayOutputStream()
        val csvPrinter = CSVPrinter(bytes.bufferedWriter(), CSVFormat.DEFAULT
                .withHeader("Transaction Date", "Document Type", "Particulars", "Document No", "Internal Reference No", "External Reference No","Debit","Credit","Net Balance","Source","Remark"))

        csvPrinter.printRecord(null, null, "OPENING BALANCE", null, null ,null, null, null, openingBal.netBal)

        var netBal = BigDecimal.ZERO
        netBal = openingBal.netBal

            successList!!.forEach {
                if(it?.documentType?.name.equals(DocumentType.PURCHASE_INVOICE.name)) {
                    it?.source = companyTenant.get(it?.tenant)?.tenantName
                }else{
                    it?.source = "VAULT"
                }
                try {
                    log.debug("type == ${it?.documentType?.name}")

                    if (it != null && it.documentType.name.equals(DocumentType.CREDIT_NOTE_PR.name)) {
                        var ledgerCreditNoteDtoList: List<LedgerCreditNoteDto> = mutableListOf()
                        ledgerCreditNoteDtoList = ledgerResource.getCreditNoteDetails(it.documentNumber)

                        ledgerCreditNoteDtoList.forEach { cnl ->
                            csvPrinter.printRecord(it.transactionDate, it.documentType, it.particulars, it.documentNumber, cnl.debitNoteNumber, it.externalReferenceNumber, cnl.creditNoteAmount, null, netBal-cnl.creditNoteAmount,it.source,it.remark)
                          netBal = netBal-cnl.creditNoteAmount
                        }

                        netBal = it.balance
                        return@forEach
                        // csvPrinter.printRecord(it.transactionDate.toLocalDate(), it.documentType, it.particulars, it.documentNumber, it.referenceNumber ,it.externalReferenceNumber, it.debitAmount, it.creditAmount, it.balance)
                    }// if
                    else if (it != null && it.documentType.name.equals(DocumentType.VENDOR_PAYMENT.name)) {
                        var ledgerPayments: List<LedgerPaymentDto> = mutableListOf()
                        ledgerPayments = ledgerResource.getPaymentDetails(it.documentNumber)

                        ledgerPayments.forEach { lp ->

                            csvPrinter.printRecord(it.transactionDate, it.documentType, it.particulars, it.documentNumber, lp.invoiceNumber, it.externalReferenceNumber, lp.paidAmount, it.creditAmount, netBal-lp.paidAmount,it.source,it.remark)
                            netBal=netBal-lp.paidAmount
                        }

                        netBal = it.balance
                        return@forEach
                    }else if (it != null && it.documentType.name.equals(DocumentType.CX_RECEIPT.name)){
                        var ledgerPayments: List<LedgerPaymentDto> = mutableListOf()
                        ledgerPayments = ledgerResource.getPaymentDetails(it.documentNumber)

                        ledgerPayments.forEach { lp ->
                            csvPrinter.printRecord(it.transactionDate, it.documentType, it.particulars, it.documentNumber, lp.invoiceNumber, it.externalReferenceNumber, null, lp.paidAmount, netBal-lp.paidAmount,it.source,it.remark)
                            netBal=netBal-lp.paidAmount
                        }

                        netBal = it.balance
                        return@forEach
                    }

                }catch (e:Exception){
                    log.debug("failed to convert id!")
                }

                csvPrinter.printRecord(it!!.transactionDate, it.documentType, it.particulars, it.documentNumber, it.referenceNumber, it.externalReferenceNumber, it.debitAmount, it.creditAmount, it.balance,it.source,it.remark)
                netBal = it.balance
            }
        csvPrinter.printRecord(null, null, "CLOSING BALANCE", null, null ,null, null, null, closingBal.netBal)

        csvPrinter.flush()
        val uploaded = s3FileUtilityService.uploadFile(bytes.toByteArray(), "CSV", "$prefix")
        vendorFileService.update(id, uploaded ?: "Failed")
    }

    fun getLedgerDetailURL(vendorId: Long?, from: LocalDate?, to: LocalDate?, tenant: String, createdBy: String?, customerType: Boolean, ds: String? = null, downloadType: DownloadFileType?, userEmail: String): CreateResultData {
        log.debug("Inside getVendorLedgerDetailURL")

        var custType :PartnerType = PartnerType.VENDOR
        if (customerType) {
            custType = PartnerType.CUSTOMER
        }
        val sortBy = Sort.by(Sort.Direction.ASC, "transactionTimestamp").and(Sort.by(Sort.Direction.ASC, "id"))
        val pagination = PageRequest.of(0, 1999999999, sortBy)
        val tenants = companyService.findTenants(ds?:tenant)

        val companyMapping = companyService.getCompanyTenantMappingObject(ds?:tenant)

        val allData = vendorLedgerRepo.findAll(LedgerSpecification(null, null, vendorId, null,
            from, to, tenants, companyMapping?.companyId,custType),pagination)
        val list = allData.content.toMutableList()
        if (list.isEmpty())
            throw RequestException("Data is not available for date range $from to $to")

        var prefix = ""
        val listVendorId: List<Long?> = listOf(vendorId)
        val supplier = supplierProxy.supplier(listVendorId)

        if (list.isNotEmpty()) {
            prefix = "Ledger-detail-${supplier[0]?.partnerName}-${from}-to-${to}-"
        }

        try {
            val openingBal = OpeningBal()

            if(from != null) {
                val openingBaldata =  vendorLedgerRepo.findAll(LedgerSpecification(null, null, vendorId, null,
                    null, from.minusDays(1), tenants,companyMapping?.companyId,custType),pagination)

                val openingBalance = openingBaldata.content.toMutableList()
                if (openingBalance.isNotEmpty()) {
                    val size = openingBalance.size
                    openingBal.credit = openingBalance.get(size - 1).creditAmount ?: BigDecimal.ZERO
                    openingBal.debit = openingBalance.get(size - 1).debitAmount ?: BigDecimal.ZERO
                    openingBal.netBal = openingBalance.get(size - 1).balance
                }
            }

            var allData = vendorLedgerRepo.findAll(LedgerSpecification(null, null, vendorId, null,
                from, to, tenants,companyMapping?.companyId,custType),pagination)

            var allDatalist = allData.content.toMutableList()
            var creditBal: BigDecimal = BigDecimal(0)
            var debitBal: BigDecimal = BigDecimal(0)
            allData.forEach{

                creditBal += it.creditAmount?: BigDecimal.ZERO
                debitBal += it.debitAmount?: BigDecimal.ZERO
            }
            creditBal = creditBal.plus(if(openingBal.netBal<= BigDecimal.ZERO){-(openingBal.netBal)}else{BigDecimal.ZERO})
            debitBal = debitBal.plus(if(openingBal.netBal>= BigDecimal.ZERO){openingBal.netBal}else{BigDecimal.ZERO})
            var netBal: BigDecimal = BigDecimal(0)
            if (allDatalist.isNotEmpty()) {
                val size = allDatalist.size
                netBal = allDatalist.get(size - 1).balance.setScale(2, RoundingMode.HALF_EVEN)
            }

            val closingBal = ClosingBal(debitBal, creditBal, netBal)
            val vendorName = allData.content[0].vendorName
            val file = vendorFileService.saveReport(tenants[0]!!, VendorDataLinks(null, LocalDateTime.now(), null, "IN_PROGRESS", VendorDataEnum.LEDGER_DETAIL, vendorId, createdBy,tenants[0], customerType))
            when (downloadType){
                DownloadFileType.EMAIL -> writeExcelLedgerDataForVendorId(file.id!!, from, to, list, tenants[0]!!, prefix, openingBal, closingBal, vendorName ?: "", userEmail)
                else -> writeLedgerDataForVendorId(file.id!!, list, tenants[0]!!, prefix , openingBal, closingBal)
            }
        } catch (e: Exception) {
            log.error("Unable to download the entire data ", e)
            throw RequestException("Unable to download the requested data")
        }
        return when (downloadType){
            DownloadFileType.EMAIL -> CreateResultData(200, "Email Sent", null)
            else -> CreateResultData(200, "Success", null)
        }
    }

    fun getVendorLedgerDetailDownloadLink(tenant: String, createdBy: String, customerType: Boolean?, ds: String? = null): CreateResultData {

        var tenants=companyService.findTenants(ds?:tenant)

        var file=vendorDataLinkRepo.getLedgerDetailLink(tenants[0], createdBy, customerType)
        if (file.isNotEmpty()) {
            return CreateResultData(200, "Success", file[0]!!.link)
        }

        return CreateResultData(200, "Success", null)
    }

    fun getAllUpdated(tenant: String, customerType: Boolean, ds: String? = null): List<SupplierListDTO?> {
        var custType:PartnerType = PartnerType.VENDOR
        if(customerType) custType = PartnerType.CUSTOMER
        var tenants=companyService.findTenants(ds?:tenant)

        var companyMapping = companyService.getCompanyTenantMappingObject(ds?:tenant)
        if (tenants.isNullOrEmpty()) return mutableListOf()

        var listOfPartnerDetailId = vendorLedgerRepo.getAllPartnerDetailId(companyMapping?.companyId!!,custType)
        return if (listOfPartnerDetailId.isNullOrEmpty()) mutableListOf() else supplierProxy.allTotalSupplier(true, listOfPartnerDetailId)
    }

    fun getSupplierName(supplierId: Long, companyId: Long, type: PartnerType): String? {
        log.debug("Inside getSupplierName: $supplierId : $companyId")
        val name =  partnerRepo.getVendorName(supplierId, companyId, type) ?: ""

        log.debug("vendor name is : $name")
        return name
    }

    fun sendLedgerMail(uploaded: String?, from :LocalDateTime?, to:LocalDateTime?, email:String?) {
        try {
            var toEmail: MutableList<String>? = mutableListOf()
            toEmail!!.add(email!!)

            log.debug("$toEmail")
            notificationService.emailDocument(
                uploaded,
                from = from?.toLocalDate(),
                to = to?.toLocalDate(),
                toEmailIds = toEmail.toList()
            )
        } catch (e: Exception) {
            log.debug("Error while sending mail $e")
        }
    }
    fun getAllFirms(): List<FirmDTO?>? {
        var firms = supplierProxy.getFirmTypes(true,null,null)
        return firms

    }

    fun getRetailerOutstanding(distributorPDI: Long,retailerId:Long): RetailerOutstandingDTO {
        var distributorDetails = invoiceService.getTenantCodeAndCustomerType(distributorPDI)
        var tenant = distributorDetails.tenant
        var orderType = distributorDetails.fulfilmentType
        var customerType = orderType == OrderType.B2B2B

        var companyTenantMappingObj = if(customerType) {
            companyService.getTenantByPDI(distributorPDI)
        }else {
            companyService.getCompanyTenantMappingObject(tenant)
        }
        var retailerPartnerId = supplierProxy.getMasterId(retailerId) ?: 0L
        val tradeCreditOutstanding = tradeCreditPaymentService.getOutstandingTradeCreditByPartnerDetailId(retailerId)
        val partner = vendorRepo.getPartners(retailerPartnerId, PartnerType.CUSTOMER,companyTenantMappingObj?.companyId?:0)?:
        return RetailerOutstandingDTO(distributorPDI,retailerId, BigDecimal.ZERO,0, tradeCreditOutstanding.tradeCreditOutstandingAmount, tradeCreditOutstanding.tradeCreditOverdueAmount, tradeCreditOutstanding.maxTradeCreditDueDays, LocalDate.now())
        val page = PageRequest.of(0, 1)
        val bkInvoice = bkInvoiceRepo.findMinDatesByTypeAndClientAndPartnerDetailIdAndTenant(retailerId, PartnerType.CUSTOMER, listOf(InvoiceType.RIO, InvoiceType.EASY_SOL), tenant, page)
        var maxDueDays = 0L
        var minDueDate = LocalDate.now()
        if (bkInvoice.hasContent()) {
            maxDueDays = java.time.temporal.ChronoUnit.DAYS.between(bkInvoice.content[0]?.minCreatedOn?.toLocalDate(), LocalDate.now())
            minDueDate = bkInvoice.content[0]?.minDueDate

        }
        val balance = partner.balance
        return RetailerOutstandingDTO(distributorPDI,retailerId,balance,maxDueDays, tradeCreditOutstanding.tradeCreditOutstandingAmount, tradeCreditOutstanding.tradeCreditOverdueAmount, tradeCreditOutstanding.maxTradeCreditDueDays, minDueDate)
    }

    fun getPaymentDetails(settlementNumber: String): List<LedgerPaymentDto> {
        val settlements = receiptService.getAllSettlementsFromReceipt(settlementNumber)
        val ledgerPayments: MutableList<LedgerPaymentDto> = mutableListOf()
        settlements.forEach {
            val id = it.id
            val invoices = invoiceService.getInvoicesBySettlementId(id)
            var retailerDebitNote:List<RetailerDebitNote>? = mutableListOf()
            if (invoices.isEmpty()){
                retailerDebitNote = retailerDebitNoteService.getRetailerDebitNoteBySettlementId(id)
            }
            if(invoices.isEmpty() && retailerDebitNote?.isEmpty() == true)throw RequestException("No invoices/retailer debit notes found for settlement id: $id")

            if(invoices.isNotEmpty()){
                invoices.forEach { invoice ->
                    val invSet = invoiceService.getInvoiceSettlementData(invoice.id, it.id)
                    val ledgerPaymentDto = LedgerPaymentDto(
                        it.createdOn, it.createdBy, id, invoice.invoiceNum,
                        it.paymentType, it.paymentDate, it.paymentReference, BigDecimal(invoice.amount),
                        invSet.amount.setScale(2, RoundingMode.HALF_EVEN), invSet.invoiceStatus, it.settlementNumber!!
                    )

                    ledgerPayments.add(ledgerPaymentDto)
                }
            }else{
                retailerDebitNote?.forEach {rdn->
                    val rdnSet = retailerDebitNoteSettlementMappingService.getRetailerDebitNoteSettlementMapping(rdn.id!!, it.id)
                    val ledgerPaymentDto = LedgerPaymentDto(
                        it.createdOn, it.createdBy, id, rdn.documentNumber,
                        it.paymentType, it.paymentDate, it.paymentReference, BigDecimal(rdn.amount),
                        BigDecimal(rdnSet.paidAmount?:0.00), rdn.status, it.settlementNumber!!
                    )

                    ledgerPayments.add(ledgerPaymentDto)
                }
            }
        }
        return ledgerPayments
    }

    fun getExistingLedgerAmount(partnerDetailId: Long, type: PartnerType?, tenant: String, documentNumber: String, receiptNumber: String?): Double{
        val ledger = vendorLedgerReadRepo.getLedgerByTenantAndTypeAndPartnerDetailIdAndDocumentNumberOrReceiptNumber(tenant, type?:PartnerType.CUSTOMER, partnerDetailId, documentNumber, receiptNumber)
        if (ledger != null){
            return when (ledger.ledgerEntryType){
                LedgerEntryType.CREDIT -> ledger.creditAmount?.toDouble()?:0.0
                LedgerEntryType.DEBIT -> ledger.debitAmount?.toDouble()?:0.0
            }
        }
        return 0.0
    }


    @Transactional
    fun checkAndAddLedgerEntry(user: String, vendorLedger: VendorLedgerDto): VendorLedger {
        val vl = vendorLedgerRepo.findDuplicateLedger(
            vendorLedger.documentNumber,
            vendorLedger.referenceNumber!!,
            vendorLedger.ledgerEntryType!!
        )
        if (vl != null) {
            return vl
        }
        return addVendorLedgerEntry(user, vendorLedger)
    }
}
