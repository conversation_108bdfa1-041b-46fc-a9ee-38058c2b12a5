package com.pharmeasy.service.ops

import com.pharmeasy.data.ops.PaymentExcess
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.advancepayment.AdvancePaymentDto
import com.pharmeasy.model.advancepayment.PaymentRefItem
import com.pharmeasy.model.advancepayment.UpdateAdvancePaymentDto
import com.pharmeasy.proxy.SupplierProxy
import com.pharmeasy.repo.ops.PaymentExcessRepository
import com.pharmeasy.service.AdvancePaymentService
import com.pharmeasy.service.SettlementService
import com.pharmeasy.type.*
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime

@Service
class PaymentExcessService {
    companion object {
        private val log = LoggerFactory.getLogger(PaymentExcessService::class.java)
    }

    @Autowired
    private lateinit var paymentExcessRepository: PaymentExcessRepository

    @Autowired
    private lateinit var advancePaymentService: AdvancePaymentService

    @Autowired
    private lateinit var supplierProxy: SupplierProxy

    @Autowired
    private lateinit var settlementService: SettlementService

    @Transactional
    fun handleAdvancePayment(amount: BigDecimal, transactionNumber: String, pdi: Long?, distributorId: Long?, tenant: String, transactionType: PaymentType): PaymentExcess {
        val excessPayment = recordAdvancePayment(amount, transactionNumber)
        val advancePaymentId = createAdvancePaymentForAutoSettlement(amount, transactionNumber, pdi, distributorId, tenant, transactionType)
        excessPayment.advancePaymentId = advancePaymentId
        save(excessPayment)
        return excessPayment
    }

    @Transactional
    fun recordAdvancePayment(amount: BigDecimal, referenceId: String): PaymentExcess {
        val excess = paymentExcessRepository.getAdvanceByRefNum(referenceId)
        if(excess != null){
            throw RequestException("Advance payment with reference number $referenceId already exists")
        }
        val paymentExcess = PaymentExcess(
            amount = amount, paymentTransaction = null, createdAt = LocalDateTime.now(),
            creditNoteNumber = null, utr = referenceId, advancePaymentId = null
        )
        return save(paymentExcess)
    }

    @Transactional
    fun save(paymentExcess: PaymentExcess): PaymentExcess {
        return paymentExcessRepository.save(paymentExcess)
    }

    @Transactional
    fun createAdvancePaymentForAutoSettlement(amount: BigDecimal, paymentReference: String, partnerDetailId: Long?, distributorId: Long?, tenant: String, transactionType: PaymentType): String?{
        val supplier = supplierProxy.supplier(null, partnerDetailId)
        val settlements = settlementService.getSettlementByReferenceNumberAndPartnerDetailId(paymentReference, partnerDetailId!!, tenant)
        val advancePaymentDto = AdvancePaymentDto(
            amount = amount,
            partnerId = supplier[0]?.partnerId!!,
            partnerName = supplier[0]?.partnerName!!,
            partnerDetailId = partnerDetailId,
            tenant = tenant,
            type = PartnerType.CUSTOMER,
            client = InvoiceType.RIO,
            typeOfAdvance = AdvanceType.INVOICE,
            remarks = "CREATED FROM RIO AUTO SETTLEMENT",
            createdByName = "SYSTEM",
            userEmail = "",
            refDocuments = listOf(PaymentRefItem(paymentReference, LocalDate.now())),
            source = if(settlements.isEmpty()){
                AdvancePaymentSource.SYSTEM}else { AdvancePaymentSource.RIO_PAY }
        )
        val advancePayment = advancePaymentService.createAdvancePayment(advancePaymentDto, "SYSTEM", null)
        val updateAdvancePaymentDto = UpdateAdvancePaymentDto(
            amount = advancePayment.amount,
            paymentReference = paymentReference,
            paymentType = transactionType,
            paymentDate = LocalDate.now(),
            chequeDate = null,
            bankName = "",
            remarks = "APPROVED VIA RIO PAY",
            tenant = tenant,
            change = true,
            source = if(settlements.isEmpty()){
                AdvancePaymentSource.SYSTEM}else { AdvancePaymentSource.RIO_PAY },
            isRioTransaction = true
        )
        advancePaymentService.checkerAdvancePayment(advancePayment.id!!, updateAdvancePaymentDto, advancePayment.assignedToId!!, null)
        val advance = advancePaymentService.getAdvancePaymentById(advancePayment.id!!)
        return advance.documentId
    }


}