package com.pharmeasy.service

import com.pharmeasy.data.CashBankLedger
import com.pharmeasy.data.Company
import com.pharmeasy.data.CompanyTenantMapping
import com.pharmeasy.data.VendorDataEnum
import com.pharmeasy.data.VendorDataLinks
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.CashBankLedgerDto
import com.pharmeasy.model.CompanyLedgerInfoDTO
import com.pharmeasy.model.CreateResultData
import com.pharmeasy.model.PaginationDto
import com.pharmeasy.repo.CashBankLedgerRepo
import com.pharmeasy.repo.CompanyRepo
import com.pharmeasy.repo.CompanyTenantMappingRepo
import com.pharmeasy.repo.VendorDataLinksRepo
import com.pharmeasy.type.LedgerEntryType
import com.pharmeasy.type.PaymentMode
import com.pharmeasy.type.SettlementType
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVPrinter
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.repository.findByIdOrNull
import org.springframework.orm.ObjectOptimisticLockingFailureException
import org.springframework.retry.annotation.Backoff
import org.springframework.retry.annotation.Retryable
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.io.ByteArrayOutputStream
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime

@Service
class CashBankLedgerService {

    companion object {
        private val log = LoggerFactory.getLogger(CashBankLedgerService::class.java)
    }

    @Autowired
    private lateinit var cashBankLedgerRepo: CashBankLedgerRepo

    @Autowired
    private lateinit var companyRepo: CompanyRepo

    @Autowired
    private lateinit var companyTenantMappingRepo: CompanyTenantMappingRepo

    @Autowired
    private lateinit var documentMasterService: DocumentMasterService

    @Autowired
    private lateinit var s3FileUtilityService: S3FileUtilityService

    @Autowired
    private lateinit var vendorFileService: VendorFileService

    @Autowired
    private lateinit var vendorDataLinkRepo: VendorDataLinksRepo

    @Retryable(
            value = [ObjectOptimisticLockingFailureException::class],
            maxAttempts = 3, backoff = Backoff(delay = 1500)
    )
    fun addCashBankLedgerEntry(user: String, cashBankLedgerDto: CashBankLedgerDto): CashBankLedger {
        log.debug("Inside addCashBankLedgerEntry: {}", cashBankLedgerDto)

        var companyMappingObj: CompanyTenantMapping? = companyTenantMappingRepo.getByTenant(cashBankLedgerDto.tenant)
                ?: throw RequestException("no tenant mapping found for ${cashBankLedgerDto.tenant} ")

        var companyObj = companyRepo.getCompanyBalanceWithLock(companyMappingObj?.companyId?:0L)

        val companyBalance = when (cashBankLedgerDto.ledgerEntryType) {
            LedgerEntryType.DEBIT -> {
                if (cashBankLedgerDto.paymentMode == PaymentMode.BANK)
                    companyObj.bankBalance.add(cashBankLedgerDto.debitAmount)
                else
                    companyObj.cashBalance.add(cashBankLedgerDto.debitAmount)
            }
            LedgerEntryType.CREDIT -> {
                if (cashBankLedgerDto.paymentMode == PaymentMode.BANK)
                    companyObj.bankBalance.subtract(cashBankLedgerDto.creditAmount)
                else
                    companyObj.cashBalance.subtract(cashBankLedgerDto.creditAmount)
            }
        }

        updateCompanyBalance(user, companyObj, companyBalance,cashBankLedgerDto.paymentMode)

        val cl = CashBankLedger(
                id = 0,
                createdOn = LocalDateTime.now(),
                updatedOn = LocalDateTime.now(),
                createdBy = user,
                updatedBy = user,
                transactionDate = cashBankLedgerDto.transactionDate,
                ledgerEntryType = cashBankLedgerDto.ledgerEntryType,
                documentNumber = cashBankLedgerDto.documentNumber,
                documentType = cashBankLedgerDto.documentType,
                referenceNumber = cashBankLedgerDto.referenceNumber,
                externalReferenceNumber = cashBankLedgerDto.externalReferenceNumber,
                particulars = cashBankLedgerDto.particulars,
                debitAmount = cashBankLedgerDto.debitAmount,
                creditAmount = cashBankLedgerDto.creditAmount,
                balance = companyBalance,
                partnerId = cashBankLedgerDto.partnerId,
                partnerDetailId = cashBankLedgerDto.partnerDetailId,
                tenant = cashBankLedgerDto.tenant,
                companyId = companyMappingObj?.companyId!!,
                paymentMode = cashBankLedgerDto.paymentMode,
                paymentType = cashBankLedgerDto.paymentType
        )

        var saveCl = cashBankLedgerRepo.save(cl)
        var docNumber = documentMasterService.getDocumentNumberForCashBank(saveCl.paymentMode,saveCl.id,companyObj.companyCode)
        saveCl.documentNumber = docNumber
        return cashBankLedgerRepo.save(saveCl)

    }

    @Transactional
    fun updateCompanyBalance(user: String, company: Company, balance: BigDecimal, paymentMode: PaymentMode): Company {
        log.debug("Updating company balance for {} with amount - {}", company.id, balance)

        company.updatedBy = user
        if(paymentMode == PaymentMode.CASH)
            company.cashBalance = balance
        else
            company.bankBalance = balance
        return companyRepo.save(company)
    }

    fun getCompanyLedgerData(paymentMode: PaymentMode,companyId:Long,from: LocalDate?, to: LocalDate?, documentType:SettlementType?, documentNumber: String?,externalRef: String?, page: Int?, size: Int?): PaginationDto {

        val size = size ?: 10
        var pageable = PageRequest.of(page ?: 0, size)

        var result = cashBankLedgerRepo.getCashBankLedgerData(paymentMode,companyId,documentNumber,externalRef,documentType,from,to,pageable)
        var companyLedger = CompanyLedgerInfoDTO(BigDecimal.ZERO, BigDecimal.ZERO,result.content)
        if(from != null && to != null) {
            var openingBal = cashBankLedgerRepo.findLastTransactionBalance(from.minusDays(1),companyId,paymentMode,PageRequest.of(0, 1))
            var closingBal = cashBankLedgerRepo.findLastTransactionBalance(to,companyId,paymentMode,PageRequest.of(0, 1))
           if(!openingBal.content.isNullOrEmpty()) {
               companyLedger.openingBalance = openingBal.content[0]?: BigDecimal.ZERO
           }
            if(!closingBal.content.isNullOrEmpty()) {
                companyLedger.closingBalance = closingBal.content[0]?: BigDecimal.ZERO
            }
        }
        return PaginationDto(result.totalElements,result.totalPages,result.hasPrevious(),result.hasNext(),companyLedger)
    }

    fun getCompanyLedgerURL(paymentMode: PaymentMode,companyId:Long,tenant: String, createdBy: String, from: LocalDate?, to: LocalDate?, documentType:SettlementType?, documentNumber: String?,externalRef: String?): CreateResultData {
        var prefix = "${paymentMode.name}LedgerList-${from}--${to}"
        var result = cashBankLedgerRepo.getCashBankLedgerDataForUrl(paymentMode,companyId,documentNumber,externalRef,documentType,from,to)
        var companyLedger = CompanyLedgerInfoDTO(BigDecimal.ZERO, BigDecimal.ZERO,result)
        if(from != null && to != null) {
            var openingBal = cashBankLedgerRepo.findLastTransactionBalance(from.minusDays(1),companyId,paymentMode,PageRequest.of(0, 1))
            var closingBal = cashBankLedgerRepo.findLastTransactionBalance(to,companyId,paymentMode,PageRequest.of(0, 1))
            if(!openingBal.content.isNullOrEmpty()) {
                companyLedger.openingBalance = openingBal.content[0]?: BigDecimal.ZERO
            }
            if(!closingBal.content.isNullOrEmpty()) {
                companyLedger.closingBalance = closingBal.content[0]?: BigDecimal.ZERO
            }
        }
        try {
            var file = vendorFileService.saveReport(tenant,VendorDataLinks(null, LocalDateTime.now(), null, "IN_PROGRESS", if(paymentMode==PaymentMode.CASH) VendorDataEnum.CASH else VendorDataEnum.BANK, null, createdBy,tenant,null))
            writeLedgerData(file.id!!, companyLedger,prefix)
            return  CreateResultData(200,"Success",file.id.toString())
        } catch (e: Exception) {
            e.printStackTrace()
           throw RequestException("Something went wrong!")
        }
    }
    @Async
    fun writeLedgerData(id: Long, cl: CompanyLedgerInfoDTO, prefix: String?) {
        val csvPrinter: CSVPrinter
        var bytes = ByteArrayOutputStream()
            csvPrinter = CSVPrinter(bytes.bufferedWriter(), CSVFormat.DEFAULT
                    .withHeader("Txn Date", "doc.Type", "Particulars", "Doc.No.", "Internal.Ref.No", "External.Ref.No","Credit","Debit","NetBalance"))
        csvPrinter.printRecord(null, null, "OPENING BALANCE", null, null ,null, null, null, cl.openingBalance)
        cl.ledgerData.forEach {
            if (it != null)
                    csvPrinter.printRecord(it.transactionDate, it.documentType, it.particulars, it.documentNumber, it.referenceNumber, it.externalReferenceNumber,it.creditAmount,it.debitAmount,it.balance)
        }
        csvPrinter.printRecord(null, null, "CLOSING BALANCE", null, null ,null, null, null, cl.closingBalance)
        csvPrinter.flush()
        val uploaded = s3FileUtilityService.uploadFile(bytes.toByteArray(), "CSV", prefix)
        vendorFileService.update(id, uploaded ?: "Failed")
    }

    fun getCompanyLedgerDownload(id:Long): CreateResultData {

        var file=vendorDataLinkRepo.findByIdOrNull(id)
        if (file != null) {
            return CreateResultData(200, "Success", file.link)
        }

        return CreateResultData(200, "Failed", null)
    }

    fun checkAndAddCashBankLedgerEntry(user: String, cashBankLedgerDto: CashBankLedgerDto): CashBankLedger {
        val cl = cashBankLedgerRepo.findDup(
            cashBankLedgerDto.documentNumber,
            cashBankLedgerDto.referenceNumber!!,
            cashBankLedgerDto.ledgerEntryType
        )
        if (cl != null) {
            return cl
        }
        return addCashBankLedgerEntry(user, cashBankLedgerDto)
    }
}
