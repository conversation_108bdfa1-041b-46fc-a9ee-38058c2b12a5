package com.pharmeasy.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.pharmeasy.data.FlatDiscountTask
import com.pharmeasy.data.FlatDiscountTaskDetail
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.*
import com.pharmeasy.repo.FlatDiscountTaskDetailRepo
import com.pharmeasy.proxy.SupplierProxy
import com.pharmeasy.repo.*
import com.pharmeasy.resource.FlatDiscountTaskResource
import com.pharmeasy.resource.FlatDiscountTaskResource.Companion
import com.pharmeasy.type.*
import com.pharmeasy.util.FlatDiscountTaskUtils
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVPrinter
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import java.io.ByteArrayOutputStream
import java.io.InputStream
import java.io.InputStreamReader
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.concurrent.Executors

@Service
class FlatDiscountService( @Value("\${app.taxPercentage.slabDiscount}") val slabCnPercentage: Int,
                           @Value("\${app.taxPercentage.offlinePromotion}") val offlinePromotion: Int,
                           @Value("\${app.taxPercentage.otcMarginLeakage}") val otcMarginLeakage: Int,
                           @Value("\${app.taxPercentage.salesIncentive}") val salesIncentive: Int) {
    companion object {
        private val log = LoggerFactory.getLogger(FlatDiscountService::class.java)
        private const val DEFAULT_REASON_CODE = 13L //13 is default for reason code as other
    }

    @Autowired
    private lateinit var flatDiscountTaskDetailRepo: FlatDiscountTaskDetailRepo

    @Autowired
    private lateinit var s3Uploader:S3Uploader

    @Autowired
    private lateinit var companyService: CompanyService

    @Autowired
    private lateinit var supplierProxy: SupplierProxy

    @Autowired
    private lateinit var flatDiscountTaskRepo: FlatDiscountTaskRepo

    @Autowired
    private lateinit var checkerService: CheckerService

    @Autowired
    private lateinit var creditNoteService: CreditNoteService

    @Autowired
    private lateinit var companyRepo: CompanyRepo

    @Autowired
    private lateinit var vaultCheckerService: CheckerService

    @Autowired
    private lateinit var bulkCnReasonCodeMappingService: BulkCnReasonCodeMappingService


    @Async
    fun validateInputPartnerFile(inputStream: InputStream, flatDiscountTask: FlatDiscountTask, companyId: Long, ds: String? = null) {
        try {
            log.debug("Discount file validation started for taskId: ${flatDiscountTask.id}")

            var uniquePdi = mutableSetOf<Long>()
            val fileInputList = mutableListOf<FlatDiscountFileDTO>()
            var sb: StringBuilder = StringBuilder()
            val invalidPartnerErrorMsg = "Invalid partner id"
            val duplicatePartnerErrorMsg = "Partner ID repeated in the file"
            var isValidRow: Boolean
            var isValidPid = true
            var noOfRecords = 0

            val csvParser = CSVFormat.DEFAULT.withFirstRecordAsHeader().parse(InputStreamReader(inputStream))

            csvParser.records.forEach { record ->
                noOfRecords++
                sb = StringBuilder()
                isValidRow = true
                try {
                    val partnerId = record.get(FlatDiscountTaskUtils.flatDiscountCsvColumns[0]).toLong()

                    if(uniquePdi.contains(partnerId)) {
                        isValidRow = false
                        sb.append(duplicatePartnerErrorMsg).append("|")
                    } else {
                        uniquePdi.add(partnerId)
                    }
                } catch (e: Exception) {
                    isValidRow = false
                    sb.append(invalidPartnerErrorMsg).append("|")
                    isValidPid = false

                }
                var amount = record.get(FlatDiscountTaskUtils.flatDiscountCsvColumns[1])
                if (!FlatDiscountTaskUtils.isValidAmount(amount)) {
                    sb.append("Invalid amount |")
                    isValidRow = false
                }
                if (!FlatDiscountTaskUtils.isIntegerAmount(amount)) {
                    sb.append("Invalid -amount should be an Integer |")
                    isValidRow = false
                }

                val hasRaisedByField = record.isMapped(FlatDiscountTaskUtils.flatDiscountCsvColumns[2])

                val raisedBy = if (hasRaisedByField)
                    record.get(FlatDiscountTaskUtils.flatDiscountCsvColumns[2])
                else
                    ""
                val hasReviewField = record.isMapped(FlatDiscountTaskUtils.flatDiscountCsvColumns[3])

                val review = if (hasReviewField)
                    record.get(FlatDiscountTaskUtils.flatDiscountCsvColumns[3])
                else
                    ""
                val reasonCode = if(hasReviewField)
                    record.get(FlatDiscountTaskUtils.flatDiscountCsvColumns[4])
                else
                    ""


                fileInputList.add(
                    FlatDiscountFileDTO(
                        record.get(FlatDiscountTaskUtils.flatDiscountCsvColumns[0]),
                        record.get(FlatDiscountTaskUtils.flatDiscountCsvColumns[1]),
                        isValidRow,
                        sb.toString(),
                        remark = review,
                        raisedBy = raisedBy,
                        reasonCode = reasonCode.toLong()
                    )
                )
            }

            if(isValidPid) {
                var supplierList: MutableMap<Int?, SupplierDetailDTO?> = preparePartnerDetailMap(uniquePdi)
                log.debug("supplierList size : ${supplierList.keys}")
                fileInputList.forEach {
                    var supplier = supplierList.get(it.pdi.toInt())
                    sb = StringBuilder()
                    log.debug("supplier check : ${ObjectMapper().writeValueAsString(supplier)}")
                    if (supplier == null) {
                        sb.append("Invalid Partner Detail Id |")
                        sb.append(it.reason)
                        isValidRow = false
                        it.isValid = false
                        it.reason = sb.toString()

                    }else{
                        it.name = supplier.name
                        it.stateId = supplier.stateId
                        it.pid = supplier.partnerId?.toLong()

                    }
                }
            }

           log.debug("File parsing and validation completed for taskId: ${flatDiscountTask.id}, companyId: $companyId. Writing validated file.")

            val validatedFileHeaders = FlatDiscountTaskUtils.flatDiscountCsvColumns.toMutableList()
            validatedFileHeaders.add("Validation")
            validatedFileHeaders.add("Failure Reason")

            val bytes = ByteArrayOutputStream()
            val csvPrinter = CSVPrinter(bytes.bufferedWriter(), CSVFormat.DEFAULT.withHeader(*validatedFileHeaders.toTypedArray()))
            var isValidFile = true

            if(noOfRecords == 0) {
                isValidFile = false
            }

            fileInputList.forEach { inputPartner ->
                isValidFile = inputPartner.isValid && isValidFile
                csvPrinter.printRecord(inputPartner.pdi, inputPartner.discountAmt, inputPartner.isValid, inputPartner.reason)
            }
            csvPrinter.flush()

            val filename = "creditnote/${LocalDate.now().format(DateTimeFormatter.ISO_DATE)}/ValidatedFlatDiscount_${System.currentTimeMillis()}.csv"

            val uri = s3Uploader.secureUploadWithKey(bytes.toByteArray(), FileType.CSV, 1000 * 60 * 60 * 24 * 7, filename)
            flatDiscountTask.draftFile = uri!!

            if(!isValidFile) {
                log.debug("Invalid file received for Flat Discount taskId: ${flatDiscountTask.id}")
                flatDiscountTask.status = SlabTaskStatus.VALIDATION_FAILED
                flatDiscountTaskRepo.save(flatDiscountTask)
            } else {
                log.debug("Valid file received for flat discount taskId: ${flatDiscountTask.id}. Flat Discount Task details will be created")
                flatDiscountTask.status = SlabTaskStatus.PENDING_APPROVAL
                populateDiscountDetails(flatDiscountTask, fileInputList, ds)
                flatDiscountTaskRepo.save(flatDiscountTask)
            }
        } catch (e: Exception) {
            log.error("Input partner file validation failed for flat discount task id: ${flatDiscountTask.id}", e)
            flatDiscountTask.status = SlabTaskStatus.TECHNICAL_FAILURE
            flatDiscountTaskRepo.save(flatDiscountTask)
        }
    }

    private fun populateDiscountDetails(discountTaskObj: FlatDiscountTask,fileList: MutableList<FlatDiscountFileDTO>,ds: String? = null){

        log.debug("populate discount detail for task : ${discountTaskObj.id}.")
        var discountList = mutableListOf<FlatDiscountTaskDetail>()
        fileList.forEach {

            log.debug("object detail : ${ObjectMapper().writeValueAsString(it)}")
            val sourceCompany = companyService.getCompanyTenantMappingObject(discountTaskObj.tenant)
            val sourceSupplier = supplierProxy.supplier(null, partnerDetailId = sourceCompany?.partnerDetailId)
            val sourceSupplierState = sourceSupplier[0]?.partnerDetailList?.get(0)?.stateId
            var taxPercentage =
                if(discountTaskObj.type == NoteTypes.PURCHASE_SLAB_DISCOUNT_CN) 12
                else if(discountTaskObj.type == NoteTypes.SALES_INCENTIVE_DISCOUNT_CN) 18
                else if (discountTaskObj.type == NoteTypes.OTC_MARGIN_LEAKAGE_DISCOUNT_CN) 18
                else if (discountTaskObj.type == NoteTypes.OFFLINE_PROMOTION_DISCOUNT_CN) 18
                else if (discountTaskObj.type == NoteTypes.FINANCE_DISCOUNT_CN) 0
                else 12
            var sgst = 0
            var cgst = 0
            var igst = 0

            if(sourceSupplierState != it.stateId) {
                igst = taxPercentage
            } else {
                sgst = taxPercentage/2
                cgst = taxPercentage/2
            }
            var taxPercentInDecimal = BigDecimal.valueOf(taxPercentage.toDouble() / 100)
            var taxableValue= it.discountAmt.toBigDecimal().divide(BigDecimal.ONE+taxPercentInDecimal, 7, RoundingMode.CEILING)
            var taxAmt = taxableValue*taxPercentInDecimal

            discountList.add(FlatDiscountTaskDetail(null, LocalDateTime.now(), LocalDateTime.now(),(it.pid?:0).toLong(),it.pdi.toLong(),
                it.name?:"",it.discountAmt.toBigDecimal(),taxableValue,taxAmt,sgst,cgst,igst,it.stateId?:0,InvoiceType.RIO, discountTaskObj,it.raisedBy,it.remark, it.reasonCode))

        }

        flatDiscountTaskDetailRepo.saveAll(discountList)


    }

    private fun preparePartnerDetailMap(partnerDetailIds: Set<Long>): MutableMap<Int?, SupplierDetailDTO?> {
        val customerDetailMap = mutableMapOf<Int?, SupplierDetailDTO?>()
        val chunkedPartners = partnerDetailIds.windowed(250, partialWindows = true)

        chunkedPartners.forEach { partners ->
            var listOfSupplier = supplierProxy.allSupplier(partners)
            log.debug("list of supplier : $listOfSupplier")
            val suppliers: Map<Int?, SupplierDetailDTO?> = listOfSupplier.associateBy { it?.id}
            customerDetailMap.putAll(suppliers)
        }
        return customerDetailMap
    }

    fun checkAndSendNotificationForApproval(discountTaskObj: FlatDiscountTask, tenant: String, userId: String, ds: String? = null) {

        var checkers = checkerService.findCheckers(tenant,null,ds)

        if (checkers.isNotEmpty()) {

            var checker = checkers.get(0)
            discountTaskObj.assignedTo = checker?.userName
            discountTaskObj.assignedToId = checker?.userId

        } else {
            throw RequestException("No checker found for this company !")
        }
    }

    fun getTaskDetails(taskId: Long,page:Int?,size:Int?): PaginationDto {
        val pageable = PageRequest.of(page ?: 0, size ?: 10)
        val res =  flatDiscountTaskDetailRepo.findByTaskId(taskId,pageable)
        val enrichedContent = res.content.map { item ->
            val reasonCategory = bulkCnReasonCodeMappingService.getBulkCNReasonCodeMapping(item.reasonCode?:DEFAULT_REASON_CODE).bulkCnCategory
            FlatDiscountTaskDetailDto(
                id = item.id!!,
                createdOn = item.createdOn,
                updatedOn = item.updatedOn,
                partnerId = item.partnerId,
                partnerDetailId = item.partnerDetailId,
                partnerName = item.partnerName,
                discountAmount = item.discountAmount,
                taxableAmount = item.taxableAmount,
                taxAmount = item.taxAmount,
                sgst = item.sgst,
                cgst = item.cgst,
                igst = item.igst,
                stateId = item.stateId,
                client = item.client,
                flatDiscountTask = item.flatDiscountTask,
                raisedBy = item.raisedBy,
                remark = item.remark,
                reason = reasonCategory
            )
        }
        return PaginationDto(res.totalElements, res.totalPages, res.hasPrevious(), res.hasNext(), enrichedContent)
    }

    fun approveTask(flatDiscountTask: FlatDiscountTask, ds: String? = null) {
        log.debug("Flat discount task with id ${flatDiscountTask.id} approved. Credit note creation process will start")
        createCreditNoteFromTask(flatDiscountTask, flatDiscountTask.createdBy, ds)
    }

    fun createCreditNoteFromTask(flatDiscountTask: FlatDiscountTask, user: String, ds: String? = null, referenceId: String?=null) {
        try {
            val discountTaskDetails = flatDiscountTaskDetailRepo.findAllByTaskId(flatDiscountTask.id!!)
            val company = companyRepo.getCompanyByTenant(flatDiscountTask.tenant)
            flatDiscountTask.status = SlabTaskStatus.COMPLETED
            flatDiscountTaskRepo.save(flatDiscountTask)
            log.debug("Discount credit note creation process success for taskId: ${flatDiscountTask.id}")
            discountTaskDetails.forEach { taskDetail ->
                creditNoteService.createCreditNoteForFlatDiscountTask(flatDiscountTask, taskDetail, user, ds, company)
            }
        } catch (e: Exception) {
            log.error("taskId: ${flatDiscountTask.id} discount cn  process failed while creating credit notes", e)
            flatDiscountTask.status = SlabTaskStatus.TECHNICAL_FAILURE
            flatDiscountTaskRepo.save(flatDiscountTask)
        }

    }

    fun rejectTask(taskId: Long) {
        log.debug("Rejecting task id: $taskId. Credit notes will not be created")
        updateTaskStatus(taskId, SlabTaskStatus.REJECTED)
    }

    private fun updateTaskStatus(taskId: Long, taskStatus: SlabTaskStatus) {
        flatDiscountTaskRepo.updateTaskStatus(taskId, taskStatus)
    }

    fun updateCreditNoteChecker(id: Long, checkerId: Long, tenant: String, userId: String, ds: String? = null): Result {
        var checker = checkerService.findCheckers(tenant,checkerId,ds)

        if(checker.isEmpty())
            throw RequestException("No active checker found with id  $checkerId , for logged in company!")

        if(checker.size>1)
            throw RequestException("More than 1 checker found $checkerId , for logged in company!")

        var flatDiscountObj = flatDiscountTaskRepo.getById(id) ?: throw RequestException("No advance Pay entry found with id $id !")

        if(flatDiscountObj.status != SlabTaskStatus.PENDING_APPROVAL) throw RequestException("Advance Payment with id $id is not in PENDING_APPROVAL state!!")

        val companyId = companyService.getCompanyTenantMappingObject(tenant)?.companyId
            ?: throw RequestException("Company not found for the given tenant")

        val checkerUserIdList = vaultCheckerService.getCheckerByCompanyId(companyId).mapNotNull { it?.userId }.toMutableList()

        if(userId != flatDiscountObj.createdBy && userId !in checkerUserIdList){
            throw RequestException("not a valid user cannot update")
        }

        flatDiscountObj.assignedToId = checker[0]?.userId
        flatDiscountObj.assignedTo = checker[0]?.userName

        flatDiscountTaskRepo.save(flatDiscountObj)

        return success
    }

    fun uploadCreditNoteFile(slabFileDto: SlabFileDto, tenant: String, user: String, userId: String, cnType: NoteTypes, ds: String? = null): PaginationDto {
        log.debug("Bulk discount CN file received. Filename: ${slabFileDto.objectKey}, user: $user ")
        val company = companyService.getCompanyTenantMappingObject(ds?:tenant)
            ?: throw RequestException("No company found for tenant $tenant , ds $ds")
        if(slabFileDto.uuid == null){
            throw RequestException("Unique id is null.")
        }
        val validateExistingTask = flatDiscountTaskRepo.findByUUID(slabFileDto.uuid)
        if(validateExistingTask != null){
            throw RequestException("Task already started.")
        }
        if(cnType == NoteTypes.PURCHASE_SLAB_DISCOUNT_CN ||cnType == NoteTypes.OFFLINE_PROMOTION_DISCOUNT_CN || cnType == NoteTypes.OTC_MARGIN_LEAKAGE_DISCOUNT_CN || cnType == NoteTypes.SALES_INCENTIVE_DISCOUNT_CN || cnType == NoteTypes.FINANCE_DISCOUNT_CN) {
            val flatDiscount = FlatDiscountTask(
                startDate = slabFileDto.startDate,
                endDate = slabFileDto.endDate,
                createdBy = user,
                partnerFile = slabFileDto.s3Url,
                tenant = company.tenant,
                status = SlabTaskStatus.VALIDATION_IN_PROGRESS,
                company = company,
                createdById = userId,
                type = cnType,
                uuid = slabFileDto.uuid
            )

            checkAndSendNotificationForApproval(flatDiscount, tenant, userId, ds)
            var taskObj = flatDiscountTaskRepo.save(flatDiscount)

            val executorService = Executors.newFixedThreadPool(1)
            executorService.execute {
                validateInputPartnerFile(
                    s3Uploader.read(slabFileDto.objectKey),
                    taskObj,
                    company.companyId!!,
                    ds
                )
            }
            executorService.shutdown()

            return getTasks(tenant = company.tenant)
        }else{
            throw RequestException("CN type $cnType not allowed!")
        }
    }

    fun getTasks(fromDateTime: LocalDateTime? = null, toDateTime: LocalDateTime? = null,
                         status: SlabTaskStatus? = null,
                         ds: String? = null, tenant: String,
                         page: Int? = null, size: Int? = null): PaginationDto {
        val company = companyService.getCompanyTenantMappingObject(ds?:tenant) ?: throw RequestException("No company mapping found for tenant $tenant")
        val pageable = PageRequest.of( page ?: 0,  size ?: 10, Sort.by(Sort.Direction.DESC, "id"))
        val discountTask = flatDiscountTaskRepo.findAllTasks(fromDateTime, toDateTime, status, company.id, pageable)
        return PaginationDto(discountTask.totalElements, discountTask.totalPages, discountTask.hasPrevious(),
            discountTask.hasNext(), discountTask.content)
    }

    fun cancelTask(taskId: Long, tenant: String): PaginationDto{
    val flatDiscountTask = flatDiscountTaskRepo.findById(taskId)
        val nonCancellableTasks = listOf(SlabTaskStatus.CANCELLED, SlabTaskStatus.REJECTED, SlabTaskStatus.APPROVED)
        if(nonCancellableTasks.contains(flatDiscountTask.get().status)){
            throw RequestException("Flat discount task with id $taskId is not in ${flatDiscountTask.get().status} state")
        }
        flatDiscountTaskRepo.updateTaskStatus(taskId,SlabTaskStatus.CANCELLED)
        return getTasks(tenant = tenant)
    }

    fun approveTaskByTaskId(taskId: Long, user: String, userId: String, ds: String? = null) {
        val flatTaskObj = flatDiscountTaskRepo.findById(taskId)
        if(!flatTaskObj.isPresent) {
            throw RequestException("flat task with id $taskId does not exist")
        }

        var flatTask = flatTaskObj.get()

        if (userId == flatTask.createdById) {
            throw RequestException("Cannot approve the request as created by you")
        }

        if(flatTask.status != SlabTaskStatus.PENDING_APPROVAL){
            throw RequestException("Flat Task status not eligible for approval, current status is ${flatTask.status}")
        }
        var checkers = checkerService.findCheckers(flatTask.tenant,null,null)
        if (checkers.isEmpty()) throw RequestException("No checker found for this company !")
        var checker = checkerService.compareChecker(checkers,flatTask.assignedToId?:"")
        if (!flatTask.assignedToId.equals(userId) || checker == null) throw RequestException("You are not authorized to change task status !")

        flatTask.approvedBy = user
        flatTask.approvedOn = LocalDateTime.now()
        flatTask.status = SlabTaskStatus.APPROVED
        flatTask = flatDiscountTaskRepo.save(flatTask)

        val executorService = Executors.newFixedThreadPool(1)
        executorService.execute { approveTask(flatTask, ds) }
        executorService.shutdown()
    }

    fun rejectTask(taskId: Long, userId: String){
        val flatTaskOpt = flatDiscountTaskRepo.findById(taskId)
        if(!flatTaskOpt.isPresent) {
            throw RequestException("flat task with id $taskId does not exist")
        }
        var flatTask = flatTaskOpt.get()

        if (userId == flatTask.createdById) {
            throw RequestException("Cannot approve the request as created by you")
        }

        if(listOf(SlabTaskStatus.COMPLETED, SlabTaskStatus.CANCELLED, SlabTaskStatus.APPROVED).contains(flatTask.status)){
            throw RequestException("Flat Task status not eligible for rejection, current status is ${flatTask.status}")
        }
        rejectTask(taskId)
    }
  }