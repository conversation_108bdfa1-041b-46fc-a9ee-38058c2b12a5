package com.pharmeasy.service

import com.pharmeasy.data.*
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.*
import com.pharmeasy.proxy.SupplierProxy
import com.pharmeasy.proxy.WarehouseProxy
import com.pharmeasy.repo.DeliveryChallanLogEntryRepo
import com.pharmeasy.repo.DeliveryChallanMappingRepo
import com.pharmeasy.repo.DeliveryChallanRepo
import com.pharmeasy.repo.DeliveryChallanTaxLogRepo
import com.pharmeasy.repo.read.DeliveryChallanLogEntryReadRepo
import com.pharmeasy.repo.read.DeliveryChallanMappingReadRepo
import com.pharmeasy.repo.read.DeliveryChallanReadRepo
import com.pharmeasy.repo.read.DeliveryChallanTaxLogReadRepo
import com.pharmeasy.stream.DcCallBackEventPusher
import com.pharmeasy.type.*
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.math.abs

@Service
class DeliveryChallanService(
    private val warehouseProxy: WarehouseProxy
) {

    @Value("\${app.aws.s3.sdcn.deliveryChallanPrefix}")
    private lateinit var pathName: String

    @Autowired
    private lateinit var deliveryChallanRepo: DeliveryChallanRepo

    @Autowired
    private lateinit var deliveryChallanReadRepo: DeliveryChallanReadRepo

    @Autowired
    private lateinit var supplierProxy: SupplierProxy

    @Autowired
    private lateinit var fileUploadService: FileUploadService

    @Autowired
    private lateinit var companyService: CompanyService

    @Autowired
    private lateinit var deliveryChallanMappingReadRepo: DeliveryChallanMappingReadRepo

    @Autowired
    private lateinit var deliveryChallanLogEntryReadRepo: DeliveryChallanLogEntryReadRepo

    @Autowired
    private lateinit var deliveryChallanTaxLogReadRepo: DeliveryChallanTaxLogReadRepo

    @Autowired
    private lateinit var deliveryChallanLogEntryRepo: DeliveryChallanLogEntryRepo

    @Autowired
    private lateinit var deliveryChallanMappingRepo: DeliveryChallanMappingRepo

    @Autowired
    private lateinit var deliveryChallanTaxLogRepo: DeliveryChallanTaxLogRepo

    @Autowired
    private lateinit var partnerService: PartnerService

    @Autowired
    private lateinit var dcCallBackEventPusher: DcCallBackEventPusher

    companion object {
        private val log = LoggerFactory.getLogger(DeliveryChallanService::class.java)
    }

    @Transactional
    fun saveDeliveryChallan(deliveryChallanDto: DeliveryChallanDto) {

        var data = deliveryChallanReadRepo.getByDcNumberAndTenantAndPartnerDetailId(
            deliveryChallanDto.dcNumber,
            deliveryChallanDto.tenant,
            deliveryChallanDto.pdi
        )
        if (data!!.isNotEmpty()) {
            return
        }
        var supplier = supplierProxy.partnerGenericDto(mutableListOf(deliveryChallanDto.pdi))?:
            throw RequestException("Partner not found for partner detail id: ${deliveryChallanDto.pdi}")

        var partnerId: Long = 0
        var partnerName = ""

            partnerId = supplier?.data?.get(0)?.procurement!!.partnerId!!.toLong()
            partnerName = supplier?.data?.get(0)?.partnerData?.name ?: ""


        var result = deliveryChallanRepo.save(
            DeliveryChallan(
                id = null,
                createdOn = LocalDateTime.now(),
                updatedOn = LocalDateTime.now(),
                createdBy = "SYSTEM",
                updatedBy = null,
                dcNumber = deliveryChallanDto.dcNumber,
                amount = deliveryChallanDto.dcValue.toDouble(),
                pendingAmount = deliveryChallanDto.dcValue.toDouble(),
                status = DeliveryChallanStatusType.OPEN,
                tenant = deliveryChallanDto.tenant,
                partnerDetailId = deliveryChallanDto.pdi,
                partnerId = partnerId,
                partnerName = partnerName,
                documentDate = deliveryChallanDto.documentDate
            )
        )

        var autoSettleDcObjs = mutableListOf<ItemDto>()
        if(!deliveryChallanDto.items.isNullOrEmpty()) {

            deliveryChallanDto.items.forEach {

                if (it!!.debitNoteSettlementType == DcSettlementType.AUTO) {
                    autoSettleDcObjs.add(it)
                } else if (it!!.debitNoteSettlementType == DcSettlementType.PM_FLAG) {
                    var pmFlag: Map<String?, PurchaseReturnConditionsDTO>? =
                        supplier.data!![0]?.procurement?.returnConditions?.associateBy { it.itemCondition }
                    if (pmFlag != null) {
                        var flag = pmFlag.get(it.itemType.name)?.autoDebitVoucher
                        if (flag == true) {
                            autoSettleDcObjs.add(it)
                        }
                    }
                }
            }
        }
        if(autoSettleDcObjs.isNotEmpty()) {
            var taxData = mutableListOf<TaxData>()
            var setOffAmt = 0.0
            var itemGstGrp: Map<Double, List<ItemDto>> = autoSettleDcObjs.groupBy { it.gst }
            itemGstGrp.forEach { (t, u) ->
                var totalAmt = u.sumByDouble { it.itemValue }
                setOffAmt += totalAmt
                var gst = t
                val percentage = gst / 100.0
                var taxValue =  percentage * totalAmt
                var taxableVal = totalAmt-taxValue
                taxData.add(TaxData("hsn", gst.toInt(),taxableVal, taxValue, totalAmt))

            }

           var dcObj =  DeliveryChallanSetOffDto(
                DeliveryChallanLogEntryStatusType.AUTO_SETOFF, setOffAmt, setOffAmt, "AUTO_SETOFF", LocalDate.now(),
                null, null, null, result.id!!, taxData, mutableListOf(), "auto settle record"
            )

            setOff(result, dcObj, DeliveryChallanLogEntryStatusType.AUTO_SETOFF, "SYSTEM", true)

        }


        if(!deliveryChallanDto.settledEvents.isNullOrEmpty()){
            deliveryChallanDto.settledEvents.forEach {

                var setOfdcObj = it?.let { it1 ->
                    var taxData = mutableListOf<TaxData>()
                    it1.taxRates.forEach { taxData.add(TaxData(it.hsn, it.tax.toInt(), it.taxableValue, it.taxValue, it.totalValue)) }
                    DeliveryChallanSetOffDto(
                        it1.type,it1.setoffValue,it1.setoffValue,it1.referenceNumberSetOff,it1.referenceDateSetOff,
                        null,null,null,result.id!!,taxData, mutableListOf(),"partial settle record"
                    )
                }
                if (setOfdcObj != null) {
                    setOff(result,setOfdcObj,setOfdcObj.type,"SYSTEM",false)
                }
            }
        }
    }

    fun getDeliveryChallanBySupplierId(id: Long, tenant: String, page: Int?, size: Int?, dcNumber: String?, from: LocalDate?, to: LocalDate?, pid: Long?, partnerName: String?, statusType: DeliveryChallanStatusType?): PaginationDto {
        val page = page ?: 0
        val size = size ?: 10
        var pdi = id
        var pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC,"id"))
        val fromDate = from?.atStartOfDay()
        val toDate = to?.atTime(23, 59, 59)
        var tenants = companyService.findTenants(tenant)
        var status: MutableList<DeliveryChallanStatusType> = mutableListOf()
        if (statusType == DeliveryChallanStatusType.OPEN){
            status.add(DeliveryChallanStatusType.OPEN)
        }else if(statusType == DeliveryChallanStatusType.CLOSED){
            status.add(DeliveryChallanStatusType.CLOSED)
        }else if(statusType == DeliveryChallanStatusType.PARTIAL_CLOSED ){
            status.add(DeliveryChallanStatusType.PARTIAL_CLOSED)
        }else{
            status.add(DeliveryChallanStatusType.PARTIAL_CLOSED)
            status.add(DeliveryChallanStatusType.OPEN)
            status.add(DeliveryChallanStatusType.CLOSED)
        }
        var res = deliveryChallanReadRepo.getSupplierDeliveryChallanData(pdi, tenants, fromDate, toDate, dcNumber, partnerName, pid, status, pageable)

        return PaginationDto(res.totalElements, res.totalPages, res.hasPrevious(), res.hasNext(), res.content)
    }

    fun getUploadURL(): UrlDataDTO {
        return fileUploadService.getUploadURL(pathName)
    }

    fun getDashboardData(
        pdi: Long?,
        tenant: String,
        page: Int?,
        size: Int?,
        from: LocalDate?,
        to: LocalDate?,
        statusType: DeliveryChallanStatusType?
    ): PaginationDto {
        val page = page ?: 0
        val size = size ?: 10
        var pageable = PageRequest.of(page, size)
        val fromDate = from?.atStartOfDay()
        val toDate = to?.atTime(23, 59, 59)
        val tenants = companyService.findTenants(tenant)
        var res = deliveryChallanReadRepo.getAllVendorDCData(
            fromDate,
            toDate,
            pdi,
            tenants,
            status = statusType,
            pageable = pageable
        )
        return PaginationDto(res.totalElements, res.totalPages, res.hasPrevious(), res.hasNext(), res.content)

    }

    fun uploadFiles(fileUploadMetaDto: MutableList<FileUploadMetaDto>, createdBy: String) {
        fileUploadService.saveFileMeta(fileUploadMetaDto, createdBy, FileMetaType.DELIVERY_CHALLAN)
    }

    @Transactional(readOnly = true)
    fun getDeliveryChallanHistory(deliveryChallanId: Long,requireS3Url: Boolean = true): SetoffHistory {

        var deliveryChallanData = deliveryChallanReadRepo.getOne(deliveryChallanId)
        var historyData: MutableList<SetoffHistoryItem> = mutableListOf()
        var deliveryChallanMappingData = deliveryChallanMappingReadRepo.getByDeliveryChallanId(deliveryChallanId)
        deliveryChallanMappingData.forEach {
            var deliveryChallanLogEntryData = deliveryChallanLogEntryReadRepo.getOne(it.deliveryChallanLogId)
            var deliveryChallanTaxData =
                deliveryChallanTaxLogReadRepo.getByDeliveryChallanLogId(it.deliveryChallanLogId)
            var s3Url = if(requireS3Url){
                fileUploadService.getDownloadUrl(it.deliveryChallanLogId, FileMetaType.DELIVERY_CHALLAN)
            }
            else{
                FileMetaUrlDto(null,mutableListOf())
            }
            historyData.add(
                SetoffHistoryItem(
                    setOffType = deliveryChallanLogEntryData.status,
                    referenceNumber = deliveryChallanLogEntryData.referenceNumber,
                    setoffAmount = deliveryChallanLogEntryData.amount,
                    dcAmount = deliveryChallanData.amount,
                    createdBy = deliveryChallanLogEntryData.createdBy,
                    createdOn = deliveryChallanLogEntryData.createdOn,
                    s3Link = s3Url.url,
                    dnValue = deliveryChallanData.amount,
                    refValue = deliveryChallanLogEntryData.amount,
                    refNumber = deliveryChallanLogEntryData.referenceNumber,
                    refDate = deliveryChallanLogEntryData.referenceDate,
                    taxLevelDetail = deliveryChallanTaxData,
                    refDate2 = deliveryChallanLogEntryData.referenceDate2,
                    refNumber2 = deliveryChallanLogEntryData.referenceNumber2,
                    refValue2 = deliveryChallanLogEntryData.referenceAmount2
                )
            )

        }

        var data = SetoffHistory(
            partnerName = deliveryChallanData.partnerName,
            dcAmount = deliveryChallanData.amount,
            setoffAmount = deliveryChallanData.amount?.minus(deliveryChallanData.pendingAmount!!),
            data = historyData
        )

        return data
    }

    @Transactional(readOnly = true)
    fun getDeliveryChallanHistoryByDc(dcNumber: String, tenant: String, partnerDetailId: Long): SetoffHistory {
        val deliveryChallanId: Long =
            deliveryChallanReadRepo.getByDcNumberAndTenantAndPartnerDetailId(dcNumber, tenant, partnerDetailId)
                ?.firstOrNull()?.id ?: throw RequestException("No Deliver Challan found against $dcNumber in Vault")
        val setOfHistoryData = getDeliveryChallanHistory(deliveryChallanId, false)

        val userIdList = setOfHistoryData.data.mapNotNull { it.createdBy }.distinct()
        val userMap = getUserNames(userIdList)

        setOfHistoryData.data.forEach {
            it.createdBy = userMap[it.createdBy]?.firstOrNull()?.displayName ?: it.createdBy
        }
        return setOfHistoryData
    }


    fun getUserNames(userIds : List<String>) :  Map<String, List<User>>{
        return warehouseProxy.getUserByUserIds(userIds).groupBy { it.id }
    }


    fun getDocumentsByDc(dcNumber: String, tenant: String, partnerDetailId: Long): MutableList<FileMetaUrlDto>{
        val deliveryChallanId = deliveryChallanReadRepo.getByDcNumberAndTenantAndPartnerDetailId(dcNumber,tenant,partnerDetailId)?.firstOrNull()?.id ?: return mutableListOf()
        var deliveryChallanMappingData = deliveryChallanMappingReadRepo.getByDeliveryChallanId(deliveryChallanId)
        val urlList: MutableList<FileMetaUrlDto> = mutableListOf()
        deliveryChallanMappingData.forEach {
            var s3Url = fileUploadService.getDownloadUrl(it.deliveryChallanLogId, FileMetaType.DELIVERY_CHALLAN)
            urlList.add(s3Url)
        }
        return urlList
    }

    @Transactional
    fun setOffDC(
        setOffDto: DeliveryChallanSetOffDto,
        user: String
    ) : Result{
        var type= setOffDto.type
        var getDcObj = deliveryChallanReadRepo.getById(setOffDto.dcId) ?: throw RequestException("Invalid DC ids!")
        setOff(getDcObj, setOffDto, type, user)
        return success


    }

    fun setOff(
        getDcObj: DeliveryChallan,
        setOffDto: DeliveryChallanSetOffDto,
        type: DeliveryChallanLogEntryStatusType,
        user: String,
        callBack:Boolean= true
    ) {
        if (getDcObj.status == DeliveryChallanStatusType.CLOSED)
            throw RequestException("DC status is already in closed state!")
        if(setOffDto.type == DeliveryChallanLogEntryStatusType.CN_SETOFF && callBack) {
            val existingDeliveryChallanLogEntries = deliveryChallanLogEntryRepo.getByRefNum(setOffDto.refNum!!, getDcObj.partnerDetailId)
            if (existingDeliveryChallanLogEntries!!.isNotEmpty()){
                compareCreditNoteUsedAmountAndTxnAmount(existingDeliveryChallanLogEntries, setOffDto.setOffAmt, setOffDto.refAmt!!, setOffDto.refDate!!)
            }
            val dcCreatedOn = getDcObj.createdOn!!.toLocalDate()
                if (setOffDto.refDate!!.isBefore(dcCreatedOn))
                    throw RequestException("Setoff date before delivery challan creation date")
        }

        if (setOffDto.setOffAmt > (getDcObj.pendingAmount ?: 0.0) || (setOffDto.setOffAmt < 1 && setOffDto.type.name != DeliveryChallanLogEntryStatusType.WRITE_OFF.name ))
            throw RequestException("DC remaining amount is less then set off amount! ${getDcObj.pendingAmount} < ${setOffDto.setOffAmt} || set off amt less then 1")

        var dcMapping = mutableListOf<DeliveryChallanMapping>()
        var taxLogs = mutableListOf<DeliveryChallanTaxLog>()

        var logId: Long
        var dcLogEntry = deliveryChallanLogEntryRepo.save(
                DeliveryChallanLogEntry(
                    null,
                    LocalDateTime.now(),
                    user,
                    setOffDto.setOffAmt,
                    setOffDto.refNum,
                    setOffDto.refDate,
                    setOffDto.refAmt,
                    setOffDto.refNum2,
                    setOffDto.refDate2,
                    setOffDto.refAmt2,
                    type,
                    setOffDto.remark,
                    setOffDto.roundOffAmt
                )
            )
            logId = dcLogEntry.id!!
            var remainingDcAmt = getDcObj.pendingAmount!!.minus(setOffDto!!.setOffAmt).toBigDecimal().setScale(2,RoundingMode.HALF_UP).toDouble()
            if (remainingDcAmt <= 0) {
                getDcObj.status = DeliveryChallanStatusType.CLOSED
                getDcObj.pendingAmount = 0.0
                dcMapping.add(
                    DeliveryChallanMapping(
                        null, LocalDateTime.now(), getDcObj.id!!, dcLogEntry.id!!,
                        setOffDto.setOffAmt, DeliveryChallanStatusType.CLOSED
                    )
                )
            } else if (remainingDcAmt > 0 && remainingDcAmt < getDcObj.amount!!) {
                getDcObj.status = DeliveryChallanStatusType.PARTIAL_CLOSED
                getDcObj.pendingAmount = remainingDcAmt
                dcMapping.add(
                    DeliveryChallanMapping(
                        null, LocalDateTime.now(), getDcObj.id!!, dcLogEntry.id!!,
                        setOffDto.setOffAmt, DeliveryChallanStatusType.PARTIAL_CLOSED
                    )
                )
            }

            setOffDto.taxList?.forEach {
                taxLogs.add(
                    DeliveryChallanTaxLog(
                        null,
                        dcLogEntry.id!!,
                        LocalDateTime.now(),
                        it.taxPercentage?.toDouble(),
                        it.taxAmt,
                        it.taxableAmt,
                        it.grossAmt,
                        it.hsn
                    )
                )
            }
            deliveryChallanTaxLogRepo.saveAll(taxLogs)
            deliveryChallanMappingRepo.saveAll(dcMapping)
            deliveryChallanRepo.save(getDcObj)
            if (type == DeliveryChallanLogEntryStatusType.CN_SETOFF || type == DeliveryChallanLogEntryStatusType.INVOICE_SETOFF)
                addLedgerForDC(setOffDto, type, getDcObj, user, dcLogEntry.id ?: 0L)
            uploadSetOffFiles(setOffDto, dcLogEntry, user)

        if(callBack) {
            log.debug("sending call back event for setoff dc-number ${getDcObj.dcNumber}")
            dcCallBackEventPusher.createDcCallBackEvent(
                DCCallBackDto(
                    logId, getDcObj.tenant!!, getDcObj.dcNumber!!, setOffDto.setOffAmt, type, getDcObj.amount!!,
                    LocalDateTime.now(), setOffDto.refNum, setOffDto.refDate,setOffDto.refAmt,setOffDto.refNum2,setOffDto.refDate2,setOffDto.refAmt2, taxLogs,setOffDto.remark, setOffDto.roundOffAmt
                )
            )
        }
    }

    private fun uploadSetOffFiles(
        setOffDto: DeliveryChallanSetOffDto,
        dcLogEntry: DeliveryChallanLogEntry,
        user: String
    ) {
        if (setOffDto.fileList != null && setOffDto.fileList!!.size > 0) {
            setOffDto.fileList?.forEach {
                it?.referenceId = dcLogEntry.id
            }
            fileUploadService.saveFileMeta(setOffDto.fileList!!, user, FileMetaType.DELIVERY_CHALLAN)
        }
    }

    fun addLedgerForDC(setOffDto: DeliveryChallanSetOffDto, type: DeliveryChallanLogEntryStatusType,dcObj: DeliveryChallan,user: String,id:Long) {

        var vendorLedger = VendorLedgerDto(
            transactionDate = LocalDate.now(),
            vendorId = dcObj?.partnerId!!,
            vendorName = dcObj?.partnerName!!,
            ledgerEntryType = LedgerEntryType.DEBIT,
            documentType = if(type == DeliveryChallanLogEntryStatusType.CN_SETOFF) DocumentType.CN_SETOFF else DocumentType.INVOICE_SETOFF,
            documentNumber = dcObj.dcNumber!!,
            referenceNumber = setOffDto.refNum+"_"+id!!,
            externalReferenceNumber = null,
            particulars = "SET-OFF",
            debitAmount = setOffDto.setOffAmt.toBigDecimal(),
            creditAmount = BigDecimal.ZERO,
            partnerDetailId = dcObj.partnerDetailId,
            partnerId = dcObj.partnerId,
            tenant = dcObj.tenant!!,
            type = PartnerType.VENDOR,
            client = InvoiceType.VENDOR
        )
            partnerService.addVendorLedgerEntry(user, vendorLedger)
    }

    fun compareCreditNoteUsedAmountAndTxnAmount(dcLogEntries: List<DeliveryChallanLogEntry>, txnAmount: Double, inputCnAmount: Double, inputCnDate: LocalDate){
        val creditNoteAmount = dcLogEntries[0].referenceAmount
        var creditNoteAmountUsed = 0.00
        dcLogEntries.forEach {
            creditNoteAmountUsed += it.amount!!
        }
        if(creditNoteAmount != inputCnAmount){
            throw RequestException("Amount mismatch for credit note number ${dcLogEntries[0].referenceNumber}. Actual CN Amount $creditNoteAmount, Transaction CN Amount $inputCnAmount")
        }
        if(dcLogEntries[0].referenceDate != inputCnDate){
            throw RequestException("Date mismatch for credit note number ${dcLogEntries[0].referenceNumber}. Actual CN Date ${dcLogEntries[0].referenceDate}, Transaction CN Date $inputCnDate")
        }
        if(creditNoteAmountUsed+txnAmount > creditNoteAmount){
            throw RequestException("Set off amount $txnAmount is greater than remaining credit note amount ${creditNoteAmount - creditNoteAmountUsed}")
        }
    }

    @Transactional
    fun retryDcCallback(dcNumbers: List<String>) {

        val getDcObj = deliveryChallanReadRepo.findByDcNumbers(dcNumbers)?: throw RequestException("Invalid DC numbers!")
        getDcObj.forEach { dc ->

            var dcMapping = deliveryChallanMappingRepo.getDcMapping(dc.id!!)
            dcMapping.forEach {
                val logId = it.deliveryChallanLogId
                val setOffDto = deliveryChallanLogEntryRepo.getOne(logId)
                val taxLogs = deliveryChallanTaxLogRepo.getByDeliveryChallanLogId(logId)

                    dcCallBackEventPusher.createDcCallBackEvent(
                        DCCallBackDto(
                            logId,
                            dc.tenant!!,
                            dc.dcNumber!!,
                            setOffDto.amount!!,
                            setOffDto.status!!,
                            dc.amount!!,
                            setOffDto.createdOn,
                            setOffDto.referenceNumber,
                            setOffDto.referenceDate,
                            setOffDto.referenceAmount,
                            setOffDto.referenceNumber2,
                            setOffDto.referenceDate2,
                            setOffDto.referenceAmount2,
                            taxLogs,
                            setOffDto.remark,
                            setOffDto.roundOffAmount
                        )
                    )
            }
        }
    }
}
