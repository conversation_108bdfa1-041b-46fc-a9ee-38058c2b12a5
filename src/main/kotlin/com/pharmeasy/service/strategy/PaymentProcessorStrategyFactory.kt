package com.pharmeasy.service.strategy

import com.pharmeasy.service.CashPaymentProcessor
import com.pharmeasy.service.ChequePaymentProcessor
import com.pharmeasy.service.DefaultPaymentProcessor
import com.pharmeasy.service.DirectPaymentProcessor
import com.pharmeasy.service.abstracts.NeftPaymentProcessor
import com.pharmeasy.service.abstracts.PaymentProcessor
import com.pharmeasy.type.PaymentType
import org.springframework.stereotype.Service

@Service
class PaymentProcessorStrategyFactory(
    private val chequePaymentProcessor: ChequePaymentProcessor,
    private val cashPaymentStrategyImpl: CashPaymentProcessor,
    private val neftPaymentStrategyImpl: NeftPaymentProcessor,
    private val directPaymentStrategyImpl: DirectPaymentProcessor,
    private val defaultPaymentStrategyImpl: DefaultPaymentProcessor
) {
    fun getInstance(paymentType: PaymentType): PaymentProcessor {
        return when (paymentType) {
            PaymentType.CHEQUE -> chequePaymentProcessor
            PaymentType.CASH -> cashPaymentStrategyImpl
            PaymentType.NEFT -> neftPaymentStrategyImpl
            PaymentType.DIRECT_PAYMENT -> directPaymentStrategyImpl
            else -> defaultPaymentStrategyImpl
        }
    }
}
