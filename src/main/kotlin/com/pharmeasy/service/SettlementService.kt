package com.pharmeasy.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.pharmeasy.data.*
import com.pharmeasy.exception.BookkeeperErrors
import com.pharmeasy.exception.DetailedRequestException
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.*
import com.pharmeasy.model.ops.SettlementGroupNumber
import com.pharmeasy.proxy.OrderFulfilmentProxy
import com.pharmeasy.proxy.SupplierProxy
import com.pharmeasy.repo.*
import com.pharmeasy.repo.advancepayment.AdvancePaymentRepo
import com.pharmeasy.repo.advancepayment.AdvanceSettlementMappingRepo
import com.pharmeasy.repo.ops.PaymentExcessRepository
import com.pharmeasy.service.ops.PaymentService
import com.pharmeasy.specification.BkInvoiceSpecification
import com.pharmeasy.specification.SupplierCreditNoteSpecification
import com.pharmeasy.repo.read.SettlementReadRepo
import com.pharmeasy.service.abstracts.SettleableProcessorFactory
import com.pharmeasy.service.ops.PaymentExcessService
import com.pharmeasy.stream.*
import com.pharmeasy.type.*
import com.pharmeasy.util.DateUtils
import com.pharmeasy.util.EventPublisherUtil
import com.pharmeasy.util.UUIDUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.data.domain.PageRequest
import org.springframework.data.repository.findByIdOrNull
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit
import java.util.stream.Collectors
import kotlin.math.abs

@Service
class SettlementService(
    @Value("\${app.maxCashLimit}") val maxCashLimit: Double,
    @Value("\${app.outstanding_round_off_value}") val roundOffLimit: BigDecimal,
    private val settleableProcessorFactory: SettleableProcessorFactory
) {
    companion object {
        private val log = LoggerFactory.getLogger(SettlementService::class.java)
        private val maxDiffAmount = 1
    }

    @Autowired
    private lateinit var settlementRepo: SettlementRepo

    @Autowired
    private lateinit var creditNoteService: CreditNoteService

   @Autowired
    private lateinit var retailerDebitNoteRepo: RetailerDebitNoteRepo

    @Autowired
    private lateinit var bkInvoiceRepo: BkInvoiceRepo

    @Autowired
    private lateinit var invoiceSettlementRepo: InvoiceSettlementRepo

    @Autowired
    private lateinit var creditNoteSettlementRepo: CreditNoteSettlementRepo

    @Autowired
    private lateinit var retailerDebitNoteSettlementMappingRepo: RetailerDebitNoteSettlementMappingRepo

    @Autowired
    private lateinit var supplierProxy: SupplierProxy

    @Autowired
    private lateinit var partnerService: PartnerService

    @Autowired
    private lateinit var companyTenantMappingRepo: CompanyTenantMappingRepo

    @Autowired
    private lateinit var creditNoteRepo: CreditNoteRepo

    @Autowired
    private lateinit var documentMasterService: DocumentMasterService

    @Autowired
    private lateinit var companyRepo: CompanyRepo

    @Autowired
    private lateinit var vendorRepo: VendorRepo

    @Autowired
    private lateinit var bkBankRepo: BkBankRepo

    @Autowired
    private lateinit var chequeHandleService: ChequeHandlingService

    @Autowired
    private lateinit var advanceSettlementMappingRepo: AdvanceSettlementMappingRepo

    @Autowired
    private lateinit var advancePaymentRepo: AdvancePaymentRepo

    @Autowired
    private lateinit var chargesRepo: ChargesRepo

    @Autowired
    private lateinit var chargeSettlementRepo: ChargeSettlementRepo

    @Autowired
    private lateinit var companyService: CompanyService

    @Autowired
    private lateinit var caseBankLedgerService: CashBankLedgerService

    @Autowired
    private lateinit var settlementDebitNoteDistributionMappingRepo: SettlementDebitNoteDistributionMappingRepo

    @Autowired
    private lateinit var paymentService: PaymentService

    @Autowired
    private lateinit var tradeCreditPaymentService: TradeCreditPaymentService

    @Autowired
    private lateinit var receiptRepo: ReceiptRepo

    @Autowired
    private lateinit var invoiceSettlementUpdateHandler: InvoiceSettlementUpdateHandler

    @Autowired
    private lateinit var orderFulfilmentProxy: OrderFulfilmentProxy

    @Autowired
    private lateinit var settlementReadRepo: SettlementReadRepo

    @Autowired
    private lateinit var retailerCreditControlRepo: RetailerCreditControlRepo

    @Autowired
    private lateinit var checkerService: CheckerService

    @Autowired
    private lateinit var adjustmentService: AdjustmentService

    @Autowired
    private lateinit var receiptService: ReceiptService

    @Autowired
    private lateinit var settlementDistributionMapping:SettlementInvoiceDistributionMappingRepo

    @Autowired
    private lateinit var paymentExcessRepository: PaymentExcessRepository

    @Autowired
    private lateinit var rioDraftInvoiceRepo: RioDraftInvoiceRepo

    @Autowired
    private lateinit var eventPublisherUtil: EventPublisherUtil

    @Autowired
    private lateinit var paymentExcessService: PaymentExcessService

    @Autowired
    private lateinit var invoiceService: InvoiceService

    @Autowired
    private lateinit var retailerDebitNoteService: RetailerDebitNoteService

    @Autowired
    private lateinit var blockedVendorSettlementPusher: BlockedVendorSettlementPusher

    @Autowired
    private lateinit var advancePaymentService: AdvancePaymentService


    fun getSettlementRecords(page: Int?, size: Int?, supplierId: Long?, invoiceId: Long?, creditNoteId: Long?, settlementNumber: String?,
                             from: LocalDate?, to: LocalDate?, tenant: String?, customerType: Boolean, ds: String? = null,pdi: Long?,receiptNumber: String?): PageableSettlement {
        var custType: PartnerType = PartnerType.VENDOR
        if (customerType) custType = PartnerType.CUSTOMER
        val tenants = companyService.findTenants(ds?:tenant!!)
        if (tenants.isEmpty()) throw RequestException("tenant $tenant is not mapped to any company")

        val page = PageRequest.of(page ?: 0, size ?: 10)
        val toDate: LocalDateTime? = if (to != null) {
            DateUtils.getUTCDateTime(to.atTime(23, 59, 59))
        } else null
        val fromDate: LocalDateTime? = if (from != null) {
            DateUtils.getUTCDateTime(from.atTime(0, 0, 0))
        } else null

        val res = if(receiptNumber != null){
            settlementRepo.getSettlementFromReceipt(receiptNumber, page)
        }else if (invoiceId == null && creditNoteId == null) {
            settlementRepo.getSettlementRecords(supplierId, settlementNumber, fromDate, toDate, tenants, custType, pdi,page)
        } else if (invoiceId != null) {
            invoiceSettlementRepo.getPageableSettlementsForInvoice(invoiceId, page)
        } else {
            settlementRepo.getSettlementRecordsByCreditNote(supplierId, settlementNumber, creditNoteId, fromDate, toDate, tenants, custType,pdi,page)
        }

        res.content.forEach { settlement ->
            val supplierList = supplierProxy.supplier(listOf(settlement.partnerId))
            val supplier = if (supplierList.isNotEmpty()) supplierList[0] else null
            if (supplier != null) settlement.supplierName = supplier.partnerName!!
            settlement.invoices = invoiceSettlementRepo.getInvoicesForSettlement(settlement.id).toMutableList()
            var creditNotes = creditNoteService.getCreditNotesBySettlementId(settlement.id)
            if (creditNotes == null) {
                creditNotes = mutableListOf()
            } else {
                creditNotes = creditNotes.toMutableList()
            }
            settlement.creditNotes = creditNotes
            settlement.createdOn = DateUtils.getISTDateTime(settlement.createdOn)
            settlement.updatedOn = DateUtils.getISTDateTime(settlement.updatedOn)
            settlement.receipt = receiptRepo.getReceiptBySettlementId(settlement.id)
            settlement.retailerDebitNotes = retailerDebitNoteSettlementMappingRepo.getSettledRetailerDn(settlement.id)
        }
        return PageableSettlement(res.totalElements, res.totalPages,res.hasPrevious(),res.hasNext(), res.content)

    }

    fun updateCreditNotes(user: String, settlement: Settlement,list:MutableList<CreditNoteSettlement>): MutableList<CreditNoteSettlement> {
        log.debug("Inside updateCreditNotes")
        settlement.creditNotes.forEach { creditNote ->
            creditNote.settlementId = settlement.id
            if(creditNote.deductRemainingAmount?:true && creditNote.amountUsed?:BigDecimal.ZERO > BigDecimal.ZERO) {
                creditNote.remainingAmount = creditNote.remainingAmount - creditNote.amountUsed!!
            }
            creditNote.status = if(creditNote.remainingAmount.setScale(2, RoundingMode.HALF_EVEN) > BigDecimal.ZERO) {NoteStatus.PARTIAL_REALIZED} else {NoteStatus.REALIZED}
            log.debug("CN remaining amount: ${creditNote.id} - ${creditNote.remainingAmount} - ${creditNote.amountUsed} - ${creditNote.status}")
            creditNote.tenant = settlement.tenant
            try {
                creditNoteService.update(user, creditNote.id, creditNote)
            } catch (e: Exception) {
                throw e
            }
            var cnsMapping = CreditNoteSettlement(creditNoteId=creditNote.id, settlementId=settlement.id, paidAmount=creditNote.amountUsed!!, createdOn=LocalDateTime.now())
            list.add(cnsMapping)
        }
        creditNoteSettlementRepo.saveAll(list)
        return list
    }

    fun updateAdvancePayment(user: String, settlement: Settlement,advPaySettlement:MutableList<AdvanceSettlementMapping>) {
        log.debug("Inside updateAdvancePayment")

        var amt = settlement.paidAmount.toBigDecimal().setScale(2, BigDecimal.ROUND_HALF_EVEN)
        var advpayment = mutableListOf<AdvancePayment>()
        var isConsumedSettlement = false
        run loop@{

            log.debug("inside inline")
            settlement.advancePayment.forEach { advpay ->

                log.debug("inside inline advance payment")
                log.debug("amount = $amt && isCons = $isConsumedSettlement")
                log.debug("advPay Obj : ${ObjectMapper().writeValueAsString(advpay)}")

                var advpayObj = advancePaymentRepo.getOne(advpay.id!!)
                if(advpayObj.type == PartnerType.VENDOR){
                    throw RequestException("${advpayObj.documentId} is of type vendor, cannot settle against customer invoice.")
                }
                log.debug("first condition ${advpayObj.amountPending} > $amt && ${!isConsumedSettlement} =  ${advpayObj.amountPending!! > amt && !isConsumedSettlement}")

                if (advpayObj.amountPending!! > amt && !isConsumedSettlement) {

                    log.debug("inside >")
                    advPaySettlement.add(AdvanceSettlementMapping(AdvanceSettlementId(settlement.id, advpayObj.id!!), amt))
                    advpayObj.amountPending = advpayObj.amountPending!!.minus(amt)
                    advpayment.add(advpayObj)
                    isConsumedSettlement = true
                    return@loop

                }
                log.debug("2nd condition ${advpayObj.amountPending} = $amt && ${!isConsumedSettlement} =  ${(advpayObj.amountPending!! == amt && !isConsumedSettlement)}")
                if (advpayObj.amountPending!! == amt && !isConsumedSettlement) {
                    log.debug("inside =")
                    advPaySettlement.add(AdvanceSettlementMapping(AdvanceSettlementId(settlement.id, advpayObj.id!!), advpayObj.amountPending!!))
                    advpayObj.amountPending = BigDecimal.ZERO
                    advpayObj.consumed = true
                    advpayment.add(advpayObj)
                    isConsumedSettlement = true
                    return@loop

                }
                log.debug("3rd condition ${advpayObj.amountPending} < $amt && ${!isConsumedSettlement} =  ${advpayObj.amountPending!! < amt && !isConsumedSettlement}")
                if (advpayObj.amountPending!! < amt && !isConsumedSettlement) {

                    log.debug("inside <")

                    advPaySettlement.add(AdvanceSettlementMapping(AdvanceSettlementId(settlement.id, advpayObj.id!!), advpayObj.amountPending!!))
                    amt = amt.minus(advpayObj.amountPending ?: BigDecimal.ZERO)
                    advpayObj.amountPending = BigDecimal.ZERO
                    advpayObj.consumed = true
                    advpayment.add(advpayObj)

                }
            }
        }

        if (!advpayment.isNullOrEmpty()) advancePaymentRepo.saveAll(advpayment)
        if (!advPaySettlement.isNullOrEmpty()) advanceSettlementMappingRepo.saveAll(advPaySettlement)

    }


    fun updateChargeInvoices(user: String, settlement: Settlement) {
        log.debug("Inside updateInvoices ${settlement.id}")

        var tenants = companyTenantMappingRepo.getAllTenantByTenant(settlement.tenant!!)
        var pid = mutableListOf<Long?>()
        if (tenants.isNullOrEmpty()) throw RequestException("No tenant Found !")

        val changedInvoices = mutableListOf<Charges>()
        val newChargesInvoices = mutableListOf<Charges>()

        newChargesInvoices.addAll(settlement.chargeInvoice)
        settlement.chargeInvoice.clear()
        settlement.chargeInvoice.addAll(newChargesInvoices)

        var list = mutableListOf<ChargeSettlement>()

        settlement.chargeInvoice.forEach { chargeInvocie ->

            val bki = chargesRepo.get(chargeInvocie.id)

            if (bki != null) {
                log.debug("DB inv  : ${bki!!.paidAmount}  ----- ")
                log.debug("settlement inv : ${chargeInvocie.paidAmount}  ----- ")
                if (bki.status == InvoiceStatus.PAID || bki.status == InvoiceStatus.DELETED)
                    throw RequestException("Trying to change the invoice in a terminal state - ${bki.status}")
                bki.updatedOn = LocalDateTime.now()

                val amountPaid = chargeInvocie.paidAmount - bki.paidAmount
                bki.paidAmount = chargeInvocie.paidAmount

                if (abs(chargeInvocie.amount - chargeInvocie.paidAmount) > 0.01) {
                    bki.status = InvoiceStatus.PARTIAL_PAID
                } else {
                    bki.status = InvoiceStatus.PAID
                    pid.add(bki.partnerId)
                }
                bki.settledOn = LocalDateTime.now()
                bki.settlementId = settlement.id
                bki.settlementNumber = settlement.settlementNumber
                changedInvoices.add(bki)
                list.add(ChargeSettlement(ChargeSettlementId(bki.id, settlement.id), BigDecimal(amountPaid), bki.status))
            } else {
                log.error("charge invoice id: ${chargeInvocie.id} not found in DB")
            }
        }

        chargeSettlementRepo.saveAll(list)

        try {
            settlement.chargeInvoice.clear()
            settlement.chargeInvoice.addAll(chargesRepo.saveAll(changedInvoices))

        } catch (e: Exception) {
            log.error("Error while saving the invoices: ${e.message}")
            throw e
        }
        if(pid.isNotEmpty()) {
            blockedVendorSettlementPusher.blockedVendorSettlementSinkProducer(pid)
        }
    }

//    fun updateItems(settlement: Settlement) {
//        log.debug("Inside updateItems")
//
//        val items = eprCalculationUtil.generateEpr(settlement.invoices, settlement.creditNotes)
//        try {
//            items.forEach { bkItem ->
//                val it = bkItemRepo.get(bkItem.id)
//                if (it != null) {
//                    it.epr = bkItem.epr
//                    bkItemRepo.save(it)
//                } else {
//                    log.error("item with id: ${bkItem.id} not found in DB")
//                }
//            }
//        } catch (e: Exception) {
//            throw e
//        }
//    }
    @Transactional
    fun updateVendorLedger(user: String, settlement: Settlement,cleared: Boolean) {
        log.debug("Inside updateVendorLedger")

        var particulars: String? = null
        var extRefNum: String? = null
        var invoiceList = settlement.invoices
        var type = when{
            settlement.invoices.isNotEmpty() -> settlement.invoices.get(0).type
            else -> PartnerType.CUSTOMER
        }
        if (invoiceList.isNotEmpty()) {

            var invoiceId = invoiceList.get(0).id
            var invoice = bkInvoiceRepo.get(invoiceId!!)
            settlement.supplierId = invoice?.partnerId!!
            settlement.partnerId = invoice?.partnerId
            settlement.partnerDetailId = invoice?.partnerDetailId!!

        }
        if (settlement.chargeInvoice.isNotEmpty()) {

            var chargeId = settlement.chargeInvoice.get(0).id
            var chargeInvoice = chargesRepo.get(chargeId!!)
            settlement.supplierId = chargeInvoice?.partnerId!!
            settlement.partnerId = chargeInvoice?.partnerId
            settlement.partnerDetailId = chargeInvoice?.partnerDetailId!!
        }

        if(settlement.retailerDebitNotes != null && settlement.retailerDebitNotes.isNotEmpty()){
            val debitNoteId = settlement.retailerDebitNotes[0].id
            var debitNote = retailerDebitNoteRepo.getOne(debitNoteId!!)
            settlement.supplierId = debitNote.partnerId
            settlement.partnerId = debitNote.partnerId
            settlement.partnerDetailId = debitNote.partnerDetailId
        }

        var makeLedger= true
        if(settlement.paymentType == PaymentType.CHEQUE && !cleared )
            makeLedger=false

        if (makeLedger){
            when (settlement.paymentType) {
                PaymentType.CASH -> {
                    particulars = "Paid by Cash"
                    extRefNum = settlement.paymentReference
                }

                PaymentType.CHEQUE -> {
                    particulars = "Paid by Cheque"
                    extRefNum = settlement.paymentReference
                }

                PaymentType.NEFT -> {
                    particulars = "Paid by NEFT"
                    extRefNum = settlement.paymentReference
                }

                PaymentType.CREDITNOTE -> {
                    if (settlement.creditNotes.isNullOrEmpty()) throw RequestException("Credit notes not specified")
                    particulars = "Paid by Credit Note"
                    extRefNum = "Settled by CN"
                }

                PaymentType.ADVANCE_PAYMENT -> {
                    if (settlement.advancePayment.isNullOrEmpty()) throw RequestException("Advance PAyments not specified")
                    particulars = "Paid by Advance Payment"
                    extRefNum = settlement.paymentReference
                }
                PaymentType.RIO_PAY -> {
                    particulars = "Paid via RioPay"
                    extRefNum = settlement.paymentReference
                }
                PaymentType.UPI -> {
                    particulars = "Paid via UPI QR Code"
                    extRefNum = settlement.paymentReference
                }
            }

            var amount = BigDecimal(settlement.paidAmount)
            var diff = 0.0
            var addRoundOff = false
            if (settlement.paidAmount > settlement.amount) {
                addRoundOff = true
                amount = BigDecimal(settlement.amount)
                diff = settlement.paidAmount - settlement.amount
            } else if (settlement.paidAmount < settlement.amount) {
                if(invoiceList.isNotEmpty()){
                    invoiceList.forEach { inv ->
                        var roundOffdiff = inv.amount - inv.paidAmount
                        if ((inv.amount - inv.amount.toInt() != 0.00) && (inv.amount.toInt() + 1 >= inv.paidAmount && inv.paidAmount >= inv.amount.toInt())) {
                            addRoundOff = true
                            diff += roundOffdiff
                        }
                    }
                }
                if(settlement.retailerDebitNotes!=null && settlement.retailerDebitNotes.isNotEmpty()){
                    settlement.retailerDebitNotes.forEach {
                        var roundOffdiff = it.amount - it.amountReceived
                        if ((it.amount - it.amount.toInt() != 0.00) && (it.amount.toInt() + 1 >= it.amountReceived && it.amountReceived >= it.amount.toInt())) {
                            addRoundOff = true
                            diff += roundOffdiff
                        }
                    }
                }
            }

            if (addRoundOff) {
                if(diff > maxDiffAmount){
                    checkAndCreateAdvance(user, settlement, BigDecimal(diff))
                }
            }
            val referenceNumber = when {
                settlement.charge -> settlement.chargeInvoice[0].docNumber
                settlement.invoices.isNotEmpty() -> settlement.invoices[0].invoiceNum
                (settlement.retailerDebitNotes.isNotEmpty()) -> settlement.retailerDebitNotes[0].documentNumber
                else -> settlement.settlementNumber
            }

            val client = when {
                settlement.charge -> InvoiceType.VENDOR
                settlement.invoices.isNotEmpty() -> settlement.invoices[0].client
                else -> InvoiceType.RIO
            }

            if (settlement.paymentType != PaymentType.CREDITNOTE && settlement.paymentType != PaymentType.ADVANCE_PAYMENT && settlement.paymentSource != AdvancePaymentSource.RIO_PAY) {
                val vl = VendorLedgerDto(
                    transactionDate = LocalDate.now(),
                    vendorId = settlement.supplierId,
                    vendorName = settlement.supplierName!!,
                    ledgerEntryType = if (type == PartnerType.CUSTOMER) LedgerEntryType.CREDIT else LedgerEntryType.DEBIT,
                    documentType = if (type == PartnerType.CUSTOMER) DocumentType.CX_RECEIPT else DocumentType.VENDOR_PAYMENT,
                    documentNumber = settlement.settlementNumber!!,
                    referenceNumber =  referenceNumber,
                    externalReferenceNumber = extRefNum,
                    particulars = particulars ?: "",
                    debitAmount = if (type == PartnerType.CUSTOMER) BigDecimal.ZERO else amount,
                    creditAmount = if (type == PartnerType.CUSTOMER) amount else BigDecimal.ZERO,
                    tenant = settlement.tenant,
                    partnerDetailId = settlement.partnerDetailId,
                    partnerId = settlement.partnerId,
                    type = type,
                    client = client,
                    remark = settlement.remarks
                )

                partnerService.addVendorLedgerEntry(user, vl)
            }

            if (settlement.paymentType == PaymentType.CASH || settlement.paymentType == PaymentType.CHEQUE || settlement.paymentType == PaymentType.NEFT || settlement.paymentType == PaymentType.RIO_PAY || settlement.paymentType == PaymentType.UPI) {
                var particulars =
                    if (settlement.type == PartnerType.VENDOR) {
                        "Paid to ${settlement.supplierName}"
                    } else {
                        "Received from ${settlement.supplierName}"
                    }

                val cl = CashBankLedgerDto(
                    transactionDate = LocalDate.now(),
                    ledgerEntryType = if (type == PartnerType.VENDOR) LedgerEntryType.CREDIT else LedgerEntryType.DEBIT,
                    documentType = if (type == PartnerType.VENDOR) SettlementType.PAYMENT else SettlementType.RECEIPT,
                    documentNumber = "",
                    referenceNumber = settlement.settlementNumber,
                    externalReferenceNumber = extRefNum,
                    particulars = particulars,
                    debitAmount = if (type == PartnerType.VENDOR) BigDecimal.ZERO else BigDecimal(settlement.paidAmount),
                    creditAmount = if (type == PartnerType.VENDOR) BigDecimal(settlement.paidAmount) else BigDecimal.ZERO,
                    tenant = settlement.tenant,
                    partnerDetailId = settlement.partnerDetailId,
                    partnerId = settlement.partnerId,
                    paymentType = settlement.paymentType,
                    paymentMode = if (settlement.paymentType == PaymentType.CASH) PaymentMode.CASH else PaymentMode.BANK

                )

                caseBankLedgerService.addCashBankLedgerEntry(user, cl)
            }
        }

    }

    @Transactional
    fun save(user: String, settlement: Settlement, ds: String? = null): Settlement {

        if (settlement.supplierId == 0L)
            throw DetailedRequestException(BookkeeperErrors.MISSING_INPUT, arrayOf("Supplier Id"))
        if (settlement.amount < 0)
            throw DetailedRequestException(BookkeeperErrors.INVALID_INPUT, arrayOf("Amount Settled"))

        if(settlementRepo.getSettlementByUUID(settlement.uuid) != null){
            throw RequestException("Request is already processed, please refresh the page and check. ${settlement.uuid}")
        }

        var cashSettlementFlag = true
        if(settlement.paymentType == PaymentType.CASH) {
            cashSettlementFlag = when{
                settlement.invoices.isNotEmpty() -> checkCashSettlementAmount(settlement.invoices[0].partnerDetailId!!, settlement.paidAmount)
                settlement.retailerDebitNotes.isNotEmpty() -> checkCashSettlementAmount(settlement.retailerDebitNotes[0].partnerDetailId!!, settlement.paidAmount)
                else -> true
            }
        }
        if(settlement.paymentType == PaymentType.NEFT){
            val checkNEFT = settlementReadRepo.existsDuplicateNeftSettlement(settlement.invoices.lastOrNull()?.partnerDetailId?:settlement.retailerDebitNotes.lastOrNull()?.partnerDetailId?:0,settlement.paymentDate?: LocalDate.now(),settlement.tenant,settlement.paymentReference!!)
            if(checkNEFT){
                throw RequestException("Duplicate NEFT Settlement for reference number ${settlement.paymentReference} and payment date ${settlement.paymentDate}")
            }
        }
        if(!cashSettlementFlag){
            throw RequestException("Settlement amount exceeds daily cash limit")
        }
        var tenants = companyService.findTenants(ds?:settlement.tenant)
        settlement.tenant = tenants[0]!!
        if(!settlement.charge) {
//            if (isProcessed(settlement)) {
//                throw RequestException("Duplicate Settlement")
//            }

            val invoiceIds = settlement.invoices.map { it.id }
            val invoices = bkInvoiceRepo.findByIds(invoiceIds)
            val invoiceMap = invoices.associateBy { it.id }

            settlement.invoices.forEach {
                if (invoiceMap[it.id]?.status == InvoiceStatus.PAID) {
                    throw RequestException("Invoice  ${it.invoiceNum} is already PAID")
                }
                it.tenant = invoiceMap[it.id]?.tenant
            }
        }
        var invoiceTenant = if(!settlement.charge && settlement.invoices.isNotEmpty()) {
            settlement.invoices.get(0).tenant
        }else if(settlement.retailerDebitNotes.isNotEmpty()){
            settlement.retailerDebitNotes.get(0).tenant
        } else {
            settlement.chargeInvoice.get(0).tenant
        }
        settlement.createdOn = LocalDateTime.now()
        settlement.updatedOn = LocalDateTime.now()
        settlement.createdBy = user
        if(settlement.charge) settlement.type = PartnerType.CUSTOMER

        if (invoiceTenant != null) settlement.tenant = invoiceTenant

        if (settlement.paymentType == PaymentType.CREDITNOTE)
            settlement.paymentReference = settlement.creditNotes[0].creditNoteNumber
        if (settlement.paymentType == PaymentType.ADVANCE_PAYMENT)
            settlement.paymentReference = settlement.advancePayment[0].documentId

        val company = companyRepo.getCompanyByTenant(settlement.tenant)
                ?: throw RequestException("Company mapping not found for ${settlement.tenant}")
        settlement.settlementNumber = if (settlement.type == PartnerType.CUSTOMER) documentMasterService.getDocumentNumber(user, company.companyCode, DocumentType.CX_RECEIPT) else documentMasterService.getDocumentNumber(user, company.companyCode, DocumentType.VENDOR_PAYMENT)
        var sr: Settlement
        try {
            sr = settlementRepo.save(settlement)
        } catch (e: Exception) {
            if (e is DataIntegrityViolationException)
                throw DetailedRequestException(BookkeeperErrors.ERROR, arrayOf("Duplicate Settlement"))

            throw e
        }

        settlement.id = sr.id

        if(settlement.paymentSource != AdvancePaymentSource.RIO_COLLECTIONS && settlement.paymentSource != AdvancePaymentSource.RIO_PAY) {
            updateVendorLedger(user, settlement, false)
        }
        var advPaySettlementList = mutableListOf<AdvanceSettlementMapping>()
        updateAdvancePayment(user, settlement,advPaySettlementList)
        var cnSettlementList = mutableListOf<CreditNoteSettlement>()
        updateCreditNotes(user, settlement,cnSettlementList)
        receiptService.createSettlementReceipt(settlement)
        var invoiceSettlementList = mutableListOf<InvoiceSettlement>()
        var debitNoteSettlementList: MutableList<RetailerDebitNoteSettlementMapping> = mutableListOf()

        if(settlement.charge){
            updateChargeInvoices(user, settlement)
            if ( settlement.paymentType.name.equals(PaymentType.CHEQUE.name))
                createChequeHandle(settlement, user)

        }else {
            if(settlement.invoices.isNotEmpty()){
                invoiceSettlementList = invoiceService.updateInvoices(user, settlement,invoiceSettlementList)
            }
            if(settlement.retailerDebitNotes.isNotEmpty()){
                debitNoteSettlementList = retailerDebitNoteService.updateRetailerDebitNotes(user, settlement)
            }
            if(settlement.paymentType == PaymentType.CHEQUE &&
                ((settlement.invoices.isNotEmpty() && settlement.invoices[0].type == PartnerType.CUSTOMER) || settlement.retailerDebitNotes.isNotEmpty())){
                createChequeHandle(settlement, user)
            }
        }
        if (settlement.paymentType == PaymentType.ADVANCE_PAYMENT || settlement.paymentType == PaymentType.CREDITNOTE) {
            if (settlement.invoices.isNotEmpty()) {
                var splitValues = splitInvoiceSettlementAmount(advPaySettlementList, cnSettlementList, invoiceSettlementList)
                settlementDistributionMapping.saveAll(splitValues)
            }else if (settlement.retailerDebitNotes.isNotEmpty()){
                var splitValues = splitDebitNoteSettlementAmount(advPaySettlementList, cnSettlementList, debitNoteSettlementList)
                settlementDebitNoteDistributionMappingRepo.saveAll(splitValues)
            }
        }

//        if (settlement.creditNotes.any{ it.noteType == NoteTypes.DISCOUNT }) {
//            updateItems(settlement)
//        }

        return sr
    }

    fun splitInvoiceSettlementAmount(
        advancePaymentList: List<AdvanceSettlementMapping>? = null,
        creditNoteList: List<CreditNoteSettlement>? = null,
        invoiceSettlementMappingList: List<InvoiceSettlement>
    ): List<SettlementInvoiceDistributionMapping> {
        val settlements = mutableListOf<SettlementInvoiceDistributionMapping>()
        var remainingInvoiceAmounts = invoiceSettlementMappingList.associate {
            it.id.invoiceId to (it.paidAmount ?: BigDecimal.ZERO)
        }.toMutableMap()

        val creditPaymentSettlementList = if (!advancePaymentList.isNullOrEmpty()) advancePaymentList.map {
            SettlementInvoiceDistributionMapping(
                advancePaymentId = it.id.advancePaymentId,
                settlementId = it.id.settlementId,
                creditNoteId = null,
                invoiceId = 0,
                amount = it.amt,
                createdOn = null
            )
        } else creditNoteList?.map {
            SettlementInvoiceDistributionMapping(
                    advancePaymentId = null,
                    settlementId = it.settlementId,
                    creditNoteId = it.creditNoteId,
                    invoiceId = 0,
                    amount = it.paidAmount,
                    createdOn = it.createdOn
            )
        }

        for (creditNote in creditPaymentSettlementList.orEmpty()) {
            var remainingCreditAmount: BigDecimal = creditNote.amount

            for (invoice in invoiceSettlementMappingList) {
                if (remainingCreditAmount > BigDecimal.ZERO && (remainingInvoiceAmounts[invoice.id.invoiceId]
                        ?: BigDecimal.ZERO) > BigDecimal.ZERO
                ) {
                    val remainingInvoiceAmount = remainingInvoiceAmounts[invoice.id.invoiceId] ?: BigDecimal.ZERO
                    val paidAmount = remainingCreditAmount.min(remainingInvoiceAmount)

                    if (paidAmount > BigDecimal.ZERO) {
                        // Credit note can settle part of the invoice
                        val distributionMapping = SettlementInvoiceDistributionMapping(
                                creditNoteId = creditNote.creditNoteId,
                                advancePaymentId = creditNote.advancePaymentId,
                                settlementId = creditNote.settlementId,
                                invoiceId = invoice.id.invoiceId,
                                amount = paidAmount,
                                createdOn = LocalDateTime.now()
                        )

                        settlements.add(distributionMapping)

                        // Update the remaining amount in the credit note and the invoice
                        remainingCreditAmount = remainingCreditAmount.subtract(paidAmount)
                        remainingInvoiceAmounts[invoice.id.invoiceId] = remainingInvoiceAmounts[invoice.id.invoiceId]?.subtract(paidAmount)
                                ?: BigDecimal.ZERO
                    }
                }
            }
        }

        return settlements
    }

    fun splitDebitNoteSettlementAmount(
        advancePaymentList: List<AdvanceSettlementMapping>? = null,
        creditNoteList: List<CreditNoteSettlement>? = null,
        retailerDnSettlementMappingList: List<RetailerDebitNoteSettlementMapping>
    ): List<SettlementDebitNoteDistributionMapping> {
        val settlements = mutableListOf<SettlementDebitNoteDistributionMapping>()
        val remainingDnAmounts: MutableMap<Long, BigDecimal> = retailerDnSettlementMappingList.associate {
            it.retailerDebitNoteId.id!! to (it.paidAmount?.toBigDecimal() ?: BigDecimal.ZERO)
        }.toMutableMap()

        val creditPaymentSettlementList = if (advancePaymentList?.isNotEmpty() == true) {
            advancePaymentList.map {
                val advancePayment = advancePaymentService.getAdvancePaymentById(it.id.advancePaymentId)
                val settlement = settlementRepo.getOne(it.id.settlementId)
                SettlementDebitNoteDistributionMapping(
                    advancePayment = advancePayment,
                    settlement = settlement,
                    creditNote = null,
                    retailerDebitNote = null,
                    amount = it.amt
                )
            }
        } else {
            creditNoteList?.map {
                val creditNote = creditNoteService.getCreditNoteById(it.creditNoteId)
                val settlement = settlementRepo.getOne(it.settlementId)
                SettlementDebitNoteDistributionMapping(
                    advancePayment = null,
                    settlement = settlement,
                    creditNote = creditNote,
                    retailerDebitNote = null,
                    amount = it.paidAmount
                )
            }
        }

        if(creditPaymentSettlementList.isNullOrEmpty()){
            return settlements
        }

        for (credit in creditPaymentSettlementList) {
            var remainingCreditAmount: BigDecimal = credit.amount

            for (dn in retailerDnSettlementMappingList) {
                if (remainingCreditAmount > BigDecimal.ZERO && (remainingDnAmounts[dn.retailerDebitNoteId.id]
                        ?: BigDecimal.ZERO) > BigDecimal.ZERO
                ) {
                    val remainingInvoiceAmount = remainingDnAmounts[dn.retailerDebitNoteId.id] ?: BigDecimal.ZERO
                    val paidAmount = remainingCreditAmount.min(remainingInvoiceAmount)

                    if (paidAmount > BigDecimal.ZERO) {
                        // Credit note can settle part of the invoice
                        val distributionMapping = SettlementDebitNoteDistributionMapping(
                            creditNote = credit.creditNote,
                            advancePayment = credit.advancePayment,
                            settlement = credit.settlement,
                            retailerDebitNote = dn.retailerDebitNoteId,
                            amount = paidAmount
                        )
                        settlements.add(distributionMapping)

                        // Update the remaining amount in the credit note and the invoice
                        remainingCreditAmount = remainingCreditAmount.subtract(paidAmount)
                        remainingDnAmounts[dn.retailerDebitNoteId.id!!] = remainingDnAmounts[dn.retailerDebitNoteId.id]?.subtract(paidAmount)
                            ?: BigDecimal.ZERO
                    }
                }
            }
        }

        return settlements
    }

    private fun createChequeHandle(settlement: Settlement, user: String) {
        var chequeObj = CreateChequeHandleDto(
            settlement.tenant,
            settlement.paymentReference ?: "",
            settlement.id,
            null,
            settlement.paymentDate
        )
        if(chequeHandleService.validateNewEntry(chequeObj.chequeNumber,settlement.partnerDetailId!!,settlement.chequeDate!!,settlement.tenant))
            chequeHandleService.addChequeHandleEntry(user, chequeObj)
        else{
            throw RequestException("Cheque already exists for the given cheque number")
        }
    }

    fun getBkInvoicesForSupplier(supplierId: Long?, InvoiceNum: String?, status: InvoiceStatus?, tenant: String, customerType: Boolean, ds: String? = null): List<BkInvoiceIdNum> {
        log.debug("Inside getBkInvoicesForSupplier")
        var custType: PartnerType = PartnerType.VENDOR
        if (customerType) custType = PartnerType.CUSTOMER
        var tenants = companyService.findTenants(ds?:tenant)
        return bkInvoiceRepo.getBkInvoicesForSupplier(supplierId, InvoiceNum, status, custType, tenants)
    }

    fun getPendingInvoiceAmount(supplierId: Long?, tenant: String, customerType: Boolean, ds: String? = null): Double {
        log.debug("Inside getPendingInvoiceAmount")
        var custType: PartnerType = PartnerType.VENDOR
        if (customerType) custType = PartnerType.CUSTOMER
        var tenants = companyService.findTenants(ds?:tenant)
        if (tenants.isNullOrEmpty()) return 0.0

        val amount = bkInvoiceRepo.getPendingInvoiceAmount(supplierId, custType, tenants)

        var res = 0.0
        if (amount != null) res = amount

        return res
    }

    fun isProcessed(settlement: Settlement): Boolean {
        log.debug("Inside isProcessed")

        val existing = settlementRepo.findDuplicates(settlement.supplierId, settlement.createdBy, settlement.amount, settlement.tenant)

        if (existing != null && existing.isNotEmpty()) {
            log.error("Settlement record being saved is similar to an existing record")
            log.error("${existing[0].id}, ${existing[0].createdBy}, ${existing[0].amount}")
            return true
        }

        return false
    }

    fun getById(settlementId: Long): Settlement? {
        return settlementRepo.findById(settlementId).orElse(null)

    }

    fun getSettlementById(settlementId: Long): Settlement? {
        log.debug("Inside getSettlementById :$settlementId")

        var settlement: Settlement
        try {
            settlement = settlementRepo.getOne(settlementId)
        } catch (e: Exception) {
            throw RequestException("$e: Settlement with id: $settlementId not found")
        }

        log.debug("settlement data extracted: $settlement")

        settlement.invoices = bkInvoiceRepo.getBySettlementId(settlement.id).toMutableList()
        log.debug("total invoices - ${settlement.invoices.size}")
        var creditNotes = creditNoteService.getCreditNotesBySettlementId(settlementId)
        if (creditNotes == null) {
            creditNotes = mutableListOf()
        } else {
            creditNotes = creditNotes.toMutableList()
        }
        settlement.creditNotes = creditNotes

        log.debug("credit notes: $settlement")
        return settlement
    }

    @Transactional
    fun getPaymentAdviceData(settlementId: Long): PaymentAdviceDto {
        var payAdviceItems: List<PaymentAdviceItem?>?
        var chequeBounce: Boolean = false
        var settlement = settlementRepo.getOne(settlementId)
        try {
            chequeBounce = settlement.isBounced ?: false
            payAdviceItems = invoiceSettlementRepo.getPaymentAdviceData(settlementId)
            if(payAdviceItems.isNullOrEmpty()){
                log.debug("inside charge check ")
                payAdviceItems = chargeSettlementRepo.getPaymentAdviceDataForCharges(settlementId)
            }

        } catch (e: Exception) {

            log.debug("advice error : $e")
            throw RequestException("$e: Settlement with id: $settlementId not found")
        }
        var companyTenantMapping = companyService.getCompanyTenantMappingObject(settlement.tenant)
        var distributorId = companyTenantMapping?.partnerDetailId?:throw IllegalArgumentException("Distributor id not mapped with tenant ${settlement.tenant}")
        log.debug("payment data extracted: ${ObjectMapper().writeValueAsString(payAdviceItems)}")

        var creditNotes = creditNoteService.getCreditNotesBySettlementId(settlementId)
        if (creditNotes == null) {
            creditNotes = mutableListOf()
        } else {
            creditNotes = creditNotes.toMutableList()
        }
        var receipt = receiptRepo.getReceiptBySettlementId(settlementId)
        var advancePaymentList = advanceSettlementMappingRepo.getAdvanceInfoForSettlement(settlementId)

        log.debug("credit notes: ${ObjectMapper().writeValueAsString(creditNotes)}")
        return PaymentAdviceDto(payAdviceItems, creditNotes, advancePaymentList ?: mutableListOf(), chequeBounce, receipt, distributorId)
    }

    fun getSettlementAmountForSupplier(supplierId: Long?): SettlementAmount {
        log.debug("getSettlementAmountForSupplier: $supplierId")

        val amount = settlementRepo.getSettledAmountForSupplier(supplierId)
        var res = 0.0
        if (amount != null) res = amount

        return SettlementAmount(res)
    }

    fun getSettlementBySettlementNumber(settlementNumber: String): Settlement? {
        log.debug(" getting settlement by settlement number: $settlementNumber")

        return settlementRepo.getSettlementBySettlementNumber(settlementNumber)
    }

    fun getAllbanks(): List<BkBank>? {
        var res = bkBankRepo.getNamesForBank()
        log.debug("getting all banks names : $res")
        return res
    }

    @Async
    fun autoSettlementCustomerPE() {
        var company = companyRepo.findAll()
        var pid = mutableListOf<Long?>()
        for (companyObj in company) {
            log.debug("company ${companyObj.name}")
            var tenantList = companyTenantMappingRepo.getAllTenantByCompanyId(companyObj.id)
            if (tenantList.isNotEmpty()) {
                var creditResult: MutableList<CreditNote>
                var originalCreditResult: MutableList<CreditNote>
                try {
                    originalCreditResult = creditNoteRepo.findAll(SupplierCreditNoteSpecification(null, mutableListOf(NoteStatus.PENDING,NoteStatus.PARTIAL_REALIZED), mutableListOf(NoteTypes.SR_ACCEPTED, NoteTypes.SR_EXPIRED), null, tenantList, null, LocalDate.now().minusDays(2), LocalDate.now().minusDays(1), PartnerType.CUSTOMER,listOf(InvoiceType.PE,InvoiceType.ML,InvoiceType.B2C,InvoiceType.CS)))

                } catch (e: Exception) {
                    log.debug("no CN found for company ${companyObj.name}.")
                    break
                }
                creditResult = originalCreditResult.map { it.copy() } as MutableList<CreditNote>
                log.debug("total CN ${creditResult.size}")
                var customerCNMap = creditResult.stream().collect(Collectors.groupingBy(CreditNote::partnerId))

                log.debug("total CN partners ${customerCNMap.size}")

                customerCNMap.forEach loop@{ (t, u) ->

                    var totalCnAmt = BigDecimal.ZERO
                    var totalPaidAmt = BigDecimal.ZERO
                    var totalInvAmt = 0.0
                    u.forEach { it ->
                        // using all the CN remaining amount to be used for settlement only
                        // when the total invoice amount is more than the total CN amount
                        it.amountUsed = it.remainingAmount
                        totalCnAmt = totalCnAmt.plus(it.remainingAmount)
                    }

                    var copyCustomerInv = mutableListOf<BkInvoice>()
                    var customerInv = mutableListOf<BkInvoice>()
                    var size = 100
                    var hasNext: Boolean
                    var page = 0
                    var isConsumed: Boolean  = false
                    try {
                        var invoiceTotalAmt = 0.0
                        do {
                            val pagination = PageRequest.of(page, size)
                            var pageResult = bkInvoiceRepo.findAll(BkInvoiceSpecification(null, null, null, mutableListOf(InvoiceStatus.PARTIAL_PAID, InvoiceStatus.PENDING), LocalDate.of(2021, 4, 1), LocalDate.now(), tenantList, t, PartnerType.CUSTOMER, InvoiceType.B2C),pagination)
                            log.debug("Total invoice found : ${pageResult.totalElements}")
                            pageResult.content.forEach {
                                log.debug(" invoice found : ${it.invoiceId} and amt ${it.amount} and paid amt ${it.paidAmount}")
                                invoiceTotalAmt = invoiceTotalAmt.plus(it!!.amount.minus(it!!.paidAmount))
                                if(!isConsumed) {
                                    customerInv.add(it)
                                }
                                if(invoiceTotalAmt.toBigDecimal() >= totalCnAmt){
                                    isConsumed = true
                                }
                            }
                            hasNext = pageResult.hasNext()
                            page++
                        }while(invoiceTotalAmt.toBigDecimal() < totalCnAmt && hasNext)

                    } catch (e: Exception) {

                        log.debug("no invoice found for Partner $t")
                        return@loop

                    }

                    copyCustomerInv = customerInv.map { it.copy() } as MutableList<BkInvoice>


                    totalPaidAmt = totalCnAmt

                    log.debug("total cn value for customer $t is $totalCnAmt")

                    copyCustomerInv.forEach { totalInvAmt = totalInvAmt.plus(it!!.amount.minus(it!!.paidAmount)) }

                    log.debug("total invoice value of customer $t is $totalInvAmt")

                    if (totalInvAmt.toBigDecimal() >= totalCnAmt) {

                        var invoiceObjs = mutableListOf<BkInvoice>()

                        for (it in copyCustomerInv) {

                            log.debug("current invoice ${ObjectMapper().writeValueAsString(it)}")

                            if (totalCnAmt > BigDecimal.ZERO) {

                                var remainingInvAmt = it!!.amount.minus(it!!.paidAmount)

                                log.debug("remaining Amt $remainingInvAmt, total cn amt $totalCnAmt")

                                if (remainingInvAmt <= totalCnAmt.toDouble()) {
                                    it.paidAmount = it.amount
                                    it.status = InvoiceStatus.PAID
                                    totalCnAmt -= remainingInvAmt.toBigDecimal()
                                    pid.add(it.partnerId)

                                } else if (remainingInvAmt > totalCnAmt.toDouble()) {

                                    it.paidAmount = it.paidAmount.plus(totalCnAmt.toDouble())
                                    it.status = InvoiceStatus.PARTIAL_PAID
                                    totalCnAmt = BigDecimal.ZERO
                                }
                            }
                            invoiceObjs.add(it)
                            if (totalCnAmt <= BigDecimal.ZERO)
                                break
                        }
                        log.debug("creating settlement")
                        log.debug(" bk invoice list : ${invoiceObjs.size}")

                        var obj = Settlement(0, null, null, null, t!!, u[0].supplierName, totalInvAmt, totalPaidAmt.toDouble(), "Settled", null, invoiceObjs,
                                u, PaymentType.CREDITNOTE, "", LocalDate.now(), t, u[0].partnerDetailId, PartnerType.CUSTOMER, u[0].tenant
                                ?: "", null, null, null, false, false, mutableListOf(), mutableListOf(), false, null, AdvancePaymentSource.SYSTEM, mutableListOf(), UUIDUtil.generateUuid()
                        )

                        save("SYSTEM", obj, null)

                    }
                }

            }
        }
        if(pid.isNotEmpty()) {
            blockedVendorSettlementPusher.blockedVendorSettlementSinkProducer(pid)
        }
    }

    @Transactional
    fun autoSettlementCash(autoSettlementDTO :AutoSettlementDTO){
        // supports cash / UPI payment event
        var queryTenant = autoSettlementDTO.tenant
        var ds: String? = null //call the required api
        if(autoSettlementDTO.orderType == "B2B2B"){
            var dsTenants = companyService.findTenants(ds?:autoSettlementDTO.tenant)
            if(dsTenants.isNullOrEmpty())
                throw RequestException("No dark store mapping found for tenant ${autoSettlementDTO.tenant}")
            else{
                queryTenant = dsTenants.get(0).toString()
            }
        }
        var paymentMode = PaymentType.valueOf(autoSettlementDTO.paymentInfo!![0].paymentMode!!)

        var obj = bkInvoiceRepo.getRioInvoice(autoSettlementDTO.externalOrderId, queryTenant)?: throw RequestException("Invoice Not Found for tenant ${autoSettlementDTO.tenant} and RIO Order ${autoSettlementDTO.externalOrderId}")
        if(obj.status == InvoiceStatus.PAID){
            // invoice already paid, creating CN
            paymentExcessService.handleAdvancePayment(autoSettlementDTO.paymentInfo?.get(0)!!.amountCollected!!, obj.invoiceId!!, obj.partnerDetailId!!, null, autoSettlementDTO.tenant, paymentMode)
            return
        }
        var bkInvoice = obj.copy()


        if(paymentMode == PaymentType.CASH) {
            var cashLimitCheck = checkCashSettlementAmount(bkInvoice.partnerDetailId!!, autoSettlementDTO.paymentInfo?.get(0)!!.amountCollected!!.toDouble())
            if(cashLimitCheck){
                var settlementObj = Settlement(
                    0,
                    null,
                    null,
                    null,
                    bkInvoice.supplierId!!,
                    bkInvoice.supplierName,
                    bkInvoice!!.amount.minus(bkInvoice!!.paidAmount),
                    autoSettlementDTO.paymentInfo?.get(0)!!.amountCollected!!.toDouble(),
                    "Settled",
                    null,
                    mutableListOf(bkInvoice),
                    mutableListOf(),
                    PaymentType.CASH,
                    "",
                    autoSettlementDTO.paymentInfo!!.get(0).paymentDate!!.toLocalDate(),
                    bkInvoice.partnerId,
                    bkInvoice.partnerDetailId,
                    PartnerType.CUSTOMER,
                    autoSettlementDTO.tenant,
                    null,
                    null,
                    null,
                    false,
                    false,
                    mutableListOf(),
                    mutableListOf(),
                    false,
                    null,
                    AdvancePaymentSource.SYSTEM,
                    mutableListOf(),
                    UUIDUtil.generateUuid()
                )
                bkInvoice.paidAmount += autoSettlementDTO.paymentInfo?.get(0)!!.amountCollected!!.toDouble()
                save("SYSTEM", settlementObj, ds)
            }else{
                throw RequestException("Max cash limit reached for invoice id: ${bkInvoice.invoiceId} and invoice number: ${bkInvoice.invoiceNum}")
            }
        } else if(paymentMode == PaymentType.UPI) {
            paymentService.processUPIQRCodePaymentEvent(
                bkInvoice,
                autoSettlementDTO.paymentInfo!![0]!!.amountCollected!!,
                autoSettlementDTO.paymentInfo!![0].paymentReferenceNumber!!
            )
        }
    }

    fun createSettlementForInvoicePayment(bkInvoice: BkInvoice, amount: BigDecimal, paymentReference: String, paymentMode: PaymentType, ds: String? = null, tenant: String, source: AdvancePaymentSource) {
        var cashLimitCheck = true
        var partnerInfo = supplierProxy.supplier(supplierId = listOf(bkInvoice.partnerId))[0]!!
        var remainingAmount = (bkInvoice.amount - bkInvoice.paidAmount)
        var bkInvoiceCopy = bkInvoice.copy()
        if(paymentMode == PaymentType.CASH){
            cashLimitCheck = checkCashSettlementAmount(bkInvoice.partnerDetailId!!, amount.toDouble())
        }
        if(cashLimitCheck){
            bkInvoiceCopy.paidAmount = bkInvoiceCopy.paidAmount.plus(amount.toDouble())
            var settlement = Settlement(
                0,
                null,
                null,
                null,
                bkInvoice.partnerId!!,
                partnerInfo.partnerName,
                remainingAmount,//remaining invoice amount before current transaction
                amount.toDouble(),
                "Settled via RIO Payment",
                null,
                mutableListOf(bkInvoiceCopy),
                mutableListOf(),
                paymentMode,
                paymentReference,
                LocalDate.now(),
                bkInvoice.partnerId,
                bkInvoice.partnerDetailId,
                PartnerType.CUSTOMER,
                tenant,
                null,
                null,
                null,
                false,
                false,
                mutableListOf(),
                mutableListOf(),
                false,
                null,
                source,
                mutableListOf(),
                UUIDUtil.generateUuid()
            )
            save("SYSTEM", settlement, ds)
        }else{
            throw RequestException("Max cash limit reached for invoice ${bkInvoice.invoiceNum} and partnerDetailId ${bkInvoice.partnerDetailId}")
        }
    }

    @Async
    fun autoSettlementRIOInvoicesWithAdvanceCN() {
        var company = companyRepo.findAll()
        var isDS = listOf(true, false)
        var pid = mutableListOf<Long?>()
        var size = 100
        for (companyObj in company) {
            var tenantList = companyTenantMappingRepo.getAllTenantByCompanyId(companyObj.id)
            if (tenantList.isNotEmpty()) {
                tenantList.forEach { tenant ->
                    isDS.forEach { ds ->
                        try {
                            var tenants = companyService.findTenants(tenant!!)
                            var creditResult: MutableList<CreditNote>
                            var settlementTotal = 0.0
                            var page = 0
                            if(!tenants.isNullOrEmpty()) {
                                var originalCreditResult: MutableList<CreditNote>
                                try {
                                    originalCreditResult = creditNoteRepo.findAll(SupplierCreditNoteSpecification(null, mutableListOf(NoteStatus.PENDING,NoteStatus.PARTIAL_REALIZED), mutableListOf(NoteTypes.ADVANCE_PAYMENT), null, tenants, null, LocalDate.of(2022, 6, 1), LocalDate.now(), PartnerType.CUSTOMER,listOf(InvoiceType.RIO)))
                                } catch (e: Exception) {
                                    log.debug("no CN found for company ${tenants}.")
                                    return@forEach
                                }
                                if(originalCreditResult.isNullOrEmpty()){
                                    return@forEach
                                }
                                creditResult = originalCreditResult.map { it.copy() } as MutableList<CreditNote>
                                var customerCNMap = creditResult.stream().collect(Collectors.groupingBy(CreditNote::partnerId))
                                customerCNMap.forEach loop@{ (partnerId, cnList) ->
                                    var totalCnAmt = BigDecimal.ZERO
                                    cnList.forEach { it ->
                                        totalCnAmt = totalCnAmt.plus(it.remainingAmount)
                                    }
                                    var cnListCopy = cnList.filter { it.remainingAmount > BigDecimal.ZERO }.map { it.copy() } as MutableList<CreditNote>
                                    var eligibleCNsForSettlement: MutableMap<Long, CreditNote> = mutableMapOf()
                                    var eligibleInvoicesForSettlement: MutableMap<Long, BkInvoice> = mutableMapOf()
                                    var customerInv = mutableListOf<BkInvoice>()
                                    var size = 100
                                    var hasNext: Boolean
                                    try{
                                        do {
                                            val pagination = PageRequest.of(page, size)
                                            var pageResult = bkInvoiceRepo.findAll(BkInvoiceSpecification(null, null, null, mutableListOf(InvoiceStatus.PARTIAL_PAID, InvoiceStatus.PENDING), LocalDate.of(2021, 4, 1), LocalDate.now(), tenantList, partnerId, PartnerType.CUSTOMER, InvoiceType.RIO), pagination)
                                            log.debug("Total invoice found : ${pageResult.totalElements}")
                                            pageResult.content.forEach {
                                                log.debug(" invoice found : ${it.invoiceId} and amt ${it.amount} and paid amt ${it.paidAmount}")
                                                var invoiceRemainingAmount = it!!.amount.minus(it!!.paidAmount)
                                                while(invoiceRemainingAmount > 0 && cnListCopy.isNotEmpty()){
                                                    var advanceCN = cnListCopy[0]
                                                    if(advanceCN.amountUsed == null){
                                                        advanceCN.amountUsed = BigDecimal.ZERO
                                                    }
                                                    var cnRemainingAmount = advanceCN.remainingAmount.toDouble() - advanceCN.amountUsed!!.toDouble()
                                                    if(invoiceRemainingAmount <= cnRemainingAmount){
                                                        it.status = InvoiceStatus.PAID
                                                        settlementTotal += invoiceRemainingAmount
                                                        it.paidAmount += invoiceRemainingAmount
                                                        advanceCN.amountUsed =
                                                            advanceCN.amountUsed!!.plus(invoiceRemainingAmount.toBigDecimal())
                                                        invoiceRemainingAmount = 0.0
                                                        pid.add(it.partnerId)
                                                    } else {
                                                        it.status = InvoiceStatus.PARTIAL_PAID
                                                        settlementTotal += cnRemainingAmount
                                                        it.paidAmount += cnRemainingAmount
                                                        advanceCN.amountUsed =
                                                            advanceCN.amountUsed!!.plus(cnRemainingAmount.toBigDecimal())
                                                        invoiceRemainingAmount -= cnRemainingAmount
                                                        cnListCopy.removeAt(0) // remove utilised CN from copy list
                                                    }
                                                    eligibleInvoicesForSettlement[it.id] = it
                                                    eligibleCNsForSettlement[advanceCN.id] = advanceCN
                                                    invoiceSettlementUpdateHandler.createSettlementConsumer(it.id, CreationType.INVOICE)
                                                }
                                            }
                                            hasNext = pageResult.hasNext()
                                            page++
                                        }while(hasNext && cnListCopy.isNotEmpty())

                                        var obj = Settlement(0, null, null, null, partnerId!!, cnList[0].supplierName, settlementTotal, settlementTotal, "Settled using Advance CNs", null, eligibleInvoicesForSettlement.values.toMutableList(),
                                            eligibleCNsForSettlement.values.toMutableList(), PaymentType.CREDITNOTE, "", LocalDate.now(), partnerId, cnList[0].partnerDetailId, PartnerType.CUSTOMER, cnList[0].tenant
                                                ?: "", null, null, null, false, false, mutableListOf(), mutableListOf(), false, null, AdvancePaymentSource.SYSTEM, mutableListOf(), UUIDUtil.generateUuid())

                                        save("SYSTEM", obj, null)
                                    } catch (e: Exception) {
                                        log.debug("no invoice found for Partner $partnerId")
                                        return@loop
                                    }
                                }
                            }
                        } catch (e: Exception) {
                            log.error("Advance CN auto settlement job failed for Ds: $ds - tenant: $tenant")
                            e.printStackTrace()
                        }
                    }
                }
            }
        }
        if(pid.isNotEmpty()) {
            blockedVendorSettlementPusher.blockedVendorSettlementSinkProducer(pid)
        }
    }
    fun unblockSettledVendor(toBeBlockVendors: MutableList<PartnerBlockedProcessDTO>?){
         try {
            if (toBeBlockVendors != null) {
                supplierProxy.blockPartners(toBeBlockVendors, "VAULT")
            }
        } catch (e: Exception) {
            log.error("Error message while unblocking ${e.message.toString()}")
             throw RequestException("Error while unblocking partner.")
        }
    }

    fun sendSettlementDataToRio(referenceId: Long, eventType: CreationType) {
        log.debug("received settlement event for invoiceId $referenceId")
        val data = when(eventType){
            CreationType.INVOICE -> bkInvoiceRepo.getInvoiceSettlementData(referenceId)
            CreationType.DEBIT_NOTE -> retailerDebitNoteRepo.getRetailerDebitNoteSettlementEventData(referenceId)
        }


        var retailer = ""
        try{
            val retailerData = supplierProxy.retailerInfo(data.partnerDetailId!!)
            if(retailerData.isNotEmpty()){
                retailer = retailerData[0]?.systemOwnership!!
            }
        }catch (e:Exception){
            log.error("Error in one roof flag check",e)
        }
        if (retailer == "RIO" && (data.client == InvoiceType.RIO || data.client == InvoiceType.EASY_SOL)) {

            val orderType = if(data.tenant!!.contains("ds")){OrderType.B2B2B}else{OrderType.B2B}
            val isDS = (orderType == OrderType.B2B2B)
            val tenant = data.tenant

            var companyTenantMappingObj = companyService.getCompanyTenantMappingObject(data.tenant?:tenant!!)
            val partnerId= data.partnerId!!

            val partner = vendorRepo.getPartners(partnerId, PartnerType.CUSTOMER,companyTenantMappingObj?.companyId?:0)?:throw RequestException("P1 and P2 mapping not found")

            val totalOutstandingAmount = partner.balance
//            if(partner.balance < roundOffLimit){
//                BigDecimal.ZERO
//            }else{
//                partner.balance
//            }
                var outstandingAmount = data.amount - data.paidAmount
                if(data.status == InvoiceStatus.PAID || data.status == InvoiceStatus.WRITE_OFF){
                    outstandingAmount = 0.0
                }
                val page = PageRequest.of(0, 1)
                val oldestInvoice =  bkInvoiceRepo.getOldestInvoicesCreatedOn(data.partnerDetailId!!, PartnerType.CUSTOMER, listOf(InvoiceType.RIO, InvoiceType.EASY_SOL), data.tenant!!, page)
                val oldestRetailerDn =  retailerDebitNoteRepo.getOldestRetailerDebitNoteCreatedOn(data.partnerDetailId!!, data.tenant!!, page)


                var maxDueDays: Long = 0
                if (oldestInvoice.hasContent()) {
                    maxDueDays = ChronoUnit.DAYS.between(
                        oldestInvoice.content[0]?.createdOn?.toLocalDate(),
                        LocalDate.now()
                    )
                }else if(oldestRetailerDn.hasContent()){
                    maxDueDays = ChronoUnit.DAYS.between(
                        oldestInvoice.content[0]?.createdOn?.toLocalDate(),
                        LocalDate.now()
                    )
                }
                val tradeCreditLedger = tradeCreditPaymentService.getOutstandingTradeCreditByPartnerDetailId(data.partnerDetailId!!)
                val res = InvoiceSettlementProducerEventDto(
                    retailerId = data.partnerDetailId!!,
                    distributorId = companyTenantMappingObj?.partnerDetailId!!,
                    totalOutstandingAmount = totalOutstandingAmount,
                    maxDueDays = maxDueDays,
                    orderId = data.referenceId!!,
                    invoiceNumber = data.referenceNumber!!,
                    invoiceDate = data.createdOn!!.toLocalDate(),
                    dueDate = data.dueDate!!,
                    amount = data.amount,
                    outstandingAmount = outstandingAmount,
                    referenceNumber = data.referenceNumber!!,
                    referenceDate = data.createdOn!!.toLocalDate(),
                    eventType = eventType,
                    type = CreationType.INVOICE,
                    tradeCreditOutstandingAmount = tradeCreditLedger.tradeCreditOutstandingAmount,
                    tradeCreditOverdueAmount = tradeCreditLedger.tradeCreditOverdueAmount,
                    maxTradeCreditDueDays = tradeCreditLedger.maxTradeCreditDueDays
                )
//                invoiceSettlementUpdateSinkPusher.createInvoiceSettlementProducer(res)
            eventPublisherUtil.createInvoiceSettlementProducer(res)
        }
    }

    fun checkVendorDetails(pid:MutableList<Long?>){
        var toBeBlockVendors: MutableList<PartnerBlockedProcessDTO>? = mutableListOf()
        val page = PageRequest.of(0, 1)
        pid.forEach {it->
            var status = supplierProxy.partners(it!!.toLong())
            var process=status?.blockedProcesses!!
            var partnerStatus=status?.isBlocked
            var unblock = false
            process.forEach {
                if(it?.processTypeId==3L && it?.isBlocked==true){
                    unblock = true
                }
            }
            val bkInvoice = bkInvoiceRepo.getOldestInvoicesCreatedOn(it!!, PartnerType.CUSTOMER, page)
            val cp = retailerCreditControlRepo.getPartnerCreditP(it) ?: 0
            var daysDiff = 0L
            if(bkInvoice.isEmpty){
                daysDiff = 0L
            }else {
                daysDiff = ChronoUnit.DAYS.between(
                    bkInvoice.content[0]?.createdOn?.toLocalDate(),
                    LocalDate.now()
                )
            }

            if (partnerStatus == false && unblock) {
                toBeBlockVendors!!.add(
                    PartnerBlockedProcessDTO(
                        null, 3, it.toInt(), null, "Payment",
                        "Sale Block", null, null, null, false
                    )
                )
            }
        }
        unblockSettledVendor(toBeBlockVendors)
    }

    fun checkCashSettlementAmount(partnerDetailId: Long, settleAmount: Double): Boolean{
        val fromDate = LocalDate.now()
        val toDate = LocalDate.now().plusDays(1)
        val from: LocalDateTime = fromDate.atStartOfDay()
        val to = toDate.atStartOfDay()
        var settledCashAmount: Double? = 0.0
        settledCashAmount = settlementReadRepo.getCashSettlementAmount(partnerDetailId, from, to)
        return settleAmount+settledCashAmount!! <= maxCashLimit
    }


    fun checkRioEnabledFlag(invoiceId: String): Boolean{
        var source = "RIO"
        try{
            var fulfilmentOrder = orderFulfilmentProxy.getWarehouseMapping(source, invoiceId)
            if(fulfilmentOrder != null){
                return true
            }
        }catch(e: Exception){
            return false
        }
        return false
    }

    @Transactional
    fun changePaymentMode(user: String, settlementChangePayment: SettlementPaymentChangeDto): Any {
        val pair = validate(settlementChangePayment, user)
        var settlement = pair.first
        val company = pair.second
        var invoiceObj = invoiceSettlementRepo.getInvoicesForSettlement(settlement.id)
        var oldInvoiceSettlementList = invoiceSettlementRepo.getSettlementInvoiceBySettlementId(settlement.id)
        createReversalLedgerEntry(settlement,user,company, invoiceObj[0].client,invoiceObj[0].invoiceNum)
        settlement.reversed = true
        settlementRepo.save(settlement)
        var settlementNumber = if (settlement.type == PartnerType.CUSTOMER) documentMasterService.getDocumentNumber(user, company.companyCode, DocumentType.CX_RECEIPT) else documentMasterService.getDocumentNumber(user, company.companyCode, DocumentType.VENDOR_PAYMENT)

        var settlementObj = Settlement(
            0,
            LocalDateTime.now(),
            LocalDateTime.now(),
            user,
            settlement.supplierId,
            settlement.supplierName,
            settlement.amount,
            settlement.paidAmount,
            "reversed settlement",
            settlementNumber,
            mutableListOf(),
            mutableListOf(),
            settlementChangePayment.type,
            settlementChangePayment.chequeOrRefNumber,
            settlementChangePayment.paymentDate,
            settlement.partnerId,
            settlement.partnerDetailId,
            settlement.type,
            settlement.tenant,
            settlementChangePayment.chequeDate,
            settlementChangePayment.bankId,
            settlementChangePayment.bankName,
            isBounced = false,
            reversed = false,
            advancePayment = mutableListOf(),
            chargeInvoice = mutableListOf(),
            charge = false,
            receipt = null,
            paymentSource = AdvancePaymentSource.SYSTEM,
            retailerDebitNotes = mutableListOf(),
            uuid = UUIDUtil.generateUuid()
        )

        val st = settlementRepo.save(settlementObj)
        settlementObj.id = st.id
        settlementObj.invoices = invoiceObj.toMutableList()
        var invoiceSettlementList = mutableListOf<InvoiceSettlement>()
        oldInvoiceSettlementList.forEach {
            invoiceSettlementList.add(InvoiceSettlement(InvoiceSettlementId(it.id.invoiceId,st.id),it.amount,it.paidAmount,it.invoiceStatus))
        }
        invoiceObj.forEach {
            it.settlementId = st.id
            it.settlementNumber = st.settlementNumber
        }
        bkInvoiceRepo.saveAll(invoiceObj)
        invoiceSettlementRepo.saveAll(invoiceSettlementList)
        updateVendorLedger(user,settlementObj,false)
        if(settlementChangePayment.type == PaymentType.CHEQUE)
            createChequeHandle(st, user)
        receiptService.handleReceiptReversal(ReceiptReversalDto(settlement.id, st.id, st.createdBy))
        return st
    }

    private fun validate(
        settlementChangePayment: SettlementPaymentChangeDto,
        user: String
    ): Pair<Settlement, Company> {
        var settlement = settlementRepo.getSettlementBySettlementNumber(settlementChangePayment.settlementNumber)
            ?: throw RequestException("Invalid Settlement Number ${settlementChangePayment.settlementNumber}")
        if (settlement.createdOn!!.toLocalDate() < LocalDate.now().minusDays(3))
            throw RequestException("payment mode reversal is only allowed for last ‘3' days settlements!")
        if (settlement.paymentType != PaymentType.CASH)
            throw RequestException("reversal is only allowed for CASH type of settlements!")
        if(settlement.reversed == true)
            throw RequestException("settlement already reversed please select another settlement!")
        var checkerList = checkerService.findCheckers(settlement.tenant, null, null)
        var checkerObj = checkerService.compareChecker(checkerList, user)
            ?: throw RequestException("Only checker can make this change.")

        if (settlementChangePayment.type.equals(PaymentType.CHEQUE)) {
            if (settlementChangePayment.chequeDate == null || settlementChangePayment.bankName == null)
                throw RequestException("cheque date or bank name is empty!")
            if(!chequeHandleService.validateNewEntry(settlementChangePayment.chequeOrRefNumber,settlement.partnerDetailId!!,settlementChangePayment.chequeDate!!,settlement.tenant))
                throw RequestException("Cheque already exists for the given cheque number")
        }
        if(settlementChangePayment.type.equals(PaymentType.NEFT)){
                var checkNeft = settlementReadRepo.existsDuplicateNeftSettlement(settlement.partnerDetailId?:0,settlementChangePayment.paymentDate,settlement.tenant,settlementChangePayment.chequeOrRefNumber)
                if(checkNeft){
                    throw RequestException("Duplicate NEFT Settlement for reference number ${settlementChangePayment.chequeOrRefNumber} and payment date ${settlementChangePayment.chequeDate}")
            }
        }
        val company = companyRepo.getCompanyByTenant(settlement.tenant!!)
            ?: throw RequestException("Company mapping not found for ${settlement.tenant}")
        return Pair(settlement, company)
    }

    fun createReversalLedgerEntry(settlement: Settlement,userId:String,company:Company,client:InvoiceType,invoiceNumber: String?){

        var adjustmentObj = AdjustmentEntry(0, LocalDateTime.now(), LocalDateTime.now(), userId, userId, userId,userId, userId, LocalDate.now(), settlement.supplierName, if (settlement.type == PartnerType.CUSTOMER) LedgerEntryType.DEBIT else LedgerEntryType.CREDIT, null, DocumentType.PAYMENT_REVERSAL, settlement.type, Status.APPROVED, if(settlement.paymentType == PaymentType.CASH) "Paid by CASH" else if(settlement.paymentType == PaymentType.NEFT)  "Paid by NEFT" else if(settlement.paymentType == PaymentType.RIO_PAY)  "Paid by RIO PAY" else "", settlement.paidAmount.toBigDecimal(), BigDecimal.ZERO, BigDecimal.ZERO, settlement.partnerId
            ?: 0, settlement.partnerDetailId, settlement.tenant, company.id, "", client)
        adjustmentObj.documentId = documentMasterService.getDocumentNumber(adjustmentObj.updatedBy!!, company.companyCode, DocumentType.PAYMENT_REVERSAL)
            adjustmentService.addAdjustmentToLedger(adjustmentObj, settlement.settlementNumber, invoiceNumber)
            adjustmentService.updateVendorBalance(adjustmentObj)

    }


    @Transactional
    fun reversePaymentMode(user: String, settlementId: Long): Result {

        var settlement = settlementRepo.findByIdOrNull(settlementId)
            ?: throw RequestException("Invalid Settlement Number $settlementId")
        if(settlement.reversed == true)
            throw RequestException("Settlement is already been reversed!")
        if(settlement.isBounced == true)
            throw RequestException("Settlement is already been bounced!")
        if(settlement.paymentType == PaymentType.CASH || settlement.paymentType == PaymentType.NEFT || settlement.paymentType == PaymentType.RIO_PAY){

            val pair = reversalData(settlement, user)
            val company = pair.first
            var invoiceObj = pair.second
            createReversalLedgerEntry(settlement,user,company, invoiceObj[0].client,invoiceObj[0].invoiceNum)
            invoiceObj.forEach {
                invoiceSettlementUpdateHandler.createSettlementConsumer(it.id, CreationType.INVOICE)
            }
        }else if (settlement.paymentType == PaymentType.ADVANCE_PAYMENT){

            val pair = reversalData(settlement, user)
            reverseAdvPayment(settlement)

        }else{
            throw RequestException("payment reversal not allowed for payment type ${settlement.paymentType}")
        }
        return success
    }

    private fun reversalData(
        settlement: Settlement,
        user: String
    ): Pair<Company, List<BkInvoice>> {
        val company = companyRepo.getCompanyByTenant(settlement.tenant!!)
            ?: throw RequestException("Company mapping not found for ${settlement.tenant}")
        var invoiceObj = reverseSettlementAndInvoice(user, settlement)
        return Pair(company, invoiceObj)
    }

    fun reverseSettlementAndInvoice(userId: String, settlement: Settlement): List<BkInvoice> {
        var invoiceObj = invoiceSettlementRepo.getInvoicesForSettlement(settlement.id)
        var settlementInvoiceObj = invoiceSettlementRepo.getSettlementInvoiceBySettlementId(settlement.id)
        var invoiceAmtMap = settlementInvoiceObj.associateBy { it.id.invoiceId }
        invoiceService.updateInvoiceAmtForBounceCheque(invoiceObj, invoiceAmtMap)
        settlement.reversed = true
        var settlementData = settlementRepo.save(settlement)
        receiptService.handleReceiptReversal(ReceiptReversalDto(settlementData.id, null, userId))
        return invoiceObj

    }
    fun reverseCn(settlement: Settlement){
        var cnObj = creditNoteSettlementRepo.getCnForSettlement(settlement.id)
        var settlementCnObj = creditNoteSettlementRepo.getSettlementCnBySettlementId(settlement.id)
        updateCreditNoteAmt(cnObj,settlementCnObj.associateBy { it.creditNoteId })
    }

    fun reverseAdvPayment(settlement: Settlement){

        var advPayObj = advanceSettlementMappingRepo.getAdvPayForSettlement(settlement.id)
        var settlementAdvMapObj = advanceSettlementMappingRepo.getSettlementAdvPayBySettlementId(settlement.id)
        updateAdvPayAmt(advPayObj,settlementAdvMapObj.associateBy { it.id.advancePaymentId })
    }

    fun updateCreditNoteAmt(cn: List<CreditNote>, cnAmtMap: Map<Long,CreditNoteSettlement>) {

        cn.forEach {

            if (cnAmtMap.containsKey(it.id)) {

                var paidAmt = it.remainingAmount
                var settlementAmt = cnAmtMap.get(it.id)?.paidAmount
                var remain = paidAmt.plus(settlementAmt?: BigDecimal.ZERO).setScale(7, RoundingMode.HALF_EVEN)

                if (it.amount <= remain) {

                    it.status = NoteStatus.PENDING
                    it.remainingAmount = it.amount
                    it.updatedOn = LocalDateTime.now()
                } else if (it.amount > settlementAmt && (remain > BigDecimal.ZERO)) {

                    it.status = NoteStatus.PARTIAL_REALIZED
                    it.remainingAmount = remain
                    it.updatedOn = LocalDateTime.now()
                }

            }

        }

        creditNoteRepo.saveAll(cn)

    }

    fun updateAdvPayAmt(advPay: List<AdvancePayment>, apAmtMap: Map<Long,AdvanceSettlementMapping>) {

        advPay.forEach {

            if (apAmtMap.containsKey(it.id)) {

                var remainingAmt = it.amountPending?: BigDecimal.ZERO
                var settlementAmt = apAmtMap.get(it.id)?.amt
                var remain = remainingAmt.plus(settlementAmt?: BigDecimal.ZERO).setScale(7, RoundingMode.HALF_EVEN)

                if (it.amount!! <= remain) {
                    it.amountPending = it.amount
                } else{
                    it.amountPending = remain
                }
                it.updatedOn = LocalDateTime.now()
                it.consumed = false

            }

        }

        advancePaymentRepo.saveAll(advPay)

    }
    fun checkAndCreateAdvance(user: String, settlement: Settlement, diff: BigDecimal){
        val excessPayment = paymentExcessRepository.getAdvanceByRefNum(settlement.paymentReference!!)
        if(excessPayment == null){
            paymentExcessService.handleAdvancePayment(diff, settlement.paymentReference!!, settlement.partnerDetailId!!, null, settlement.tenant, settlement.paymentType)
        }else{
            log.info("Advance CN ${excessPayment.creditNoteNumber} created for reference number ${excessPayment.utr} under settlement number ${settlement.settlementNumber}")
        }
    }

    fun getAllSettlementIdsByReferenceNumber(referenceNumber: String): List<Long> {
        var settlementIds = settlementRepo.getSettlementByReferenceNumber(referenceNumber)
                if(settlementIds.isNullOrEmpty()) rioDraftInvoiceRepo.getRioDraftReferenceNumbers(referenceNumber)?.let { draftIds ->
                    settlementIds = settlementRepo.getSettlementByReferenceNumber(draftIds)
                } ?: throw RequestException("Reference Number $referenceNumber not found")

        return settlementIds?: listOf()
    }

    fun createSettlementForDebitNotePayment(retailerDN: RetailerDebitNote, amount: BigDecimal, paymentReference: String, paymentMode: PaymentType, ds: String? = null, tenant: String, source: AdvancePaymentSource) {
        var cashLimitCheck = true
        var partnerInfo = supplierProxy.supplier(supplierId = listOf(retailerDN.partnerId))[0]!!
        var remainingAmount = retailerDN.amount - retailerDN.amountReceived
        var retailerDNCopy = retailerDN.copy()
        if(paymentMode == PaymentType.CASH){
            cashLimitCheck = checkCashSettlementAmount(retailerDNCopy.partnerDetailId, amount.toDouble())
        }
        if(cashLimitCheck){
            retailerDNCopy.amountReceived = retailerDNCopy.amountReceived.plus(amount.toDouble())
            var settlement = Settlement(
                0,
                null,
                null,
                null,
                retailerDNCopy.partnerId,
                partnerInfo.partnerName,
                remainingAmount,//remaining debit note amount before the current transaction
                amount.toDouble(),
                "Settled via RIO Payment",
                null,
                mutableListOf(),
                mutableListOf(),
                paymentMode,
                paymentReference,
                LocalDate.now(),
                retailerDNCopy.partnerId,
                retailerDNCopy.partnerDetailId,
                PartnerType.CUSTOMER,
                tenant,
                null,
                null,
                null,
                false,
                false,
                mutableListOf(),
                mutableListOf(),
                false,
                null,
                source,
                mutableListOf(retailerDNCopy),
                UUIDUtil.generateUuid()
            )
            save("SYSTEM", settlement, ds)
        }else{
            throw RequestException("Max cash limit reached for invoice ${retailerDNCopy.documentNumber} and partnerDetailId ${retailerDNCopy.partnerDetailId}")
        }
    }

    fun getSettlementByReferenceNumberAndPartnerDetailId(paymentReference: String, partnerDetailId: Long, tenant: String): List<Settlement>{
        return settlementRepo.getSettlementByReferenceNumberAndPartnerDetailId(paymentReference, partnerDetailId, tenant)
    }

    fun getSettlementsByReceiptNumber(receiptNumber: String): List<Settlement> {
        return settlementRepo.getSettlementsByReceiptNumber(receiptNumber)
    }

    fun createSettlementFromReceipt(
        receipt: Receipt,
        settlementMapping: DraftReceiptEntityMapping,
        settlementGroupNumber: SettlementGroupNumber?
    ): Settlement {
        val settleableProcessor = settleableProcessorFactory.getSettleableProcessor(settlementMapping.entityType)
        val settleableDetails = settleableProcessor.getSettleableDetails(settlementMapping.entityId)
            ?: throw RequestException("No invoice/DN found for id ${settlementMapping.entityId}")
        val settlement = settleableProcessor.constructSettlement(receipt, settleableDetails, settlementMapping)
        processSettlement(settlement, settlementGroupNumber)
        settleableProcessor.updateSettlementMapping(settlement, settleableDetails, settlementMapping)
        return settlement
    }

    /**
     * Processes the settlement
     * Currently, this method is not handling charges
     * Handles one invoice at a time (kafka flow)
     * To be updated for Web flow (multiple invoices)
     */
    private fun processSettlement(settlement: Settlement, settlementGroupNumber: SettlementGroupNumber?) {
        val user = settlement.createdBy!!
        val company = companyRepo.getCompanyByTenant(settlement.tenant)
            ?: throw RequestException("Company mapping not found for ${settlement.tenant}")
        if (settlement.charge) settlement.type = PartnerType.CUSTOMER
        settlement.settlementNumber = settlementGroupNumber?.next() ?: documentMasterService.getDocumentNumber(
            user,
            company.companyCode,
            if (settlement.type == PartnerType.CUSTOMER) DocumentType.CX_RECEIPT else DocumentType.VENDOR_PAYMENT
        )
        settlement.settlementGroupNumber = settlementGroupNumber?.number
        val savedSettlement = try {
            settlementRepo.save(settlement)
        } catch (_: DataIntegrityViolationException) {
            throw DetailedRequestException(BookkeeperErrors.ERROR, arrayOf("Duplicate Settlement"))
        }

        settlement.id = savedSettlement.id

    }

}
