package com.pharmeasy.service

import com.pharmeasy.data.DraftReceipt
import com.pharmeasy.model.ops.PartnerInfo
import com.pharmeasy.model.ops.PaymentInfo
import com.pharmeasy.service.abstracts.BaseReceiptProcessingStrategy
import com.pharmeasy.service.abstracts.SettleableProcessorFactory
import com.pharmeasy.service.strategy.PaymentProcessorStrategyFactory
import com.pharmeasy.type.AdvancePaymentSource
import com.pharmeasy.type.PaymentType
import com.pharmeasy.type.ReceiptStatus
import com.pharmeasy.type.ReceiptType
import com.pharmeasy.util.DateUtils
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class DraftReceiptProcessingStrategyImpl(
    private val companyService: CompanyService,
    private val documentService: DocumentMasterService,
    private val draftReceiptService: DraftReceiptService,
    private val paymentProcessorStrategyFactory: PaymentProcessorStrategyFactory,
    private val settleableProcessorFactory: SettleableProcessorFactory
) : BaseReceiptProcessingStrategy(
    companyService,
    documentService,
    paymentProcessorStrategyFactory,
    settleableProcessorFactory
) {
    override val source: AdvancePaymentSource = AdvancePaymentSource.RIO_PAY

    companion object {
        private val log = LoggerFactory.getLogger(DraftReceiptProcessingStrategyImpl::class.java)
    }

    @Transactional
    fun createDraftReceipt(
        payment: PaymentInfo,
        partnerInfo: PartnerInfo,
        preApprove: Boolean = false
    ): DraftReceipt {
        log.info("Creating receipt for payment: ${payment.customerTransactionId}")
        // Validate receipt details
        val errorRemark = validateReceiptRequest(payment, partnerInfo)

        val existingReceipt = draftReceiptService.getReceiptByPaymentTransactionId(payment.customerTransactionId)
        val draftReceipt =
            if (existingReceipt == null) {
                val receiptNumber = generateReceiptNumber(partnerInfo.tenant)
                val draft = constructDraftReceipt(receiptNumber, payment, partnerInfo, errorRemark, preApprove)
                draftReceiptService.saveDraftReceipt(draft)
            } else {
                existingReceipt
            }

        addSettlements(draftReceipt, payment, partnerInfo)
        return draftReceipt
    }

    /**
     * Saves the draft receipt and its entity mappings.
     * If the settlement amount is higher than the outstanding amount, it will be adjusted with the outstanding amount.
     */
    @Transactional
    fun addSettlements(
        draftReceipt: DraftReceipt,
        payment: PaymentInfo,
        partnerInfo: PartnerInfo
    ) {
        draftReceiptService.updateSettleableMappings(draftReceipt, payment, partnerInfo)
    }

    private fun constructDraftReceipt(
        receiptNumber: String,
        payment: PaymentInfo,
        partnerInfo: PartnerInfo,
        errorRemark: String?,
        preApprove: Boolean
    ): DraftReceipt {
        val transactionRefNumber =
            when (payment.paymentType) {
                PaymentType.CHEQUE -> payment.bankDetails!!.chequeNo
                PaymentType.NEFT -> payment.bankDetails!!.neftId
                else -> null
            }

        val status =
            if (!errorRemark.isNullOrBlank()) ReceiptStatus.REJECTED
            else if (preApprove) ReceiptStatus.APPROVED
            else ReceiptStatus.DRAFT
        val receiptType = determineReceiptType(payment, preApprove)
        val remarks = errorRemark ?: DEFAULT_RECEIPT_REMARK

        return DraftReceipt(
            receiptNumber = receiptNumber,
            paymentTransactionId = payment.customerTransactionId,
            remarks = remarks,
            amount = payment.transactionAmount,
            status = status,
            source = payment.paymentSource ?: this.source,
            tenant = partnerInfo.tenant,
            partnerId = partnerInfo.partnerId,
            partnerDetailId = partnerInfo.partnerDetailId,
            unUtilizedAmount = payment.transactionAmount,
            salesmanId = payment.salesmanId,
            salesmanName = payment.salesmanName,
            paymentType = payment.paymentType,
            txReferenceNumber = transactionRefNumber,
            transactionDate = payment.bankDetails?.chequeDate?.takeIf { payment.paymentType == PaymentType.CHEQUE },
            bankName = payment.bankDetails?.bankName,
            retailerTxnDate = DateUtils.getDateFromEpochStamp(payment.transactionDate),
            retailerName = partnerInfo.partnerName,
            isBankDeposit = payment.bankDetails?.isBankDeposit ?: false,
            bankDepositSlipNo = payment.bankDetails?.depositSlipNo,
            receiptType = receiptType
        )
    }

    private fun determineReceiptType(
        payment: PaymentInfo,
        preApprove: Boolean
    ): ReceiptType {
        return when {
            // Pure Advance Receipt if no invoice && initiated by WEB
            payment.paymentSource == AdvancePaymentSource.SYSTEM && payment.settleables.isEmpty() ->
                ReceiptType.ADVANCE_RECEIPT

            !preApprove -> ReceiptType.DIGITAL_RECEIPT
            else -> ReceiptType.RECEIPT
        }
    }
}
