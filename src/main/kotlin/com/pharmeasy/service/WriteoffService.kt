package com.pharmeasy.service

import com.pharmeasy.data.WriteOffLedger
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.*
import com.pharmeasy.repo.*
import com.pharmeasy.stream.InvoiceSettlementUpdateHandler
import com.pharmeasy.type.*
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime

@Service
class WriteoffService {
    @Autowired
    private lateinit var bkInvoiceRepo: BkInvoiceRepo
    @Autowired
    private lateinit var writeOffRepo: WriteOffRepo
    @Autowired
    private lateinit var creditNoteRepo: CreditNoteRepo
    @Autowired
    private lateinit var partnerService: PartnerService
    @Autowired
    private lateinit var companyService: CompanyService
    @Autowired
    private lateinit var invoiceSettlementUpdateHandler: InvoiceSettlementUpdateHandler
    @Autowired
    private lateinit var settlementService: SettlementService
    @Autowired
    private lateinit var slipService: SlipService

    companion object {
        private val log = LoggerFactory.getLogger(WriteoffService::class.java)
    }

    @Transactional
    fun invoiceWriteOff(writeOffEntryDataDto: InvoiceWriteOffDataDto, createdBy: String, tenant: String, ds: String? = null){
        var tenants=companyService.findTenants(ds?:tenant)
        var paidInvoiceIds = mutableListOf<String>()
        var createdBy = createdBy
        var invoiceData = writeOffEntryDataDto.invoiceData
        var writeoffData: MutableList<InvoiceWriteOffDto> = mutableListOf()
        var invoiceIdList: MutableList<WriteoffLedgerDto> = mutableListOf()
        log.debug("Write off for tenant $tenant")
        if(invoiceData.size>5){
            log.debug("More than 5 invoices not allowed")
        }else {
            invoiceData.forEach {
                var invoiceId = it.invoiceId
                var writeOffAmount = it.writeOffAmount
                var invoiceNum = it.invoiceNum
                var bkInvoiceData = bkInvoiceRepo.getByInvoiceId(invoiceId,invoiceNum,tenants)
                var totalAmount = bkInvoiceData!!.amount
                var paidAmount = bkInvoiceData.paidAmount
                var totalPendingAmount = totalAmount - paidAmount
                var id = bkInvoiceData.id
                try {
                    bkInvoiceData.status = InvoiceStatus.WRITE_OFF
                    bkInvoiceData.paidAmount = totalAmount
                    bkInvoiceData.updatedBy = createdBy
                    bkInvoiceData.updatedOn = LocalDateTime.now()
                    bkInvoiceRepo.save(bkInvoiceData)
                    paidInvoiceIds.add(bkInvoiceData.invoiceId!!)

                    var data = InvoiceWriteOffDto(
                        createdOn = LocalDateTime.now(),
                        createdBy = createdBy,
                        bkInvoiceId = id,
                        totalAmount = totalAmount,
                        totalPendingAmount = totalPendingAmount,
                        writeOffAmount = totalPendingAmount
                    )
                    invoiceIdList.add(WriteoffLedgerDto(id, totalPendingAmount))
                    writeoffData.add(data)
                }catch(e:Exception){log.debug("Error while saving invoice: ",e)}
            }
            if(writeoffData.isNotEmpty()){
                saveWritOffData(writeoffData)
                updateInvoiceLedger(invoiceIdList, createdBy, LocalDate.now())
            }
            invoiceIdList.forEach {
                settlementService.sendSettlementDataToRio(it.id, CreationType.INVOICE)
            }
            try {
                slipService.statusChangeSlip(paidInvoiceIds,SlipStatus.CLOSED,"Invoice write off")
            } catch (requestException: RequestException) {
                log.error("Failed to close slips due to request error: ${requestException.message}")
            } catch (e: Exception) {
                log.error("Failed to close slips due to an unexpected error: ${e.message}",e)
            }
        }
    }

    @Transactional
    fun saveWritOffData(writeoffData: MutableList<InvoiceWriteOffDto>) {
        log.debug("inside save write off data")
        try{
            writeoffData.forEach {
                var createdOn = it.createdOn
                var createdBy = it.createdBy
                var bkInvoiceId = it.bkInvoiceId
                var totalAmount = it.totalAmount
                var totalPendingAmount = it.totalPendingAmount
                var writeOffAmount = it.writeOffAmount
                var data = WriteOffLedger(
                    id = 0,
                    createdOn = createdOn,
                    updatedOn = createdOn,
                    createdBy = createdBy,
                    bkInvoiceId = bkInvoiceId,
                    totalAmount = totalAmount,
                    totalPendingAmount = totalPendingAmount,
                    writeOffAmount = writeOffAmount
                )
                writeOffRepo.save(data)
            }
        }catch (e: Exception){
            log.error("Error while saving write off invoice", e)
        }
    }

    fun updateInvoiceLedger(invoiceIdList: MutableList<WriteoffLedgerDto>, updatedBy: String, updatedOn: LocalDate) {
        log.debug("inside update invoice ledger")
        invoiceIdList.forEach {
            var id = it.id
            var amount = it.amount
            var invoice = bkInvoiceRepo.get(id)
            invoiceSettlementUpdateHandler.createSettlementConsumer(id, CreationType.INVOICE)
            val entryType =  LedgerEntryType.CREDIT

            var vendorLedger = VendorLedgerDto(
                transactionDate = updatedOn,
                vendorId = invoice?.partnerId!!,
                vendorName = invoice?.supplierName!!,
                ledgerEntryType = entryType,
                documentType = DocumentType.WRITE_OFF,
                documentNumber = invoice.invoiceNum!!,
                referenceNumber = invoice.invoiceNum!!,
                externalReferenceNumber = null,
                particulars = "WRITE_OFF",
                debitAmount = BigDecimal.ZERO,
                creditAmount = amount.toBigDecimal(),
                partnerDetailId = invoice.partnerDetailId,
                partnerId = invoice.partnerId,
                tenant = invoice.tenant!!,
                type = invoice.type,
                client = invoice.client
            )
            try{
                partnerService.addVendorLedgerEntry(updatedBy, vendorLedger)
            }catch (e:Exception){
                log.debug("Error in partner service",e)
            }
        }
    }

    fun cancelCreditNote(creditNoteWritOffDataDto: CreditNoteWritOffDataDto, updatedBy :String, tenant: String, ds: String? = null){
        var tenants=companyService.findTenants(ds?:tenant)
        var creditNoteWritOffData = creditNoteWritOffDataDto.creditNoteWriteOffData
        var updatedBy = updatedBy
        var updatedOn = LocalDateTime.now()
        var creditNoteIdList: MutableList<WriteoffLedgerDto> = mutableListOf()
        if(creditNoteWritOffData.size>5){
            log.debug("More than 5 credit notes are getting updated")
        }else {
            creditNoteWritOffData.forEach {
                var creditNoteId = it.creditNoteId
                var remarks = it.writeOffRemarks
                var creditNoteNum = it.creditNoteNum
                var cn = creditNoteRepo.getByCreditNoteIdAndNumber(creditNoteId, creditNoteNum)
                try {
                    creditNoteRepo.updateCreditNoteViaWriteoff(
                        remarks,
                        CreditNoteClosureType.WRITE_OFF,
                        NoteStatus.WRITE_OFF,
                        updatedOn,
                        updatedBy,
                        creditNoteId
                    )
                    creditNoteIdList.add(WriteoffLedgerDto(it.creditNoteId, cn!!.remainingAmount.toDouble()))

                } catch (e: Exception) {
                    log.error("Error while updating Credit note id $creditNoteId", e)
                }
            }
        }
        updateCreditNoteLedger(creditNoteIdList, updatedBy, updatedOn.toLocalDate())
    }


    fun updateCreditNoteLedger(creditNoteId: MutableList<WriteoffLedgerDto>, updatedBy: String, updatedOn: LocalDate) {
        creditNoteId.forEach {
            var id = it.id
            var amount = it.amount
            var cn = creditNoteRepo.get(id)
            val entryType = LedgerEntryType.DEBIT

            var vendorLedger = VendorLedgerDto(
                transactionDate = updatedOn,
                vendorId = cn?.partnerId!!,
                vendorName = cn?.supplierName,
                ledgerEntryType = entryType,
                documentType = DocumentType.WRITE_OFF,
                documentNumber = cn.creditNoteNumber!!,
                referenceNumber = cn.creditNoteNumber!!,
                externalReferenceNumber = null,
                particulars = "WRITE_OFF",
                debitAmount = amount.toBigDecimal(),
                creditAmount = BigDecimal.ZERO,
                partnerDetailId = cn.partnerDetailId,
                partnerId = cn.partnerId,
                tenant = cn.tenant!!,
                type = cn.type,
                client = cn.client
            )
            partnerService.addVendorLedgerEntry(updatedBy, vendorLedger)
        }
    }

    fun getWriteOffInvoiceReport(tenant: String, from: LocalDateTime?, to: LocalDateTime?, invoiceNumber: String?, partnerId: Long?, partnerDetailId: Long?): MutableList<WriteOffInvoiceReportDto>{
        var result : MutableList<WriteOffInvoiceReportDto> = mutableListOf()
        result = writeOffRepo.getWriteOffInvoices(tenant, from, to, partnerDetailId, partnerId, invoiceNumber)
        return result
    }

    fun getCancelCreditReport(tenant: String, from: LocalDateTime?, to: LocalDateTime?, creditNoteNumber: String?, partnerId: Long?, partnerDetailId: Long?): MutableList<CancelCreditReportDto>{
        var result: MutableList<CancelCreditReportDto> = mutableListOf()

        result= creditNoteRepo.getCancelledCreditNotes(tenant, from, to, partnerDetailId, partnerId, creditNoteNumber)

        return result
    }
}
