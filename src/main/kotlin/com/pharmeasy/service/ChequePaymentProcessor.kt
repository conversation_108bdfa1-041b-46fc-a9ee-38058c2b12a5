package com.pharmeasy.service

import com.pharmeasy.model.ops.PartnerInfo
import com.pharmeasy.model.ops.PaymentInfo
import com.pharmeasy.service.abstracts.PaymentProcessor
import org.springframework.stereotype.Service

@Service
class ChequePaymentProcessor(
    private val draftReceiptService: DraftReceiptService
) : PaymentProcessor(draftReceiptService) {
    override fun validatePayment(payment: PaymentInfo, partnerInfo: PartnerInfo): String? {
        super.validatePayment(payment, partnerInfo)?.let { return it }
        var remark =
            if (payment.bankDetails?.chequeNo.isNullOrBlank()) {
                "Cheque number is required for cheque payments"
            } else if (payment.bankDetails?.chequeDate == null) {
                "Cheque date is required for cheque payments"
            } else if (payment.bankDetails?.bankName.isNullOrBlank()) {
                "Bank name is required for cheque payments"
            } else {
                null
            }

        if(remark != null) return remark

        val chequeDate = payment.bankDetails!!.chequeDate!!
        val duplicateCheque =
            draftReceiptService
                .findDuplicateCheque(
                    payment.bankDetails.chequeNo!!,
                    chequeDate,
                    partnerInfo.partnerDetailId,
                    partnerInfo.tenant
                )
        if (duplicateCheque.isNotEmpty()) {
            remark = "Cheque number ${payment.bankDetails.chequeNo} already exists for the given date" +
                " in receipt: ${duplicateCheque[0].receiptNumber}"
        }
        return remark
    }
}
