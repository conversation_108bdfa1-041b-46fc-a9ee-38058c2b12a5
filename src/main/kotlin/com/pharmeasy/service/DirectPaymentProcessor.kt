package com.pharmeasy.service

import com.pharmeasy.model.ops.PartnerInfo
import com.pharmeasy.model.ops.PaymentInfo
import com.pharmeasy.service.abstracts.PaymentProcessor
import org.springframework.stereotype.Service

@Service
class DirectPaymentProcessor(
    draftReceiptService: DraftReceiptService
) : PaymentProcessor(draftReceiptService) {
    override fun validatePayment(payment: PaymentInfo, partnerInfo: PartnerInfo): String? {
        return null
    }
}
