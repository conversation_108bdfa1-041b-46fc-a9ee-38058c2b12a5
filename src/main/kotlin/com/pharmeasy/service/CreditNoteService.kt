package com.pharmeasy.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.pharmeasy.data.BkInvoice
import com.pharmeasy.data.BuyerDetail
import com.pharmeasy.data.Company
import com.pharmeasy.data.CompanyTenantMapping
import com.pharmeasy.data.CreditNote
import com.pharmeasy.data.CreditNoteReservation
import com.pharmeasy.data.CreditNoteReservationMapping
import com.pharmeasy.data.DebitCreditID
import com.pharmeasy.data.DebitCreditMapping
import com.pharmeasy.data.DebitNote
import com.pharmeasy.data.DebitNoteDetails
import com.pharmeasy.data.DraftCreditNote
import com.pharmeasy.data.DraftCreditNoteLog
import com.pharmeasy.data.DraftItem
import com.pharmeasy.data.DraftItemInvoice
import com.pharmeasy.data.DummyCreditNoteDetails
import com.pharmeasy.data.Einvoice
import com.pharmeasy.data.EinvoiceItem
import com.pharmeasy.data.FlatDiscountTask
import com.pharmeasy.data.FlatDiscountTaskDetail
import com.pharmeasy.data.SellerDetail
import com.pharmeasy.data.Settlement
import com.pharmeasy.data.SlabTask
import com.pharmeasy.data.SlabTaskDetail
import com.pharmeasy.data.ValueDetail
import com.pharmeasy.data.VendorDataEnum
import com.pharmeasy.data.VendorDataLinks
import com.pharmeasy.data.nsr.DraftNsr
import com.pharmeasy.data.nsr.DraftNsrItem
import com.pharmeasy.exception.BookkeeperErrors
import com.pharmeasy.exception.DetailedRequestException
import com.pharmeasy.exception.ProxyException
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.AggregatedCreditNoteDataDto
import com.pharmeasy.model.CreateResultData
import com.pharmeasy.model.CreditNoteClosureType
import com.pharmeasy.model.CreditNoteNumberAndTypeMappingDTO
import com.pharmeasy.model.CreditNotePdfEventDto
import com.pharmeasy.model.CreditNoteReceiptDto
import com.pharmeasy.model.CreditNoteReservationDTO
import com.pharmeasy.model.CreditNoteReservationPayloadDTO
import com.pharmeasy.model.CreditNoteReservationV2PayloadDTO
import com.pharmeasy.model.CreditNoteWithDebitNoteIds
import com.pharmeasy.model.Data
import com.pharmeasy.model.DiscountSettleInvoiceDto
import com.pharmeasy.model.EIClientResponseDto
import com.pharmeasy.model.EinvoiceKafkaDto
import com.pharmeasy.model.FirmDTO
import com.pharmeasy.model.PageableCreditnoteWithDebitNoteid
import com.pharmeasy.model.PaginationDto
import com.pharmeasy.model.PartnerGenericDetailDTO
import com.pharmeasy.model.ProductDto
import com.pharmeasy.model.RequestStatusDto
import com.pharmeasy.model.Result
import com.pharmeasy.model.RioReturnEventDto
import com.pharmeasy.model.SaleReturnCreditnoteDto
import com.pharmeasy.model.SaleReturnHeaderItem
import com.pharmeasy.model.SaleReturnItemsDto
import com.pharmeasy.model.SlabDiscountCreditNoteDto
import com.pharmeasy.model.Supplier
import com.pharmeasy.model.User
import com.pharmeasy.model.VendorCreditNoteDto
import com.pharmeasy.model.VendorLedgerDto
import com.pharmeasy.model.advancepayment.FlatDiscountCreditNoteDetail
import com.pharmeasy.model.advancepayment.FlatDiscountCreditNotePrintDto
import com.pharmeasy.model.creditnote.DiscountCreditNoteAdvice
import com.pharmeasy.model.creditnote.DiscountCreditNoteDto
import com.pharmeasy.model.creditnote.DraftNsrCreditNoteDto
import com.pharmeasy.model.creditnote.NsrCreditNotePrintDto
import com.pharmeasy.model.debitnote.DiscountDebitNoteDto
import com.pharmeasy.model.debitnote.NsrDebitNoteDto
import com.pharmeasy.model.einvoice.EInvoiceCreationKafkaDTOv2
import com.pharmeasy.model.item.NsrItemDto
import com.pharmeasy.model.success
import com.pharmeasy.proxy.CoreServiceProxy
import com.pharmeasy.proxy.EInvoiceProxy
import com.pharmeasy.proxy.InventoryProxy
import com.pharmeasy.proxy.InwardProxy
import com.pharmeasy.proxy.ReportProxy
import com.pharmeasy.proxy.RetailIoProxy
import com.pharmeasy.proxy.StoreProxy
import com.pharmeasy.proxy.SupplierProxy
import com.pharmeasy.proxy.UserProxy
import com.pharmeasy.proxy.WarehouseProxy
import com.pharmeasy.repo.BkInvoiceRepo
import com.pharmeasy.repo.BkItemRepo
import com.pharmeasy.repo.CheckerRepo
import com.pharmeasy.repo.CompanyLogsRepo
import com.pharmeasy.repo.CompanyRepo
import com.pharmeasy.repo.CompanyTenantMappingRepo
import com.pharmeasy.repo.CreditDataLinksRepo
import com.pharmeasy.repo.CreditNoteRepo
import com.pharmeasy.repo.CreditNoteReservationMappingRepo
import com.pharmeasy.repo.CreditNoteReservationRepo
import com.pharmeasy.repo.CreditNoteSettlementRepo
import com.pharmeasy.repo.DebitCreditMappingRepo
import com.pharmeasy.repo.DebitNoteDetailRepo
import com.pharmeasy.repo.DebitNoteRepo
import com.pharmeasy.repo.DraftCreditNoteLogRepo
import com.pharmeasy.repo.DraftCreditNoteRepo
import com.pharmeasy.repo.DraftItemInvoiceRepo
import com.pharmeasy.repo.DraftItemRepo
import com.pharmeasy.repo.DummyCreditNoteDetailRepo
import com.pharmeasy.repo.FlatDiscountTaskDetailRepo
import com.pharmeasy.repo.SlabTaskDetailRepo
import com.pharmeasy.repo.nsr.DraftNsrItemRepo
import com.pharmeasy.repo.nsr.DraftNsrRepo
import com.pharmeasy.repo.read.BkInvoiceReadRepo
import com.pharmeasy.service.einvoice.EInvoiceCreditNoteService
import com.pharmeasy.specification.DraftCreditNoteSpecification
import com.pharmeasy.specification.DraftNsrSpecification
import com.pharmeasy.specification.SupplierCreditNoteSpecification
import com.pharmeasy.stream.CnCreatePdfVaultSinkPusher
import com.pharmeasy.stream.EinvoiceSinkPusher
import com.pharmeasy.type.APIVersionType
import com.pharmeasy.type.AccountConstants
import com.pharmeasy.type.AdvancePaymentSource
import com.pharmeasy.type.CNReservationSource
import com.pharmeasy.type.CreditNoteReservationStatus
import com.pharmeasy.type.DebitNoteReturnEventType
import com.pharmeasy.type.DocumentType
import com.pharmeasy.type.EInvoiceType
import com.pharmeasy.type.InvoiceType
import com.pharmeasy.type.LedgerEntryType
import com.pharmeasy.type.LicenseType
import com.pharmeasy.type.NoteStatus
import com.pharmeasy.type.NoteTypes
import com.pharmeasy.type.OrderType
import com.pharmeasy.type.PartnerType
import com.pharmeasy.type.PaymentType
import com.pharmeasy.type.ReceiptStatus
import com.pharmeasy.type.Status
import com.pharmeasy.util.DateUtils.getISTDateTime
import com.pharmeasy.util.DateUtils.getUTCDateTime
import com.pharmeasy.util.EventPublisherUtil
import com.pharmeasy.util.SlabTaskUtils
import com.pharmeasy.util.UUIDUtil
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVPrinter
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.io.ByteArrayOutputStream
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalDateTime.now
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.*
import kotlin.math.floor

@Service
class CreditNoteService(@Value("\${app.retail-io.version}")val retailIoVersion: String,
                        @Value("\${app.retail-io.source}")val retailIoSource: String,
                        @Value("\${app.retail-io.key}")val retailIoKey: String) {
    companion object {
        private val log = LoggerFactory.getLogger(CreditNoteService::class.java)
        private const val DEFAULT_REASON_CODE = 13L //13 is default for reason code as other
    }

    @Autowired
    private lateinit var creditNoteRepo: CreditNoteRepo

    @Autowired
    private lateinit var bkItemRepo: BkItemRepo

    @Autowired
    private lateinit var debitNoteService: DebitNoteService

    @Autowired
    private lateinit var debitNoteRepo: DebitNoteRepo

    @Autowired
    private lateinit var debitCreditMappingRepo: DebitCreditMappingRepo

    @Autowired
    private lateinit var partnerService: PartnerService

    @Autowired
    private lateinit var companyTenantMappingRepo: CompanyTenantMappingRepo

    @Autowired
    private lateinit var supplierProxy: SupplierProxy

    @Autowired
    private lateinit var coreServiceProxy: CoreServiceProxy

    @Autowired
    private lateinit var s3FileUtilityService: S3FileUtilityService

    @Autowired
    private lateinit var creditDataLinksRepo: CreditDataLinksRepo

    @Autowired
    private lateinit var documentMasterService: DocumentMasterService

    @Autowired
    private lateinit var vendorFileService: VendorFileService

    @Autowired
    private lateinit var companyRepo: CompanyRepo

    @Autowired
    private lateinit var draftCreditNoteRepo: DraftCreditNoteRepo

    @Autowired
    private lateinit var checkerService: CheckerService

    @Autowired
    private lateinit var draftCreditNoteLogRepo: DraftCreditNoteLogRepo

    @Autowired
    private lateinit var warehouseProxy: WarehouseProxy

    @Autowired
    private lateinit var draftItemRepo: DraftItemRepo

    @Autowired
    private lateinit var bkInvoiceRepo: BkInvoiceRepo

    @Autowired
    private lateinit var draftNsrRepo: DraftNsrRepo

    @Autowired
    private lateinit var draftNsrItemRepo: DraftNsrItemRepo

    @Autowired
    private lateinit var reportProxy: ReportProxy

    @Autowired
    private lateinit var inwardProxy: InwardProxy

    @Autowired
    private lateinit var userProxy: UserProxy

    @Autowired
    private lateinit var debitNoteDetailRepo: DebitNoteDetailRepo

    @Autowired
    private lateinit var companyService: CompanyService

    @Autowired
    private lateinit var eInvoiceProxy: EInvoiceProxy

    @Autowired
    private lateinit var inventoryProxy: InventoryProxy

    @Autowired
    private lateinit var creditNoteSettlementRepo: CreditNoteSettlementRepo

    @Autowired
    private lateinit var slabTaskDetailRepo: SlabTaskDetailRepo

    @Autowired
    private lateinit var redisUtilityService: RedisUtilityService

    @Autowired
    private lateinit var creditNoteReservationRepo: CreditNoteReservationRepo

    @Autowired
    private lateinit var creditNoteReservationMappingRepo: CreditNoteReservationMappingRepo

    @Autowired
    private lateinit var storeProxy: StoreProxy

    @Autowired
    private lateinit var emailService: EmailService

    @Autowired
    private lateinit var einvoiceSinkPusher: EinvoiceSinkPusher

    @Autowired
    private lateinit var flatTaskDetailRepo: FlatDiscountTaskDetailRepo

    @Autowired
    private lateinit var retailIoProxy: RetailIoProxy

    @Autowired
    private lateinit var companyLogsRepo: CompanyLogsRepo

    @Autowired
    private lateinit var pushPdfEvent : CnCreatePdfVaultSinkPusher

    @Autowired
    private lateinit var englishNumberToWords: EnglishNumberToWords

    @Autowired
    private lateinit var bkInvoiceReadRepo: BkInvoiceReadRepo

    @Autowired
    private lateinit var invoiceService: InvoiceService

    @Autowired
    private lateinit var eventPublisherUtil: EventPublisherUtil

    @Autowired
    private lateinit var draftItemInvoiceRepo: DraftItemInvoiceRepo

    @Autowired
    private lateinit var settlementService: SettlementService

    @Autowired
    private lateinit var dummyCreditNoteDetailRepo: DummyCreditNoteDetailRepo

    @Autowired
    private lateinit var cnConfigService: CnConfigService

    @Autowired
    private lateinit var vaultCheckerService: CheckerService

    @Autowired
    private lateinit var bulkCnReasonCodeMappingService: BulkCnReasonCodeMappingService

    fun getCreditNotesForSupplier(id: Long, status: NoteStatus?, type: NoteTypes?, tenant: String, customerType: Boolean, ds: String? = null): List<CreditNoteWithDebitNoteIds>? {

        var tenants=companyService.findTenants(tenant)
        var custType:PartnerType = PartnerType.VENDOR
        if(customerType) custType = PartnerType.CUSTOMER

        var creditNoteWithDebitNoteIds = mutableListOf<CreditNoteWithDebitNoteIds>()
        if(tenants.isNullOrEmpty())  return creditNoteWithDebitNoteIds

        var statuses: MutableList<NoteStatus>? = null

        if (status != null) {
            statuses = mutableListOf()
            statuses.add(status)
            if (status == NoteStatus.PENDING) {
                statuses.add(NoteStatus.PARTIAL_REALIZED)
            }
        }


        val res = creditNoteRepo.getCreditNotesForSupplier(id, statuses, type,tenants,custType)

        if (res != null && !res.isEmpty()) {
            res.forEach { creditNote ->

                var debitNoteIds = mutableListOf<Long>()
                creditNote.debitNotes?.forEach { debitNoteIds.add(it.id) }

                creditNoteWithDebitNoteIds.add(CreditNoteWithDebitNoteIds(creditNote.id, creditNote.createdOn, creditNote.updatedOn, creditNote.createdBy, creditNote.updatedBy, creditNote.status, creditNote.noteType
                        , creditNote.amount, creditNote.partnerId?:creditNote.supplierId, creditNote.supplierName, creditNote.remarks, creditNote.settlementId, creditNote.receiptStatus, debitNoteIds, creditNote.invoiceId))
            }
        }
        return creditNoteWithDebitNoteIds
    }

    fun getCreditNotes(page: Int?, size: Int?, partnerId: Long?, invoiceId: Long?, debitNoteId: Long?, creditNoteId: Long?,
                       status: NoteStatus?, type: NoteTypes?, from: LocalDate?, to: LocalDate?, tenant: String?, customerType: Boolean, ds: String? = null): PageableCreditnoteWithDebitNoteid {
        log.debug("Inside getCreditNotes")

        var custType:PartnerType = PartnerType.VENDOR
        if(customerType) custType = PartnerType.CUSTOMER
        var fromDate: LocalDateTime? = null
        if (from != null) {
            fromDate = getUTCDateTime(from.atStartOfDay())
        }

        var toDate: LocalDateTime? = null
        if (to != null) {
            toDate = getUTCDateTime(to.atTime(23, 59, 59))
        }

        var tenants=companyService.findTenants(ds?:tenant!!)

        if(tenants.isNullOrEmpty()) return PageableCreditnoteWithDebitNoteid()

        var statuses: MutableList<NoteStatus>? = null

        if (status != null) {
            statuses = mutableListOf()
            statuses.add(status)
            if (status == NoteStatus.PENDING) {
                statuses.add(NoteStatus.PARTIAL_REALIZED)
            }
        }

        val page = PageRequest.of(if (page == null) 0 else page, if (size == null) 10 else size)
        val res = creditNoteRepo.getCreditNotes(partnerId, creditNoteId, statuses, type, fromDate, toDate, tenants,custType, page)

        if (res != null) {
            var creditNoteWithDebitNoteIds = mutableListOf<CreditNoteWithDebitNoteIds>()
            res.forEach { creditNote ->
                creditNote.createdOn = getISTDateTime(creditNote.createdOn)
                creditNote.updatedOn = getISTDateTime(creditNote.updatedOn)
                var debitNoteIds = mutableListOf<Long>()
                creditNote.debitNotes?.forEach { debitNoteIds.add(it.id) }

                var supplierList = supplierProxy.supplier(listOf(creditNote.partnerId))
                var supplier = if(supplierList.isNotEmpty()) supplierList.get(0) else null
                if(supplier != null ) creditNote.supplierName = supplier.partnerName!!

                creditNoteWithDebitNoteIds.add(CreditNoteWithDebitNoteIds(creditNote.id, creditNote.createdOn, creditNote.updatedOn,
                        creditNote.createdBy, creditNote.updatedBy, creditNote.status, creditNote.noteType,
                        creditNote.amount, creditNote.partnerId?:creditNote.supplierId,
                        supplier?.partnerName?:creditNote.supplierName, creditNote.remarks, creditNote.settlementId,
                        creditNote.receiptStatus, debitNoteIds, creditNote.invoiceId))

            }
            return PageableCreditnoteWithDebitNoteid(res.totalElements, res.totalPages, creditNoteWithDebitNoteIds)
        } else
            return PageableCreditnoteWithDebitNoteid()
    }

    fun getVendorCreditNotes(page: Int?, size: Int?, partnerId: Long, debitNoteNumber: String?, creditNoteNumber: String?,
                             receiptStatus: ReceiptStatus?, status: NoteStatus?, type: NoteTypes?, source: String?,
                             from: LocalDate?, to: LocalDate?, tenant: String, customerType: Boolean, ds: String? = null, returnEventType: DebitNoteReturnEventType?,distributorId: Long?): PaginationDto {
        log.debug("Inside getVendorCreditNotes : $receiptStatus : $status : $type : $debitNoteNumber : $creditNoteNumber : $returnEventType")
        // Update the return event type
        //var returnEventType =""
        var custType: PartnerType = PartnerType.VENDOR
        if (customerType) custType = PartnerType.CUSTOMER
        var sortBy = Sort.by(Sort.Direction.ASC, "createdOn").and(Sort.by(Sort.Direction.DESC, "amount"))
        val pagination = PageRequest.of(page ?: 0, size ?: 10, sortBy)

        val res: Page<CreditNote>

        var companyMapping = if(distributorId != null) companyService.getTenantByPDI(distributorId) else companyService.getCompanyTenantMappingObject(ds?:tenant)
        var tenants = companyService.findTenants(companyMapping!!.tenant)
        if (tenants.isEmpty()) throw RequestException("Your tenant: $tenant is not mapped to any Company")

        val companyTenant = companyService.getCompanyTenantMappingObject(ds?:tenant)

        var statuses: MutableList<NoteStatus>? = null

        if (status != null) {
            statuses = mutableListOf()
            statuses.add(status)
            if (status == NoteStatus.PENDING) {
                statuses.add(NoteStatus.PARTIAL_REALIZED)
            }
        }

        if (debitNoteNumber != null) {
            res =
                creditNoteRepo.getCreditNoteForDebitNoteNumber(debitNoteNumber, partnerId, statuses, pagination)
        } else if (creditNoteNumber != null) {
            res =
                creditNoteRepo.getCreditNoteForCreditNoteNumber(creditNoteNumber, partnerId, statuses, custType, pagination)
        } else if (returnEventType != null) {
            res = creditNoteRepo.getCreditNoteForReturnTypeSource(returnEventType, partnerId, statuses, custType, pagination)
        }
        else {
            res = creditNoteRepo.findAll(SupplierCreditNoteSpecification(partnerId, statuses, if (type != null) mutableListOf(type) else null, source, tenants, receiptStatus, from, to, custType, null), pagination)
        }
//        val supplierList = supplierProxy.supplier(listOf(partnerId))
//        val supplier = if (supplierList.isNotEmpty()) supplierList[0] else null
        var listOfCnIds = res.content.map { it.id }
        var listOfDN = mapOf<Long?, List<DebitNote?>>()
        if (!listOfCnIds.isNullOrEmpty()) {
                listOfDN =
                    debitNoteRepo.getDebitNotesForCreditNoteByListOfIds(listOfCnIds)?.groupBy { it?.creditNoteId }
                        ?: mapOf()
                val slabTaskDetailIds = res.content.map { it.slabTaskDetail?.id }
                val flatTaskDetailIds = res.content.map { it.flatTaskDetail?.id }
                val details =
                    slabTaskDetailRepo.getSlabCreditNoteDetail(slabTaskDetailIds).associateBy { it.slabTaskDetailId }

                val flatDiscountDetails =
                    flatTaskDetailRepo.getFlatCreditNoteDetail(flatTaskDetailIds).associateBy { it.taskDetailId }

                res.content.forEach { cn ->
                    val settlementNumbers: String? = creditNoteSettlementRepo.getSettlementNumbersForCN(cn.id)?.joinToString(", ")
                    cn.settlementNumbers = settlementNumbers
                    cn.tenant = companyTenant?.tenantName!!
                    cn.debitNotes =
                        if (listOfDN.containsKey(cn.id)) (listOfDN.get(cn.id) as MutableList<DebitNote>?) else mutableListOf()

                    if (cn.noteType == NoteTypes.SLAB_DISCOUNT_CN) {
                        if (cn.slabTaskDetail != null) {
                            val creditNoteDetail = details[cn.slabTaskDetail?.id]!!
                            val netSales = creditNoteDetail.netSales - creditNoteDetail.netSaleReturn
                            val taxableAmount =
                                netSales * BigDecimal.valueOf(creditNoteDetail.discountPercent / 10000.0)
                            val sgstOrCgst = taxableAmount * BigDecimal.valueOf(creditNoteDetail.cgst / 100.0)
                            val igst = taxableAmount * BigDecimal.valueOf(creditNoteDetail.igst / 100.0)


                            cn.slabDiscountDetail = SlabDiscountCreditNoteDto(
                                period = SlabTaskUtils.getPeriodString(
                                    creditNoteDetail.startDate,
                                    creditNoteDetail.endDate
                                ),
                                saleAmount = creditNoteDetail.netSales,
                                saleReturnAmount = creditNoteDetail.netSaleReturn,
                                netSales = netSales,
                                discountPercent = creditNoteDetail.discountPercent / 100.0,
                                taxableValue = taxableAmount,
                                partnerId = cn.partnerId!!,
                                partnerDetailId = cn.partnerDetailId!!,
                                cgst = sgstOrCgst,
                                sgst = sgstOrCgst,
                                igst = igst,
                                total = taxableAmount + sgstOrCgst + sgstOrCgst + igst,
                                createdOn = cn.createdOn?.toLocalDate()!!,
                                gstPercent = if (igst != BigDecimal.valueOf(0)) 18 else 9,
                                compName = "",
                                irn = cn.irn,
                                qrCode = cn.qrCode
                            )
                        } else if (cn.flatTaskDetail != null) {
                            cn.flatDiscountCnDetail = flatDiscountDetails.get(cn.flatTaskDetail?.id)
                        }
                    }
                    if (cn.noteType == NoteTypes.PURCHASE_SLAB_DISCOUNT_CN || cn.noteType == NoteTypes.OTC_MARGIN_LEAKAGE_DISCOUNT_CN || cn.noteType == NoteTypes.SALES_INCENTIVE_DISCOUNT_CN || cn.noteType == NoteTypes.OFFLINE_PROMOTION_DISCOUNT_CN || cn.noteType == NoteTypes.FINANCE_DISCOUNT_CN) {
                        cn.flatDiscountCnDetail = flatDiscountDetails.get(cn.flatTaskDetail?.id)

                    }

//                    if (supplier != null) cn.supplierName = supplier.partnerName!!
//                    log.debug("vendor name: ${cn.supplierName} : ${supplier?.partnerName}")
                }
            }
        return PaginationDto(res.totalElements, res.totalPages, res.hasPrevious(), res.hasNext(), res.content)
    }

    fun setDebitNotes(user: String, creditNote: CreditNote) {

       var mapDn = creditNote.debitItems.groupBy { it.debitNoteNumber }

        log.debug("Inside setDebitNotes")


        creditNote.debitNotes?.forEach { debitNote ->
            var totalAmt = mapDn.get(debitNote.debitNoteNumber)?.map { it.creditItemAmount }?.sumByDouble {it!!.toDouble()}?:0.0

            log.debug("total amt for : ${totalAmt}")

            debitNote.bkInvoices.forEach { bkInvoice ->
                bkInvoice.partnerId = creditNote.partnerId
                bkInvoice.items.forEach { bkItem ->
                    bkItem.bkInvoice = bkInvoice
                }
            }



//            if (debitNote.amountReceivable < remainingAmount) {
                debitNote.amountReceived += totalAmt!!.toBigDecimal()
//                remainingAmount -= debitNote.amountReceivable
//            } else {
//                debitNote.amountReceived = remainingAmount
//                remainingAmount = BigDecimal(0)
//            }

            debitNote.updatedOn = now()
            debitNote.updatedBy = user
            debitNote.creditNoteId = creditNote.id
            debitNote.supplierId = creditNote.partnerId!!

            log.debug("Debit Note ${debitNote.id}: amount receivable: ${debitNote.amountReceivable}, amoutn received: ${debitNote.amountReceived}")
//            log.debug("Credit Note ${creditNote.id}: remaining amount: $remainingAmount")
        }
    }

    fun updateDebitNotes(user: String, creditNote: CreditNote) {
        log.debug("Inside updateDebitNotes")
        var remainingAmount = creditNote.amount

        creditNote.debitNotes?.forEach { debitNote ->
            if (debitNote.amountReceivable < remainingAmount) {
                debitNote.amountReceived = debitNote.amountReceivable
                remainingAmount -= debitNote.amountReceivable
            } else {
                debitNote.amountReceived = remainingAmount
                remainingAmount = BigDecimal(0)
            }

            debitNote.creditNoteId = creditNote.id
            debitNote.status = NoteStatus.REALIZED

            log.debug("Debit Note ${debitNote.id}: amount receivable: ${debitNote.amountReceivable}, amoutn received: ${debitNote.amountReceived}")
            log.debug("Credit Note ${creditNote.id}: remaining amount: $remainingAmount")

            try {
                debitNoteService.update(user, debitNote.id, debitNote)
            } catch (e: Exception) {
                throw e
            }
        }
    }

//    fun updateItemsWithTrueEPR(creditNote: CreditNote): Boolean {
//        log.debug("Inside updateItemsWithTrueEPR")
//        if (creditNote.noteType == NoteTypes.DISCOUNT) {
//            val items = eprCalculationUtil.generateTrueEpr(creditNote.debitNotes!!, creditNote.amount)
//            try {
//               // bkItemRepo.saveAll(items)
////                if (items.isNotEmpty())
////                    bkItemRepo.updateTrueEprForItems(items)
//                items.forEach { bkItem ->
//                    val it = bkItemRepo.get(bkItem.id)
//                    if (it != null) {
//                        it.trueEpr = bkItem.trueEpr
//                        bkItemRepo.save(it)
//                    } else {
//                        log.error("item with id: ${bkItem.id} not found in DB")
//                    }
//                }
//                return true
//            } catch (e: Exception) {
//                throw e
//            }
//        }
//        return false
//    }

    fun getCreditNoteNumber(user: String, creditNote: CreditNote): String {
        log.debug("Creating credit note number for ${creditNote.id}: ${creditNote.tenant}")

        val company = companyRepo.getCompanyByTenant(creditNote.tenant!!) ?: throw RequestException("Company mapping not found for ${creditNote.tenant}")
        return documentMasterService.getDocumentNumber(user, company.companyCode, DocumentType.CREDIT_NOTE_PR)
    }

    fun updateDebitNotesForCreditNote(
        user: String,
        creditNote: CreditNote,
        rioReturnEventDto: Boolean = false,
        dummyCn: Boolean,
        isCopyEvent:Boolean? =false
    ): CreditNote {

        var listDnCnMapping = mutableListOf<DebitCreditMapping>()


        var cnAmt = BigDecimal.ZERO
        var totalDnAmt = BigDecimal.ZERO
        creditNote.debitNotes?.forEach { dn ->

            val dns = debitNoteRepo.get(dn.id) ?: throw DetailedRequestException(BookkeeperErrors.ERROR, arrayOf("Debit note ${dn.id} not found"))
//            log.debug("get DN value : ${ObjectMapper().writeValueAsString(dns)}")
            var dnAmt = BigDecimal.ZERO

            dns.creditNoteId = creditNote.id
            if (dn.status != NoteStatus.RECORDED) {
                //TODO uncomment below line for partial DN..
//                var itemNotUsed = debitNoteDetailRepo.getUnusedDebitNoteItemByDnId(dn.id)
                if((creditNote.type == PartnerType.CUSTOMER) && (creditNote.noteType != NoteTypes.DISCOUNT) && (creditNote.noteType != NoteTypes.NSR)) {
//                    log.debug("inside if customer : ${dns.amountReceived}")
                    dns.amountReceived = BigDecimal.ZERO
                }
                //TODO uncomment below line for partial DN..
//                dn.status = if(!itemNotUsed.isNullOrEmpty()) NoteStatus.PARTIAL_RECORDED else NoteStatus.RECORDED
                dns.status  = NoteStatus.RECORDED
                dns.updatedOn = now()
                dns.updatedBy = user
                dns.creditNoteNumber = creditNote.creditNoteNumber

                if((creditNote.type == PartnerType.CUSTOMER) && (creditNote.noteType != NoteTypes.DISCOUNT) && (creditNote.noteType != NoteTypes.NSR) && (isCopyEvent == false)) {
                    creditNote.debitItems.forEach {
                         if (it.debitnoteId!! == dn.id) {
                            var taxableValue = if (dn.client.name == "PE" || dn.client.name == "ML"){
                                getTaxableValue(it,null)
                            }
                            else {
                                if (dummyCn) {
                                    (it.taxableAmount?.divide(it.returnQty!!.toBigDecimal(), 5, RoundingMode.CEILING))!!.multiply((it.cnQty ?: 0).toBigDecimal())
                                } else {
                                    it.taxableAmount
                                }
                            }
//                        it.conversion = creditNote.conversion
                            if(!rioReturnEventDto){
                                if(it.gst != BigDecimal.ZERO) {
                                    it.cnTaxableValue = (it.conversion!!.toBigDecimal()
                                        .divide(BigDecimal(100), 5, RoundingMode.CEILING)).multiply(taxableValue!!)
                                }else{
                                    var netGstAmt = if(it.showGstSeparately == true)((maxOf(it.igst!!, (it.sgst!! + it.cgst!!))).divide(BigDecimal(100), 5, RoundingMode.CEILING)).multiply(taxableValue) else BigDecimal.ZERO
                                    var taxableAmt = it.taxableAmount?.minus(netGstAmt)
                                    it.cnTaxableValue = (it.conversion!!.toBigDecimal()
                                        .divide(BigDecimal(100), 5, RoundingMode.CEILING)).multiply(taxableAmt!!)

                                }
                            }

                            var netGstAmt = if(it.showGstSeparately == true)((maxOf(it.igst!!, (it.sgst!! + it.cgst!!))).divide(BigDecimal(100), 5, RoundingMode.CEILING)).multiply(taxableValue) else BigDecimal.ZERO
                            it.cnNetGST = if(!rioReturnEventDto){
                                if(creditNote.client == InvoiceType.EASY_SOL){
                                    it.conversion!!.toBigDecimal().divide(BigDecimal(100), 5, RoundingMode.CEILING).multiply(it.gst)
                                }else {
                                    (it.conversion!!.toBigDecimal()
                                        .divide(BigDecimal(100), 5, RoundingMode.CEILING)).multiply(netGstAmt)
                                }
                            } else it.cnNetGST

                            log.debug("netGST ${it.cnNetGST}  ${(it.conversion!!.toBigDecimal().divide(BigDecimal(100), 5, RoundingMode.CEILING)).multiply(netGstAmt)} ")
                            it.cnBillValue =if(!rioReturnEventDto) it.cnTaxableValue!! + it.cnNetGST!! else it.cnBillValue

                            it.cnMrpValue = if(!rioReturnEventDto) ((it.conversion!!.toBigDecimal().divide(BigDecimal(100), 5, RoundingMode.CEILING)).multiply(it.amount.toBigDecimal())).multiply(it.cnQty!!.toBigDecimal()) else it.cnMrpValue

                            cnAmt = cnAmt.plus(it.cnBillValue!!)

                            dnAmt = dnAmt.plus(it.cnBillValue!!)

                            it.creditnoteId =creditNote.id
                            if (!dummyCn) it.cnQty = it.returnQty
                        }
                    }

                    debitNoteDetailRepo.saveAll(creditNote.debitItems)
                    if(!rioReturnEventDto)
                    dns.amountReceived += dnAmt
                    totalDnAmt += dnAmt
                } else {
                    dnAmt = creditNote.amount
                    cnAmt = creditNote.amount
                }
            }
            else throw RequestException("Debit note number: ${dn.debitNoteNumber} is already RECORDED")

            if (cnAmt.equals(BigDecimal.ZERO)) cnAmt = creditNote.amount

            debitNoteRepo.save(dns)
            listDnCnMapping.add(DebitCreditMapping(DebitCreditID(dn.id,creditNote.id),creditNote.creditNoteNumber))

        }
        debitCreditMappingRepo.saveAll(listDnCnMapping)
        if((creditNote.type == PartnerType.CUSTOMER) && (creditNote.noteType != NoteTypes.DISCOUNT) && (creditNote.noteType != NoteTypes.NSR) && (isCopyEvent == false)) {

            if(cnAmt.setScale(2, BigDecimal.ROUND_HALF_EVEN) > totalDnAmt.setScale(2, BigDecimal.ROUND_HALF_EVEN)) throw RequestException("CN amount is greater then DN amount : $cnAmt > $totalDnAmt")
        }
        if(cnAmt.minus(creditNote.amount).abs() > BigDecimal.ONE && !dummyCn)
            throw RequestException("Total line item CN value doesn't match header level cn !")
        creditNote.amount = if(dummyCn ) BigDecimal(cnAmt.toInt()) else creditNote.amount
        creditNote.remainingAmount = creditNote.amount // only for creating CNs from DNs with less than 100% conversion
        return creditNote
    }

    private fun getTaxableValue(it: DebitNoteDetails,cnQty:Int?): BigDecimal =
        (it.abtMrp!! - (it.abtMrp!!.multiply(
            it.discountPercentage!!.divide(
                BigDecimal(100),
                5,
                RoundingMode.CEILING
            )
        ))).multiply(((cnQty?:it.returnQty?:0).toBigDecimal()))

    fun addVendorLedgerEntryForCreditNote(user: String, cn: CreditNote,invoiceNumber:String) {

        val entryType = when(cn.type) {
            PartnerType.CUSTOMER -> LedgerEntryType.CREDIT
            PartnerType.VENDOR -> LedgerEntryType.DEBIT
        }

        val vl = VendorLedgerDto(
                transactionDate = LocalDate.now(),
                vendorId = cn.partnerId!!,
                vendorName = cn.supplierName,
                ledgerEntryType = entryType,
                documentType = if (cn.noteType == NoteTypes.NSR || cn.noteType == NoteTypes.NSR_ACCEPTED || cn.noteType == NoteTypes.NSR_EXPIRED) {
                    DocumentType.CREDIT_NOTE_NSR
                } else if (cn.noteType == NoteTypes.DISCOUNT) {
                    if (cn.client == InvoiceType.VENDOR) DocumentType.VN_CN_DISCOUNT
                    else DocumentType.CX_CN_DISCOUNT
                } else if (cn.type == PartnerType.CUSTOMER) {
                    if (cn.noteType == NoteTypes.SR_ACCEPTED) DocumentType.CN_SR_ACCEPTED else if(cn.noteType == NoteTypes.SR_EXPIRED) DocumentType.CN_SR_EXPIRED else if(cn.noteType == NoteTypes.ICS_RETURN) DocumentType.CN_ICS_RETURN else  DocumentType.CN_ST_RETURN
                } else {
                    DocumentType.CREDIT_NOTE_PR
                },
                documentNumber = cn.creditNoteNumber!!,
                referenceNumber = cn.debitNotes?.get(0)?.debitNoteNumber,
                externalReferenceNumber = invoiceNumber,
                particulars = if (cn.noteType == NoteTypes.DISCOUNT) "CN For Discount"
                else if (cn.type == PartnerType.CUSTOMER) "CN Against Sale Return DN"
                else "CN Against Purchase Return DN",
                debitAmount = if (cn.type == PartnerType.CUSTOMER) BigDecimal.ZERO else cn.amount,
                creditAmount = if (cn.type == PartnerType.CUSTOMER) cn.amount else BigDecimal.ZERO,
                tenant = cn.tenant!!,
                partnerDetailId = cn.partnerDetailId,
                partnerId = cn.partnerId,
                type = cn.type,
                client = cn.client
        )

        partnerService.addVendorLedgerEntry(user, vl)
        val einvoiceKafka = if (cn.tenant!!.startsWith("ds")){
            val tenant = companyService.getTenantForDsTenant(cn.tenant!!)!!.tenant
            EinvoiceKafkaDto(cn = cn, tenant = tenant)
        }else {
            EinvoiceKafkaDto(cn = cn, tenant = cn.tenant!!)
        }
        //task: this to einvoiceKafakv2 ~ done

        log.info("sending einvoice event for credit note: ${cn.creditNoteNumber}, pdi: ${cn.partnerDetailId}, tenant: ${einvoiceKafka.tenant}")
        einvoiceSinkPusher.genericEInvoiceCreationProducer(convertPayloadToV2(einvoiceKafka))
    }



    @Transactional
    fun save(user: String, creditNote: CreditNote, tenant: String, partnerType: PartnerType=PartnerType.VENDOR, ds: String? = null,fsc:Boolean = false,rioReturnEventDto: RioReturnEventDto? = null, isCopyEvent: Boolean? =false): CreditNote {

        var tenants = companyService.findTenants(ds?:tenant)
        if(tenants.isNullOrEmpty()) throw RequestException("Your tenant: $tenant is not mapped to any Company")
        val company = companyRepo.getCompanyByTenant(tenants[0]!!) ?: throw RequestException("Company mapping not found for $tenant")

        if(!fsc) {
            if (ds!=null) creditNote.tenant = tenants[0]!!
        }

        if(partnerType.name == "VENDOR") {

            if (isProcessed(creditNote)) {
                throw RequestException("Duplicate Credit Note")
            }
        }

        var baseCNAmount = 0.00
        var roundOffAmount = 0.00
        creditNote.debitNotes?.forEach {
            baseCNAmount += it.baseDNAmount?:0.00
            roundOffAmount += it.roundOffAmount?:0.00
        }
        creditNote.createdBy = user
        creditNote.updatedBy = creditNote.createdBy
        creditNote.createdOn = now()
        creditNote.updatedOn = now()
        creditNote.partnerId = creditNote.partnerId
        creditNote.partnerDetailId = creditNote.debitNotes?.get(0)?.partnerDetailId

        creditNote.baseCNAmount = if(isCopyEvent==true){baseCNAmount}else{rioReturnEventDto?.baseCreditNoteAmount}
        creditNote.roundOffAmount = if(isCopyEvent==true){roundOffAmount}else{rioReturnEventDto?.roundOffAmount}

        if (creditNote.receiptStatus == ReceiptStatus.NOT_RECEIVED) {
            creditNote.expectedDate ?: throw RequestException("Please add expected date")
            creditNote.vendorCreditNoteAmount = null
            creditNote.vendorCreditNoteDate = null
            creditNote.vendorCreditNoteNumber = null
        }
        else {
            creditNote.expectedDate = null
        }

        val dnList  = creditNote.debitNotes

        if (!dnList.isNullOrEmpty() && dnList[0].noteType == NoteTypes.PURCHASE_RETURN) {
          //  val purchaseReturnId = debitNoteRepo.get(dnList[0].id)?.purchaseTransactionNumber
            val debitNotePrs = debitNoteService.getDebitNotePrByDebitNoteNumber(dnList[0].debitNoteNumber, tenant,ds)
            if (debitNotePrs.isEmpty()) throw RequestException("Purchase issues not found with debit note: ${dnList[0].debitNoteNumber}")

            val purchaseReturnId = debitNotePrs[0].purchaseTransactionNumber

            if (!purchaseReturnId.equals("0")) {
                val partnerDetailIdList = debitNoteRepo.getPartnerDetailId(purchaseReturnId, dnList[0].tenant)

                if(!partnerDetailIdList.isNullOrEmpty()) creditNote.partnerDetailId = partnerDetailIdList[0]
            }
        }


        val documentType = when {
            creditNote.noteType == NoteTypes.DISCOUNT && creditNote.type == PartnerType.CUSTOMER ->
                DocumentType.CX_CN_DISCOUNT
            creditNote.noteType in listOf(NoteTypes.NSR, NoteTypes.NSR_ACCEPTED, NoteTypes.NSR_EXPIRED) ->
                DocumentType.CREDIT_NOTE_NSR
            creditNote.noteType == NoteTypes.DISCOUNT && creditNote.type == PartnerType.VENDOR ->
                DocumentType.VN_CN_DISCOUNT
            creditNote.noteType == NoteTypes.SR_EXPIRED && creditNote.type == PartnerType.CUSTOMER ->
                if (creditNote.client.name == "RIO") DocumentType.CN_SR_EXPIRED_B2B else DocumentType.CN_SR_EXPIRED_B2C
            creditNote.noteType == NoteTypes.SR_ACCEPTED && creditNote.type == PartnerType.CUSTOMER ->
                if (creditNote.client.name == "RIO") DocumentType.CN_SR_ACCEPTED_B2B else DocumentType.CN_SR_ACCEPTED_B2C
            creditNote.noteType == NoteTypes.ICS_RETURN && creditNote.type == PartnerType.CUSTOMER ->
                DocumentType.CN_ICS_RETURN
            creditNote.noteType == NoteTypes.ST_RETURN && creditNote.type == PartnerType.CUSTOMER ->
                DocumentType.CN_ST_RETURN
            else ->
                DocumentType.CREDIT_NOTE_PR
        }

        creditNote.creditNoteNumber = documentMasterService.getDocumentNumber(user, company.companyCode, documentType)

        log.info("Going to save credit note ${creditNote.id}")
        var cn: CreditNote

        var consumedItems = mutableListOf<DebitNoteDetails>()



        try {
            cn = creditNoteRepo.save(creditNote)

            if(!dnList.isNullOrEmpty()){
                dnList.forEach {

                    it.debitItems.forEach { item ->

                        if (item.consumed == true) {
                            item.creditnoteId = cn.id
                            item.debitnoteId = it.id
                            consumedItems.add(item)
                        }
                    }
                }
            }

            var dnd = debitNoteDetailRepo.saveAll(consumedItems)

            cn.debitItems = dnd
            creditNote.debitItems = dnd
        } catch (e: Exception) {
            if (e is DataIntegrityViolationException)
                throw DetailedRequestException(BookkeeperErrors.ERROR, arrayOf("Duplicate Credit Note"))

            throw e
        }

//        setDebitNotes(user, cn)
        log.debug("Going to update Debit Notes for Credit Note")
        val updatedCreditNote = updateDebitNotesForCreditNote(user, cn,rioReturnEventDto != null,
            (creditNote.dummyCnAmt ?: BigDecimal.ZERO) > BigDecimal.ZERO,isCopyEvent
        )
        log.debug("Going to save credit note ${updatedCreditNote.id}")
        if(updatedCreditNote.type==PartnerType.CUSTOMER) {
            try {
                var customerCn = creditNoteRepo.save(updatedCreditNote)
                if(customerCn.noteType != NoteTypes.DISCOUNT && customerCn.noteType != NoteTypes.NSR && customerCn.noteType != NoteTypes.NSR_EXPIRED && customerCn.noteType != NoteTypes.NSR_ACCEPTED) {
                }
            } catch (e: Exception) {
                if (e is DataIntegrityViolationException)
                    throw DetailedRequestException(BookkeeperErrors.ERROR, arrayOf("Duplicate Credit Note"))
                throw e
            }
        }
        log.debug("add ledger entry")
        val ledgerEntryInvNum = if(isCopyEvent == true){""}else{
            cn.debitNotes?.get(0)!!.debitItems[0].invoiceNumber?:""
        }
        addVendorLedgerEntryForCreditNote(user, creditNote,ledgerEntryInvNum!!)
        //  updateItemsWithTrueEPR(cn)

        creditNote.debitNotes?.forEach {
            if(rioReturnEventDto == null && !it.logisticsPackageId.isNullOrEmpty())
            {
                eventPublisherUtil.createVaultCnPdfEvent(
                        CreditNotePdfEventDto(it.logisticsPackageId!!,cn.creditNoteNumber?:"",cn.amount,"",cn.noteType.name,cn.tenant!!,cn.partnerDetailId!!,null)
                )
            }
        }
        val isDummyCNCreated = createDummyCn(creditNote, company, cn, tenant, documentType, user)

        // Check if the credit note amount is 0 and if dummy CN is created
        if (updatedCreditNote.amount.compareTo(BigDecimal.ZERO) == 0 && isDummyCNCreated) {
            // Update the status to REALIZED
            updatedCreditNote.status = NoteStatus.REALIZED
            creditNoteRepo.save(updatedCreditNote)
            log.debug("Updated Credit Note status to REALIZED for Credit Note Number: ${updatedCreditNote.creditNoteNumber}")
        }

        return updatedCreditNote
    }

    fun createDummyCn(
        creditNote: CreditNote,
        company: Company,
        cn: CreditNote,
        tenant: String,
        documentType: DocumentType,
        user: String
    ): Boolean{
        if ((creditNote.dummyCnAmt ?: BigDecimal.ZERO) > BigDecimal.ZERO) {
            if (company.dummyAccountPdi != null) {
                val partner = supplierProxy.supplier(null, company.dummyAccountPdi)
                if (partner.isEmpty()) log.info("Unable to create the cn, partner Info not found for dummy account: ${company.dummyAccountPdi}")
                var dummyCreditNote = CreditNote(
                    id = 0,
                    createdOn = now(),
                    updatedOn = now(),
                    createdBy = creditNote.createdBy,
                    updatedBy = creditNote.createdBy,
                    status = NoteStatus.PENDING,
                    noteType = cn.noteType,
                    amount = creditNote.dummyCnAmt ?: BigDecimal.ZERO,
                    supplierId = partner[0]?.partnerId!!,
                    supplierName = partner[0]?.partnerName!!,
                    remarks = "Created For Dummy Account",
                    settlementId = null,
                    invoiceId = null,
                    debitNotes = null,
                    closureType = CreditNoteClosureType.MANUAL,
                    partnerId = partner[0]?.partnerId!!,
                    partnerDetailId = company.dummyAccountPdi,
                    tenant = tenant,
                    creditNoteNumber = documentMasterService.getDocumentNumber(
                        "SYSTEM",
                        company.companyCode,
                        documentType
                    ),
                    type = PartnerType.CUSTOMER,
                    client = creditNote.client,
                    receiptStatus = null,
                    expectedDate = null,
                    vendorCreditNoteDate = null,
                    vendorCreditNoteAmount = null,
                    vendorCreditNoteNumber = null,
                    slabTaskDetail = null,
                    flatTaskDetail = null,
                    referenceId = null,
                    referenceDnId = creditNote.debitNotes?.get(0)?.id
                )
                var dummyCn = creditNoteRepo.save(dummyCreditNote)
                var totalCnValue = BigDecimal.ZERO
                var dummyCreditNoteDetails= mutableListOf<DummyCreditNoteDetails>()
                creditNote.debitItems.forEach {
                    var taxableValue =  (it.taxableAmount?.divide(it.returnQty!!.toBigDecimal(), 5, RoundingMode.CEILING))!!.multiply(((it.returnQty?:0)-(it.cnQty?:0)).toBigDecimal())
                    var netGstAmt = if(it.showGstSeparately == true)((maxOf(it.igst!!, (it.sgst!! + it.cgst!!))).divide(BigDecimal(100), 5, RoundingMode.CEILING)).multiply(taxableValue) else BigDecimal.ZERO

                    dummyCreditNoteDetails.add(DummyCreditNoteDetails(
                        id = 0,
                        createdOn = now(),
                        updatedOn = now(),
                        creditNoteId = dummyCn.id,
                        batch = it.batch,
                        cgst = it.cgst,
                        sgst = it.sgst,
                        igst = it.igst,
                        hsn = it.hsn,
                        name = it.name,
                        mrp = it.amount,
                        ucode = it.ucode,
                        cnQty = (it.returnQty?:0).minus(it.cnQty?:0),
                        gst = it.gst,
                        cnTaxableValue = taxableValue,
                        cnNetGSTValue =  netGstAmt,
                        cnBillValue = taxableValue+netGstAmt
                    ))
                    totalCnValue += dummyCreditNoteDetails.last().cnBillValue?:BigDecimal.ZERO
                }
                dummyCn.amount = BigDecimal(totalCnValue.toInt())
                dummyCn.remainingAmount = BigDecimal(totalCnValue.toInt())
                dummyCreditNoteDetailRepo.saveAll(dummyCreditNoteDetails)
                creditNoteRepo.save(dummyCn)
                addVendorLedgerEntryForCreditNote(
                    user,
                    dummyCn,
                    cn.debitNotes?.get(0)!!.debitItems[0]?.invoiceNumber ?: ""
                )

                return true
            }else {
               throw RequestException("No dummy retailer tagged to this WH. Please have one added by the Support team to create a dummy CN")
            }
        }
        return false
    }

    fun isProcessed(creditNote: CreditNote): Boolean {
        log.debug("Inside isProcessed()")
        val existing = creditNoteRepo.findDuplicates(creditNote.partnerId!!, creditNote.createdBy, creditNote.status,
                creditNote.noteType, creditNote.amount, creditNote.tenant?:"")

        if (existing != null && existing.isNotEmpty()) {
            log.error("Credit note being saved is similar to an existing credit note")
            log.error("${existing[0]?.id}, ${existing[0]?.createdBy}, ${existing[0]?.status}, ${existing[0]?.noteType}, " +
                    "${existing[0]?.amount}")
            return true
        }

        return false
    }

    @Transactional
    fun updateCreditNoteReceiptStatus(user: String, id: Long, creditNoteReceiptDto: CreditNoteReceiptDto): CreditNote {
        log.debug("Inside updateCreditNoteReceiptStatus: $creditNoteReceiptDto")

        val cn = creditNoteRepo.get(id) ?: throw RequestException("credit note with id: $id not found")

        if (creditNoteReceiptDto.receiptStatus == ReceiptStatus.RECEIVED) {
            cn.vendorCreditNoteNumber = creditNoteReceiptDto.vendorCreditNoteNumber
            cn.vendorCreditNoteDate = creditNoteReceiptDto.vendorCreditNoteDate
            cn.vendorCreditNoteAmount = creditNoteReceiptDto.vendorCreditNoteAmount
        }
        else {
            cn.expectedDate = creditNoteReceiptDto.expectedDate
            cn.vendorCreditNoteNumber = null
            cn.vendorCreditNoteDate = null
            cn.vendorCreditNoteAmount = BigDecimal.ZERO
        }

        cn.receiptStatus = creditNoteReceiptDto.receiptStatus
        cn.remarks = creditNoteReceiptDto.remarks
        cn.updatedBy = user
        cn.updatedOn = now()

        return creditNoteRepo.save(cn)
    }

    @Transactional
    fun update(user: String, id: Long, creditNote: CreditNote): CreditNote {
        log.debug("Inside update() ${creditNote.id} : ${creditNote.status}")

        val cn = creditNoteRepo.get(id) ?: throw RequestException("credit note with id: $id not found")

        // Return if the credit note has already been saved.
        if (creditNote.status != NoteStatus.REALIZED && cn.status == NoteStatus.REALIZED) {
            throw RequestException("Credit note with id: $id already realized")
        }

        // We are only going to update the following fields and just ignore the rest.
        cn.invoiceId = creditNote.invoiceId
        cn.updatedBy = user
        cn.status = creditNote.status
        cn.remainingAmount = creditNote.remainingAmount

        if (creditNote.settlementId != null) cn.settlementId = creditNote.settlementId

        cn.updatedOn = now()
     //   cn.settlement = creditNote.settlement
        cn.closureType = if(creditNote.closureType!!.equals(CreditNoteClosureType.MANUAL)) CreditNoteClosureType.MANUAL else cn.closureType

        return creditNoteRepo.save(cn)
    }

    fun getById(id: Long): CreditNote? {
        log.debug("Inside getById")
        return  creditNoteRepo.findById(id).orElse(null)
    }

    fun getVendorCreditNotesData(partnerIds: List<Long>?, partnerId: Long?,partnerDetailId: Long?, startAt: LocalDate?, endAt: LocalDate?, noteType: NoteTypes?, tenant: String, page: Int?, size: Int?, customerType: Boolean, ds: String? = null,onClick:Boolean?,firmType:List<Int>?,client: List<InvoiceType>): PaginationDto {
        log.debug("Inside getVendorCreditNotesData $partnerIds: $tenant $customerType")
        var custType:PartnerType = PartnerType.VENDOR
        if(customerType) {custType = PartnerType.CUSTOMER}

        val today: LocalDateTime = if (endAt != null) {getUTCDateTime(endAt.atTime(23, 59, 59))} else {getUTCDateTime(LocalDate.now().atTime(23, 59, 59))}
        val from: LocalDateTime = if (startAt != null) {getUTCDateTime(startAt.atTime(0, 0, 0))} else {today.minusYears(10).minusMinutes(330)}
        var statuses = if(onClick == true){
            mutableListOf(NoteStatus.PARTIAL_REALIZED,NoteStatus.PENDING)
        }else{
            mutableListOf(NoteStatus.PARTIAL_REALIZED,NoteStatus.PENDING,NoteStatus.REALIZED)
        }
        var tenants = companyService.findTenants(ds?:tenant)
        if (tenants.isEmpty()) throw RequestException("Your tenant: $tenant is not mapped to any Company")
        log.debug("$from : $today : $tenant : $tenants : $partnerIds : ds $ds $custType")

        val pagination = PageRequest.of(page ?: 0, size ?: 10)
        var partnerIdFiltered: List<Long>?
        var getAllFlag: Boolean = false

        if(partnerId != null) {
            if(partnerIds != null) {
                partnerIdFiltered  = if(partnerIds.contains(partnerId)) listOf<Long>(partnerId) else listOf<Long>()
            } else {
                partnerIdFiltered = listOf<Long>(partnerId)
            }
        }else {
            if(!partnerIds.isNullOrEmpty()){
                partnerIdFiltered = partnerIds
            } else {
                partnerIdFiltered = listOf<Long>()
                getAllFlag = true
            }
        }

        var res:  Page<VendorCreditNoteDto>

        if(partnerIdFiltered.isNullOrEmpty()) {
            if(getAllFlag) {
                res = creditNoteRepo.getVendorCreditNotes(from, today, noteType, partnerDetailId, statuses, tenants, custType, client,pagination)
            } else {
                res = Page.empty<VendorCreditNoteDto>()
            }
        } else {
            res = creditNoteRepo.getVendorCreditNotesWithPartnerIds(partnerIdFiltered, noteType, partnerDetailId, from, today, tenants,custType,statuses, client,pagination)
        }
        log.debug("got CreditNoteService : response ++ res $res ")
        var companyMappingObj = companyTenantMappingRepo.getByTenant(tenant) ?:  throw RequestException("no tenant mapping found for $tenant")

        var allSupplierList = res.map { it.vendorId }.toMutableList()
        var supplierMap = supplierProxy.supplier(allSupplierList).associateBy { it?.partnerId }
        var vendorMap = mutableMapOf<Long,String>()
        if(customerType && !allSupplierList.isNullOrEmpty()){

            var vendorClient  = creditNoteRepo.getVendorClient(from, today, custType, tenants,allSupplierList)
            vendorClient.forEach {
                var listOfClient = vendorMap.get(it.vendorId)
                if(listOfClient== null){
                    vendorMap[it.vendorId!!] = it.client?:""
                }else{
                    vendorMap[it.vendorId!!] = listOfClient+"/"+it.client
                }

            }
        }
        res.forEach { vendorCreditNoteDto ->
            if (vendorCreditNoteDto.vendorId != null) {
                val supplier = supplierMap.get(vendorCreditNoteDto.vendorId)
                if (supplier != null) vendorCreditNoteDto.vendorName = supplier.partnerName!!
                if(customerType){
                    vendorCreditNoteDto.client  = vendorMap.get(vendorCreditNoteDto.vendorId?:0L)
                }
                log.debug("vendor name: ${supplier?.partnerName}")
            }
        }

        return PaginationDto(res.totalElements, res.totalPages, res.hasPrevious(), res.hasNext(), res)
    }

    fun getAggregatedCreditNoteData(partnerIds: List<Long>?, partnerId: Long?, partnerDetailId: Long?, startAt: LocalDate?, endAt: LocalDate?, noteType: NoteTypes?, tenant: String, customerType: Boolean, ds: String? = null,firmType:List<Int>?, client: List<InvoiceType>): AggregatedCreditNoteDataDto {
        log.debug("Inside getAggregatedInvoiceData $partnerIds : $tenant")
        var custType:PartnerType = PartnerType.VENDOR
        if(customerType) custType = PartnerType.CUSTOMER

        val today: LocalDateTime = if (endAt != null) {getUTCDateTime(endAt.atTime(23, 59, 59))} else {getUTCDateTime(LocalDate.now().atTime(23, 59, 59))}
        val from: LocalDateTime = if (startAt != null) {getUTCDateTime(startAt.atTime(0, 0, 0))} else {getUTCDateTime(today.minusYears(10))}

        var tenants = companyService.findTenants(ds?:tenant)
        if (tenants.isEmpty()) throw RequestException("Your tenant: $tenant is not mapped to any Company")
        log.debug("$from : $today : $tenant : $tenants : $ds")

        var res = AggregatedCreditNoteDataDto(0L, 0.0)
        val mapper = jacksonObjectMapper()
        var creditNoteAggregatedDataCacheKey = "bookkeeper_credit_note_aggregated_data_${tenant}_${custType}_${ds}"
        if(partnerIds.isNullOrEmpty() && noteType == null && partnerId == null && startAt == null && endAt == null && firmType.isNullOrEmpty()){
            // using cache only for summary page - not for other filter lookups
            var cacheResponseString = redisUtilityService.getfromRedis(creditNoteAggregatedDataCacheKey)
            if(!cacheResponseString.isNullOrEmpty()){
                var cacheResponseDTO: AggregatedCreditNoteDataDto = mapper.readValue(cacheResponseString)
                return cacheResponseDTO
            }
        }

        var partnerIdFiltered: List<Long>?
        var getAllFlag: Boolean = false

        if(partnerId != null) {
            if(partnerIds != null) {
                partnerIdFiltered  = if(partnerIds.contains(partnerId)) listOf<Long>(partnerId) else listOf<Long>()
            } else {
                partnerIdFiltered = listOf<Long>(partnerId)
            }
        }else {
            if(!partnerIds.isNullOrEmpty()){
                partnerIdFiltered = partnerIds
            } else {
                partnerIdFiltered = listOf<Long>()
                getAllFlag = true
            }
        }

        try {
            if(partnerIdFiltered.isNullOrEmpty()) {
                if(getAllFlag) {
                    res = creditNoteRepo.getAggregatedCreditNoteData(from, today, noteType, partnerDetailId, tenants,custType,client)
                    if(noteType == null && startAt == null && endAt == null){
                        redisUtilityService.setToRedis(creditNoteAggregatedDataCacheKey, mapper.writeValueAsString(res), null)
                        log.debug("Cache miss for creditnote aggregated data $creditNoteAggregatedDataCacheKey. Cache set at ${now()}")
                    }
                } else {
                    res = AggregatedCreditNoteDataDto()
                }
            } else {
                res = creditNoteRepo.getAggregatedCreditNoteDataWithPartnerIds(partnerIdFiltered, noteType, partnerDetailId, from, today, tenants,custType,client)
            }
        }
        catch (e: Exception) {
            res = AggregatedCreditNoteDataDto(0L, 0.0)
        }

        log.debug("aggregated data: $res")
        return res
    }

// credit note url

    fun getCreditNoteURL(tenant: String, createdBy: String?, customerType: Boolean, ds: String? = null,firmType:List<Int>?,startAt: LocalDate?,endAt: LocalDate?,client: List<InvoiceType>): CreateResultData {
        log.debug("inside getCreditNoteURL")
        var custType:PartnerType = PartnerType.VENDOR
        if(customerType) custType = PartnerType.CUSTOMER
        var tenants=companyService.findTenants(ds?:tenant)
        if(tenants.isNullOrEmpty()) return CreateResultData(200,"No Data",null)
        //val list = creditNoteRepo.getVendorCreditNotesForUrl(tenants,custType)
        val to: LocalDateTime = if (endAt != null) {endAt.atTime(23, 59, 59)} else {LocalDate.now().atTime(23, 59, 59)}
        val from: LocalDateTime = if (startAt != null) {startAt.atTime(0, 0, 0)} else {to.minusYears(10)}

        var list : MutableList<VendorCreditNoteDto?> = mutableListOf()
        if(client==null){
            list = creditNoteRepo.getVendorCreditNotesForUrl(tenants,custType,from,to)
        }else if(client!=null && from !=null && to !=null){
            list = creditNoteRepo.getVendorCreditNotesDataForUrlWithPidANDDate(custType,tenants,client,from,to)
        }else if (client!=null && from==null && to==null){
            list = creditNoteRepo.getVendorCreditNotesForUrlWithPid(tenants,custType,client)
        }


        if (list.isNullOrEmpty())
            throw RequestException("Data is not available")
        var prefix = "CreditNote"
        try {
            var file = vendorFileService.saveReport(tenants[0]?:tenant, VendorDataLinks(null, now(),null,"IN_PROGRESS",VendorDataEnum.CREDITNOTE,null,createdBy,tenant,customerType))
            writeCreditNoteData(file.id!!, list, tenants[0]?:tenant,prefix,customerType)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return  CreateResultData(200,"Success",null)
    }


    @Async
    fun writeCreditNoteData(id: Long, successList: MutableList<VendorCreditNoteDto?>, tenant: String,prefix:String?,customerType: Boolean) {
        log.debug("Inside writeCreditNoteData: writing data into csv")

        val csvPrinter: CSVPrinter
        var bytes = ByteArrayOutputStream()

        if(customerType) {
             csvPrinter = CSVPrinter(bytes.bufferedWriter(), CSVFormat.DEFAULT
                    .withHeader("Customer Id", "Customer Name", AccountConstants.CUSTOMER_TYPE, "No Of Credit Notes", "No Of Realized Credit Notes", "No Of Pending Credit Notes", "Pending Credit Note AMT"))
        }else{
            csvPrinter = CSVPrinter(bytes.bufferedWriter(), CSVFormat.DEFAULT
                    .withHeader("Vendor Id", "Vendor Name", "No Of Credit Notes", "No Of Realized Credit Notes", "No Of Pending Credit Notes", "Pending Credit Note AMT"))
        }
        var companyMappingObj = companyTenantMappingRepo.getByTenant(tenant) ?:  throw RequestException("no tenant mapping found for $tenant")
        var supplierList: List<Supplier?>? = null
        successList!!.forEach {
            if (it != null) {
                if (it.vendorId != null)
                    supplierList = supplierProxy.supplier(listOf(it.vendorId))
                val supplier = if (supplierList!!.isNotEmpty()) supplierList!![0] else null
                if (supplier != null) it.vendorName = supplier.partnerName!!
                if(customerType){
                    csvPrinter.printRecord(it.vendorId, it.vendorName, it.client, it.totalCreditNotes, it.totalRealizedCreditNotes, it.totalPendingCreditNotes, it.totalPendingCreditNoteAmount)
                }else
                    csvPrinter.printRecord(it.vendorId, it.vendorName, it.totalCreditNotes, it.totalRealizedCreditNotes, it.totalPendingCreditNotes, it.totalPendingCreditNoteAmount)
            }
        }
        csvPrinter.flush()
        val uploaded = s3FileUtilityService.uploadFile(bytes.toByteArray(), "CSV", prefix)
        vendorFileService.update(id,uploaded?: "Failed")
    }


    fun getCreditNoteDownload(createdBy: String, tenant: String, customerType: Boolean, ds: String? = null): CreateResultData {

        var tenants=companyService.findTenants(ds?:tenant)

        var file = creditDataLinksRepo.getCreditNoteLink(createdBy,tenants[0]?:tenant,customerType)
        if (file.isNotEmpty()) {
            return  CreateResultData(200,"Success", file[0]!!.link)
        }

        return  CreateResultData(200,"Success",null)
    }

//credit note detail url
    fun getCreditNoteDetailURL(partnerId: Long?, tenant: String, createdBy: String?, from: LocalDate?, to: LocalDate?, status: NoteStatus?, customerType: Boolean, ds: String? = null): CreateResultData {
        log.debug("inside getCreditNoteDetailURL")

        var custType:PartnerType = PartnerType.VENDOR
        if(customerType) custType = PartnerType.CUSTOMER
        var tenants=companyService.findTenants(ds?:tenant)
        if (tenants.isEmpty()) throw RequestException("Your tenant: $tenant is not mapped to any Company")
        var statuses: MutableList<NoteStatus>? = null

        if (status != null) {
            statuses = mutableListOf()
            statuses.add(status)
            if (status == NoteStatus.PENDING) {
                statuses.add(NoteStatus.PARTIAL_REALIZED)
            }
        }

        val list = creditNoteRepo.findAll(SupplierCreditNoteSpecification(partnerId, statuses, null, null, tenants, null, from, to,custType))

        if (list.isNullOrEmpty())
            throw RequestException("Data is not available")
        var prefix = "CreditNoteDetail"
        try {
            var file = vendorFileService.saveReport(tenants[0]!!, VendorDataLinks(null, now(),null,"IN_PROGRESS",VendorDataEnum.CREDITNOTE_DETAIL,null,createdBy,tenant,customerType))
            writeCreditNoteDetailData(file.id!!, list, tenants[0]!!,prefix,customerType)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return  CreateResultData(200,"Success",null)
    }

    @Async
    fun writeCreditNoteDetailData(id: Long, successList: MutableList<CreditNote?>, tenant: String,prefix:String?,customerType: Boolean) {
        log.debug("Inside writeCreditNoteDetailData: writing data into csv")

        val csvPrinter: CSVPrinter
        var bytes = ByteArrayOutputStream()
        if(customerType) {
             csvPrinter = CSVPrinter(bytes.bufferedWriter(), CSVFormat.DEFAULT
                    .withHeader("Credit Note No.", "Created On", "Debit Note No.", "Credit Note Type", "Open CN Amount", "Total CN Amount", "Document Status", "Source of Return"))
                    // .withHeader("Credit Note No.", "Created On", "Debit Note No.", "Credit Note Type", "Debit Note Amount", "Credit Note Amount", "Document Status", "Source of Return"))
        }else{
             csvPrinter = CSVPrinter(bytes.bufferedWriter(), CSVFormat.DEFAULT
                    .withHeader("Credit Note No.", "Created On", "Debit Note No.", "Credit Note Type", "Debit Note Amount", "Credit Note Amount", "Receipt Status", "Receipt Amount", "Document Status"))
        }
        successList.forEach {
            var amtt: BigDecimal = BigDecimal.ZERO
            if (it != null) {
                it.debitNotes = debitNoteService.getDebitNotesForCreditNote(it.id).toMutableList()
                val dbn = StringBuffer()
                val returnSource = StringBuffer()
                it.debitNotes!!.forEach { db ->
                   dbn.append(db.debitNoteNumber+",")
                   amtt = amtt.plus(db.amountReceivable)
                    returnSource.append(db.returnEventType.toString() + ",")
                }
                if(customerType) {
                    log.debug("Inside If block 1 $customerType and Return Source : $returnSource ")
                    csvPrinter.printRecord(it.creditNoteNumber, it.createdOn?.toLocalDate(), dbn, it.noteType, it.remainingAmount, it.amount, it.status, returnSource)
                    //csvPrinter.printRecord(it.creditNoteNumber, it.createdOn?.toLocalDate(), dbn, it.noteType, amtt, it.amount, it.status)
                }else if(it.noteType.name.equals(NoteTypes.DISCOUNT.name)){
                    csvPrinter.printRecord(it.creditNoteNumber, it.createdOn?.toLocalDate(), dbn, it.noteType, amtt, it.amount,null, it.vendorCreditNoteAmount, it.status)
                }else{
                    csvPrinter.printRecord(it.creditNoteNumber, it.createdOn?.toLocalDate(), dbn, it.noteType, amtt, it.amount,it.receiptStatus, it.vendorCreditNoteAmount, it.status)
                }
            }
        }
        csvPrinter.flush()
        val uploaded = s3FileUtilityService.uploadFile(bytes.toByteArray(), "CSV", prefix)
        vendorFileService.update(id,uploaded?: "Failed")
    }

    fun getCreditNoteDetailDownload(createdBy: String, tenant: String, customerType: Boolean, ds: String? = null): CreateResultData {

        var tenants=companyService.findTenants(ds?:tenant)

        var file = creditDataLinksRepo.getCreditNoteDetailLink(tenants[0]?:tenant,createdBy,customerType)
        if (file.isNotEmpty()) {
            return  CreateResultData(200,"Success", file[0]!!.link)
        }

        return  CreateResultData(200,"Success",null)
    }

    fun checkItemDiscountQuantity(draftItems: List<DraftItem>) {

        val itemMap = hashMapOf<Long, Long>()
        draftItems.forEach { item ->
            val sumItemDiscountQuantity: Long = if (itemMap.containsKey(item.itemId)) {
                itemMap[item.itemId]!!.plus(item.discountQuantity!!)
            } else {
                item.discountQuantity!!
            }

            itemMap[item.itemId] = sumItemDiscountQuantity
        }

        log.debug("Item map : $itemMap")

        itemMap.keys.forEach { itemId ->
            val exDiscountQuantity = draftItemRepo.getDiscountQuantityByBkItemId(itemId) ?: 0

            val bkItem = bkItemRepo.getByItemId(itemId)
                    ?: throw RequestException("Bk Item with item id: $itemId not found")

            val totalQuantity = bkItem.quantityScheme!!.add(BigDecimal(bkItem.quantityOrdered!!)).toLong()

            log.debug("existing discount item quantity: $exDiscountQuantity")
            log.debug("total new discount item quantity: $totalQuantity")

            if (itemMap[itemId]!! + exDiscountQuantity > totalQuantity)
                throw RequestException("Item quantity for  Item id: $itemId has been exhausted. Current remaining limit - ${totalQuantity - exDiscountQuantity}")
        }
    }

    fun getDiscountQuantity(id: Long): Long {
        log.debug("Inside getUsedQuantity for BkItem id: $id")

        return draftItemRepo.getDiscountQuantityByBkItemId(id) ?: 0
    }

    @Transactional
    fun createDiscountCreditNoteEntry(user: String, discountCreditNoteDto: DiscountCreditNoteDto, ds: String? = null): DraftCreditNote {
        log.debug("Creating discount credit note $discountCreditNoteDto")

        var companyMappingObj = companyService.getCompanyTenantMappingObject(ds?:discountCreditNoteDto.tenant)
                ?: throw RequestException("no tenant mapping found for ${discountCreditNoteDto.tenant}")
        if(discountCreditNoteDto.client != InvoiceType.RIO && discountCreditNoteDto.mode == "ptr"){
            throw RequestException("PTR mode is enabled for RIO client invoices only")
        }

        val docType = if (discountCreditNoteDto.client == InvoiceType.VENDOR) DocumentType.VN_CN_DISCOUNT else DocumentType.CX_CN_DISCOUNT
        var invoiceObject = bkInvoiceRepo.getByInvoiceNum(discountCreditNoteDto.invoiceNumber, mutableListOf(companyMappingObj.tenant))
        if(invoiceObject.isEmpty()) throw RequestException("Invoice ${discountCreditNoteDto.invoiceNumber} not found!")
        var draftCreditNote = DraftCreditNote (
                id = 0,
                createdBy = user,
                createdOn = now(),
                updatedBy = user,
                updatedOn = now(),
                transactionDate = null,
                partnerName = discountCreditNoteDto.partnerName,
                partnerId = discountCreditNoteDto.partnerId,
                partnerDetailId = discountCreditNoteDto.partnerDetailId,
                documentType = docType,
                amount = discountCreditNoteDto.amount,
                companyId = companyMappingObj.companyId,
                status = Status.PENDING_APPROVAL,
                type = discountCreditNoteDto.type,
                client = discountCreditNoteDto.client,
                creditNoteNumber = null,
                approvalDate = null,
                assignedTo = null,
                assignedToId = null,
                sender = discountCreditNoteDto.senderName,
                senderId = user,
                tenant = companyMappingObj.tenant,
                items = mutableListOf(),
                invoiceNumber = discountCreditNoteDto.invoiceNumber,
                updatedByName = discountCreditNoteDto.senderName,
                makerRemarks = discountCreditNoteDto.makerRemarks,
                checkerRemarks = discountCreditNoteDto.checkerRemarks,
                mode = discountCreditNoteDto.mode?:"mrp",
                invoiceSettled = discountCreditNoteDto.settleInvoice
        )

        var checkers = checkerService.findCheckers(discountCreditNoteDto.tenant,null,ds)

        if (checkers.isNotEmpty()) {

            var checker = checkers[0]
            draftCreditNote.assignedTo = checker?.userName
            draftCreditNote.assignedToId = checker?.userId

//                emailService.sendMessage("Discount credit note Review","Please review new discount credit note entry in vault",checker?.email!!,null)
        } else {
            throw RequestException("No checker found for this company !")
        }

        val dcn = draftCreditNoteRepo.save(draftCreditNote)

        try {
            draftCreditNoteLogRepo.save(DraftCreditNoteLog(null, now(), user, dcn.status, dcn.id))
        } catch (e: Exception) {
            log.debug("failed to log discount credit note")
        }

        val draftItems = mutableListOf<DraftItem>()

        discountCreditNoteDto.items.forEach { item ->
            val draftItem = DraftItem(
                    id = 0,
                    createdOn = now(),
                    createdBy = user,
                    updatedOn = now(),
                    updatedBy = user,
                    name = item.name,
                    ucode = item.ucode,
                    batch = item.batch,
                    mrp = item.mrp,
                    discountQuantity = item.discountQuantity,
                    discountPercentage = item.discountPercentage,
                    discountAmount = item.discountAmount,
                    draftCreditNoteId = dcn.id,
                    itemId = item.bkItemId,
                    invoiceNumber = item.invoiceNumber,
                    gstPercent = item.gstPercent,
                    gstAmount = item.gstAmount,
                    discountTaxTotal = item.discountTaxTotal
            )

            draftItems.add(draftItem)
        }
        dcn.items = draftItemRepo.saveAll(draftItems)
        val draftItemInvoiceList = mutableListOf<DraftItemInvoice>()
        if(discountCreditNoteDto.settleInvoice){
            discountCreditNoteDto.settleInvoiceList!!.forEach {
                val draftInvoice = DraftItemInvoice(
                    id = 0,
                    invoiceNumber = it.invoiceNumber,
                    createdOn =  now(),
                    draftId = dcn.id,
                    paidAmount = it.paidAmount,
                    bkInvoiceId = it.bkInvoiceId
                )
                draftItemInvoiceList.add(draftInvoice)
            }

           draftItemInvoiceRepo.saveAll(draftItemInvoiceList)
        }


        return dcn
    }

    @Transactional
    fun updateDraftCreditNote(draftCreditNote: DraftCreditNote, user: String, username: String, tenant: String, ds: String? = null): DraftCreditNote {
        log.debug("Inside updateDraftCreditNote : $draftCreditNote")

        val ex: DraftCreditNote
        try {
            ex = draftCreditNoteRepo.getOne(draftCreditNote.id)
        }
        catch (e: Exception) {
            throw RequestException("draft credit note with id: ${draftCreditNote.id} not found")
        }

        if (ex.status != Status.REVIEW) throw RequestException("Incorrect state of the adjustment entry : ${ex.status}")

        ex.updatedBy = user
        ex.updatedByName = username
        ex.updatedOn = now()
        ex.status = Status.PENDING_APPROVAL
        ex.makerRemarks = draftCreditNote.makerRemarks
        ex.amount = draftCreditNote.amount

        draftItemRepo.deleteItems(ex.id)

        var checkers = checkerService.findCheckers(tenant,null,ds)

        if (checkers.isNotEmpty()) {

            var random = Random()
            val index = random.nextInt(checkers.size)
            var checker = checkers[index]
            if (checker?.userId.equals(user)) throw RequestException("Users having Checker rights, cannot Create Discount Credit Note. Please request Maker!")
            ex.assignedTo = checker?.userName
            ex.assignedToId = checker?.userId

//                emailService.sendMessage("Discount credit note Review","Please review new discount credit note entry in vault",checker?.email!!,null)
        } else {
            throw RequestException("No checker found for this company !")
        }

        val exDraftCreditNote = draftCreditNoteRepo.save(ex)

        draftCreditNote.items.forEach { item ->
            item.createdBy = user
            item.createdOn = now()
            item.updatedBy = user
            item.updatedOn = now()
            item.draftCreditNoteId = exDraftCreditNote.id
        }
        exDraftCreditNote.items = draftItemRepo.saveAll(draftCreditNote.items)

        return exDraftCreditNote

    }

    fun getDirectCreditNotes(page: Int?, size: Int?, creditNoteNumber: String?, partnerId: Long?, documentType: DocumentType?,
                             draftId: Long?, partnerType: PartnerType?, transactionFrom: LocalDate?, transactionTo: LocalDate?,
                             createdBy: String?, pendingFor: String?, updatedBy: String?, tenant: String, statuses: MutableList<String?>?, ds: String? = null, partnerDetailId: Long?): PaginationDto {

        log.debug("Getting direct credit notes")

        var sortBy = Sort.by(Sort.Direction.DESC, "updatedOn").and(Sort.by(Sort.Direction.DESC, "amount"))
        val pagination = PageRequest.of(page ?: 0, size ?: 10, sortBy)

//        val tenants = companyService.findTenants(tenant,ds)
//        if (tenants.isEmpty()) throw RequestException("Your tenant: $tenant is not mapped to any Company")


        var statuslist: MutableList<Status?> = mutableListOf()
        if (!statuses.isNullOrEmpty()) {
            statuses.forEach {
                try {
                    if (it != null) statuslist.add(Status.valueOf(it.toUpperCase()))
                } catch (e: Exception) {
                    throw RequestException("unable to set status")
                }
            }
        }

        var companyMappingObj: CompanyTenantMapping? = companyService.getCompanyTenantMappingObject(ds?:tenant)
                ?: throw RequestException("No tenant mapping found for ${tenant} ")

        val res = draftCreditNoteRepo.findAll(DraftCreditNoteSpecification(creditNoteNumber, draftId, documentType,
                partnerType, transactionFrom, transactionTo, createdBy, pendingFor, updatedBy, statuslist, companyMappingObj?.companyId, partnerId, partnerDetailId), pagination)

        var draftIds: List<Long> = res.content.map { it.id }
        if(draftIds.isNullOrEmpty()){
            return PaginationDto(res.totalElements, res.totalPages, res.hasPrevious(), res.hasNext(), res.content)
        }
        var draftItemsById: Map<Long?, List<DraftItem>> = draftItemRepo.getItemsByDraftCreditNoteIds(draftIds).groupBy { it.draftCreditNoteId }
        var settleDraftListById: Map<Long, List<DraftItemInvoice>>? = draftItemInvoiceRepo.getInvoiceToSettleByDraftCreditNoteIds( draftIds)?.groupBy { it.draftId }

        var invoiceIdList: List<Long>? = settleDraftListById?.flatMap { it.value.map { it.bkInvoiceId } }
        var bkInvoicesList: List<BkInvoice> = mutableListOf()
        if(!invoiceIdList.isNullOrEmpty()){
            bkInvoicesList = bkInvoiceReadRepo.getByIds(invoiceIdList.toMutableList())
        }
        res.content.forEach { draftCreditNote ->
            draftCreditNote.items = draftItemsById.get(draftCreditNote.id)?: mutableListOf()
            if(draftCreditNote.invoiceSettled) {
                var settleInvoiceDtoList = mutableListOf<DiscountSettleInvoiceDto>()

                settleDraftListById?.get(draftCreditNote.id)?.forEach {draftSettleObj ->

                    var invoiceObj = bkInvoicesList.find { it.id == draftSettleObj.bkInvoiceId }
                    val pendingAmount: Double = ((invoiceObj?.amount) ?: 0.0) - ((invoiceObj?.paidAmount) ?: 0.0)
                    settleInvoiceDtoList.add(DiscountSettleInvoiceDto(draftSettleObj.invoiceNumber, draftSettleObj.bkInvoiceId, invoiceObj?.createdOn, pendingAmount, draftSettleObj.paidAmount))
                }

                draftCreditNote.settleInvoiceList = settleInvoiceDtoList

            }
        }

        return PaginationDto(res.totalElements, res.totalPages, res.hasPrevious(), res.hasNext(), res.content)
    }


    fun createDiscountDebitNote(draftCreditNote: DraftCreditNote, user: String): DebitNote {
        log.debug("Creating DiscountDebitNote $draftCreditNote : $user ")

        val ddn = DiscountDebitNoteDto(
                amountReceivable = draftCreditNote.amount!!,
                remarks = "",
                status = NoteStatus.PENDING,
                items = draftItemRepo.getItemsByDraftCreditNoteId(draftCreditNote.id).toMutableList(),
                partnerId = draftCreditNote.partnerId,
                partnerName = draftCreditNote.partnerName!!,
                partnerDetailId = draftCreditNote.partnerDetailId,
                tenant = draftCreditNote.tenant,
                type = draftCreditNote.type,
                client = draftCreditNote.client,
                invoiceNumber = draftCreditNote.invoiceNumber
        )

        return debitNoteService.createDiscountDebitNote(ddn, user)
    }

    fun createDiscountCreditNote(draftCreditNote: DraftCreditNote, user: String, tenant: String, debitNote: DebitNote): CreditNote {
        log.debug("Inside createDiscountCreditNote : $draftCreditNote")

        var tenants = companyTenantMappingRepo.getAllTenantByTenant(draftCreditNote.tenant)

        val invoices = bkInvoiceRepo.getByInvoiceNum(draftCreditNote.invoiceNumber, tenants)
        if (invoices.isNullOrEmpty()) throw RequestException("Invoice not found for invoice Number: ${draftCreditNote.invoiceNumber} for tenants: $tenants")

        val cn = CreditNote(
                id = 0,
                createdOn = now(),
                createdBy = draftCreditNote.createdBy,
                updatedOn = now(),
                updatedBy = draftCreditNote.createdBy,
                status = NoteStatus.PENDING,
                noteType = NoteTypes.DISCOUNT,
                amount = draftCreditNote.amount!!,
                supplierId = draftCreditNote.partnerId,
                supplierName = draftCreditNote.partnerName!!,
                remarks = "Discount Credit Note",
                settlementId = null,
                invoiceId = invoices[0]!!.id,
                debitNotes = mutableListOf(debitNote),
                closureType = CreditNoteClosureType.SETTLEMENT,
                partnerId = draftCreditNote.partnerId,
                partnerDetailId = draftCreditNote.partnerDetailId,
                tenant = draftCreditNote.tenant,
                creditNoteNumber = null,
                type = draftCreditNote.type,
                client = draftCreditNote.client,
                receiptStatus = ReceiptStatus.NOT_RECEIVED,
                expectedDate = LocalDate.now().plusDays(60),
                vendorCreditNoteDate = null,
                vendorCreditNoteAmount = null,
                vendorCreditNoteNumber = null,
                conversion = 100.0,
                remainingAmount = draftCreditNote.amount!!
        )

        return save(user, cn, tenant)
    }

    @Transactional
    fun updateDraftCreditNoteStatus(user: String, draftId: Long, tenant: String, status: Status, userName: String,
                                    checkerRemarks: String?,
                                    ds: String? = null,
                                    cnWithSettleInvoice: Boolean): CreditNote? {
        log.debug("Inside updateDraftCreditNoteStatus $user : $draftId")

        var draftCreditNote: DraftCreditNote
        try {
            draftCreditNote = draftCreditNoteRepo.getOne(draftId)
        }
        catch(e: Exception) {
            throw RequestException("DraftCreditNote not found for id: $draftId : ${e.message}")
        }

        if (user == draftCreditNote.createdBy) {
            throw RequestException("Cannot approve the request as created by you")
        }

        var creditNote: CreditNote? = null

        if (status != draftCreditNote.status && (status == Status.APPROVED || status == Status.REJECTED || status == Status.REVIEW )) {

            var checkers = checkerService.findCheckers(tenant,null,ds)
            if (checkers.isEmpty()) throw RequestException("No checker found for this company !")


            var checker = checkerService.compareChecker(checkers,draftCreditNote.assignedToId?:"")

            if (!draftCreditNote.assignedToId.equals(user) || checker == null)
                throw RequestException("You are not authorized to change draft credit note status - ${draftCreditNote.assignedToId} : ${checker?.userId} : $user")

            draftCreditNote.updatedBy = user
            draftCreditNote.updatedByName = userName
            draftCreditNote.updatedOn = now()
            draftCreditNote.status = status
            draftCreditNote.checkerRemarks = checkerRemarks

            if (Status.REVIEW == status) {
                draftCreditNote.assignedTo = draftCreditNote.sender
                draftCreditNote.assignedToId = draftCreditNote.senderId
//                emailService.sendMessage("Review Discount Credit note","Your Discount credit note transaction has came back for a review.",userMail)
            }
            else if (Status.APPROVED == status) {
                draftCreditNote.transactionDate = LocalDate.now()
                var settleInvoiceList: MutableList<DraftItemInvoice> = mutableListOf()
                var bkInvoiceMap: Map<Long, BkInvoice> = mutableMapOf()
                if(draftCreditNote.invoiceSettled && cnWithSettleInvoice) {

                    val pair = validateInvoices(draftCreditNote)
                    bkInvoiceMap = pair.first
                    settleInvoiceList = pair.second

                }
                val debitNote = createDiscountDebitNote(draftCreditNote, user)
                creditNote = createDiscountCreditNote(draftCreditNote, user, tenant, debitNote)
                draftCreditNote.creditNoteNumber = creditNote.creditNoteNumber

                if(draftCreditNote.invoiceSettled && cnWithSettleInvoice)
                updateAndSettleInvoice(draftCreditNote, settleInvoiceList, bkInvoiceMap, creditNote)

            }
            else if (Status.REJECTED == status) {
                draftItemRepo.deleteItems(draftCreditNote.id)
            }

            draftCreditNote = draftCreditNoteRepo.save(draftCreditNote)
            draftCreditNoteLogRepo.save(DraftCreditNoteLog(0, now(), user, status, draftCreditNote.id))
        }

        return creditNote
    }

    private fun validateInvoices(
        draftCreditNote: DraftCreditNote
    ): Pair<Map<Long, BkInvoice>, MutableList<DraftItemInvoice>> {
        var settleInvoiceList = mutableListOf<DraftItemInvoice>()
        var bkInvoiceMap = mapOf<Long, BkInvoice>()
        settleInvoiceList = draftItemInvoiceRepo.getInvoiceToSettleByDraftCreditNoteId(draftCreditNote.id)
            ?: throw RequestException("invoice detail not found to settle this draft!")
        val invoiceIds = settleInvoiceList.map { it.bkInvoiceId }
        bkInvoiceMap = bkInvoiceReadRepo.getByIds(invoiceIds as MutableList<Long>).associateBy { it.id }
        settleInvoiceList.forEach {
            val invoice = bkInvoiceMap[it.bkInvoiceId]
            if (invoice != null) {
                val invoicePendingAmt = invoice.amount - invoice.paidAmount
                if (it.paidAmount > invoicePendingAmt) {
                    throw RequestException("current invoice(${it.invoiceNumber}) pending amount(${invoicePendingAmt}) is less then paid amount(${it.paidAmount})! Please reject and re-create this draft!")
                }
            }

        }
        return Pair(bkInvoiceMap, settleInvoiceList)
    }

    private fun updateAndSettleInvoice(
        draftCreditNote: DraftCreditNote,
        settleInvoiceList: MutableList<DraftItemInvoice>,
        bkInvoiceMap: Map<Long, BkInvoice>,
        creditNote: CreditNote
    ) {

        var creditNoteCopy = creditNote.copy()
        var updateInvoiceList = mutableListOf<BkInvoice>()

            settleInvoiceList.forEach {

                var invoice = bkInvoiceMap.get(it.bkInvoiceId)?.copy()
                if (invoice != null) {
                    invoice.paidAmount += it.paidAmount
                    updateInvoiceList.add(invoice)
                }

            }


        creditNoteCopy.amountUsed = settleInvoiceList.sumByDouble { it.paidAmount }.toBigDecimal()
            var settlement = Settlement(
                0,
                null,
                null,
                null,
                creditNoteCopy.partnerId!!,
                creditNoteCopy.supplierName,
                creditNoteCopy.amountUsed!!.toDouble(),
                creditNoteCopy.amountUsed!!.toDouble(),
                "discount cn settlement",
                null,
                updateInvoiceList,
                mutableListOf(creditNoteCopy),
                PaymentType.CREDITNOTE,
                "",
                LocalDate.now(),
                creditNoteCopy.partnerId,
                null,
                creditNoteCopy.type,
                creditNoteCopy.tenant ?: "",
                null,
                null,
                null,
                false,
                reversed = false,
                advancePayment = mutableListOf(),
                chargeInvoice = mutableListOf(),
                charge = false,
                receipt = null,
                paymentSource = AdvancePaymentSource.SYSTEM,
                retailerDebitNotes = mutableListOf(),
                uuid = UUIDUtil.generateUuid()
            )
            settlementService.save("SYSTEM", settlement, null)
        }




    fun getDiscountedItemsByCreditNoteNumber(creditNoteNumber: String): List<DraftItem> {
        log.debug("Inside getDiscountedItemsByCreditNoteNumber $creditNoteNumber")

        return draftItemRepo.getByCreditNoteNumber(creditNoteNumber)
    }

    fun getCreditNoteByCreditNoteNumber(creditNoteNumber: String, partnerId: Long, partnerType: PartnerType): CreditNote? {
        log.debug("Inside getCreditNoteByCreditNoteNumber $creditNoteNumber")

        val pagination = PageRequest.of( 0, 10)
        val res =  creditNoteRepo.getCreditNoteForCreditNoteNumber(creditNoteNumber, partnerId, null, partnerType, pagination)
        var cn: CreditNote? = null

        if (res.content.isNotEmpty()) {
            cn = res.content[0]
        }
        return cn
    }

    fun getCreditNoteInfo(creditNoteNumber: String,tenant: String): DiscountCreditNoteAdvice {
        log.debug("Inside getCreditNoteInfo : $creditNoteNumber")
        var tenants=companyService.findTenants(tenant)
        val cn = if(tenants.isNullOrEmpty()){
            creditNoteRepo.getByNumber(creditNoteNumber) ?: throw RequestException("credit note with id: $creditNoteNumber note found")
        }else{
            creditNoteRepo.getByNumberAndTenant(creditNoteNumber,tenants) ?: throw RequestException("credit note with id: $creditNoteNumber note found")
        }

        var company = companyService.getCompanyTenantMappingObject(cn.tenant!!)

        cn.debitNotes = debitNoteService.getDebitNotesForCreditNote(cn.id).toMutableList()
        if (cn.debitNotes.isNullOrEmpty()) throw RequestException("Debit note not found for credit note: ${cn.id}")
        val items = debitNoteService.getDebitNoteDetailsByDebitNoteNumber(cn.debitNotes!![0].debitNoteNumber)
        var draftCNmode = draftCreditNoteRepo.getByCreditNoteNumber(creditNoteNumber)?.mode?:"mrp"
        log.debug("Debit notes - ${cn.debitNotes!!.size} : ${items.size}")
        val comp = companyRepo.getCompanyByTenant(cn.tenant!!)?: throw RequestException("Company mapping not found for ${cn.tenant}")

        var compName = if(cn?.tenant!!.contains("ds")){
                        "For Aarush Tirupathi Enterprises Pvt Ltd"
                        }else {
                        "For ${comp.name}"
                    }
        return DiscountCreditNoteAdvice(cn, items.toMutableList(),cn.qrCode,cn.irn,company?.partnerDetailId?:0L, draftCNmode,compName = compName)
    }

    fun getCreditNoteDataForPdf(creditNoteId:String,tenant:String? = null, ds: String? = null): SaleReturnCreditnoteDto {


        var tenants = mutableListOf<String?>()
        if(tenant != null){
            tenants = companyService.findTenants(ds?:tenant)
        }
        val creditNote = if(tenants.isNullOrEmpty()){
            creditNoteRepo.getByNumber(creditNoteId) ?: throw RequestException("credit note with id: $creditNoteId note found")
        }else{
            creditNoteRepo.getByNumberAndTenant(creditNoteId,tenants) ?: throw RequestException("credit note with id: $creditNoteId note found")
        }

        var saleReturnItems = mutableListOf<SaleReturnItemsDto>()

        var taxableAmt = BigDecimal.ZERO
        var totalTax = BigDecimal.ZERO
        var taxPer = BigDecimal.ZERO
        var allTaxPerc = mutableMapOf<String,BigDecimal>()
        allTaxPerc.put("5cgst", BigDecimal.ZERO)
        allTaxPerc.put("5sgst",BigDecimal.ZERO)
        allTaxPerc.put("5igst",BigDecimal.ZERO)
        allTaxPerc.put("12cgst", BigDecimal.ZERO)
        allTaxPerc.put("12sgst",BigDecimal.ZERO)
        allTaxPerc.put("12igst",BigDecimal.ZERO)
        allTaxPerc.put("18cgst", BigDecimal.ZERO)
        allTaxPerc.put("18sgst",BigDecimal.ZERO)
        allTaxPerc.put("18igst",BigDecimal.ZERO)
        allTaxPerc.put("28cgst", BigDecimal.ZERO)
        allTaxPerc.put("28sgst",BigDecimal.ZERO)
        allTaxPerc.put("28igst",BigDecimal.ZERO)
        allTaxPerc.put("cgstTotal",BigDecimal.ZERO)
        allTaxPerc.put("sgstTotal",BigDecimal.ZERO)
        allTaxPerc.put("igstTotal",BigDecimal.ZERO)
        allTaxPerc.put("5Taxable",BigDecimal.ZERO)
        allTaxPerc.put("12Taxable",BigDecimal.ZERO)
        allTaxPerc.put("18Taxable",BigDecimal.ZERO)
        allTaxPerc.put("28Taxable",BigDecimal.ZERO)
        allTaxPerc.put("0Taxable",BigDecimal.ZERO)


        var invoiceNumber:String? = ""

        var debitNotes = debitNoteRepo.getByCreditNoteId(creditNote.id) as MutableList<DebitNote>?
        val allowedNoteTypes = arrayOf<NoteTypes>(
            NoteTypes.SR_ACCEPTED,
            NoteTypes.ICS_RETURN,
            NoteTypes.ST_RETURN,
            NoteTypes.SR_EXPIRED,
            NoteTypes.SR_DAMAGED,
            NoteTypes.NSR_DAMAGED
        )
        debitNotes?.forEach {

            if(it.apiVersion == APIVersionType.V2){
                var detailsObj = retailIoProxy.getRioDebitItemData(retailIoVersion,retailIoSource,retailIoKey,it.logisticsPackageId?:"",true)
                invoiceNumber= detailsObj.invoiceNumber?:""
                detailsObj.items.forEach { i ->

                    var cgstAmt = i.cgstAmount
                    var igstAmt = i.igstAmount
                    var sgstAmt = i.sgstAmount


                    var cgst =
                        if (creditNote.noteType in allowedNoteTypes) (i.cgstPercent?:0.0).toBigDecimal() else BigDecimal.ZERO
                    var igst =
                        if (creditNote.noteType in allowedNoteTypes) (i.igstPercent?:0.0).toBigDecimal() else BigDecimal.ZERO
                    var sgst =
                        if (creditNote.noteType in allowedNoteTypes) (i.sgstPercent?:0.0).toBigDecimal() else BigDecimal.ZERO
                    taxableAmt += if (creditNote.noteType in allowedNoteTypes) i.cnTaxableAmount!!.toBigDecimal() else i.totalMrp!!.toBigDecimal()
                    totalTax += if (creditNote.noteType in allowedNoteTypes) i.cnNetGstAmount!!.toBigDecimal() else BigDecimal.ZERO
                    taxPer =
                        if (creditNote.noteType in allowedNoteTypes) ((i.cgstPercent?:0.0) + (i.sgstPercent?:0.0) + (i.igstPercent?:0.0)).toBigDecimal() else BigDecimal.ZERO

                    var cgstTaxValue: BigDecimal
                    var sgstTaxValue: BigDecimal
                    var igstTaxValue: BigDecimal
                    var taxableVal: BigDecimal
                    var cntaxablval =
                        if (creditNote.noteType in allowedNoteTypes) i.cnTaxableAmount!!.toBigDecimal() else BigDecimal(
                            i.totalMrp!!
                        )
                    if ((cgst + sgst).intValueExact() == 5) {
                        cgstTaxValue = allTaxPerc.get("5cgst")!!
                        sgstTaxValue = allTaxPerc.get("5sgst")!!
                        taxableVal = allTaxPerc.get("5Taxable")!!
                        allTaxPerc.put(
                            "5cgst",
                            cgstTaxValue + i.cnNetGstAmount!!.toBigDecimal().divide(BigDecimal(2)).setScale(2, RoundingMode.CEILING)
                        )
                        allTaxPerc.put(
                            "5sgst",
                            sgstTaxValue + i.cnNetGstAmount!!.toBigDecimal().divide(BigDecimal(2)).setScale(2, RoundingMode.CEILING)
                        )
                        allTaxPerc.put("5Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))

                    } else if (igst.intValueExact() == 5) {
                        igstTaxValue = allTaxPerc.get("5igst")!!
                        taxableVal = allTaxPerc.get("5Taxable")!!
                        allTaxPerc.put("5igst", igstTaxValue + i.cnNetGstAmount!!.toBigDecimal())
                        allTaxPerc.put("5Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))
                    } else if ((cgst + sgst).intValueExact() == 12) {
                        cgstTaxValue = allTaxPerc.get("12cgst")!!
                        sgstTaxValue = allTaxPerc.get("12sgst")!!
                        taxableVal = allTaxPerc.get("12Taxable")!!
                        allTaxPerc.put(
                            "12cgst",
                            cgstTaxValue + i.cnNetGstAmount!!.toBigDecimal().divide(BigDecimal(2)).setScale(2, RoundingMode.CEILING)
                        )
                        allTaxPerc.put(
                            "12sgst",
                            sgstTaxValue + i.cnNetGstAmount!!.toBigDecimal().divide(BigDecimal(2)).setScale(2, RoundingMode.CEILING)
                        )
                        allTaxPerc.put("12Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))

                    } else if (igst.intValueExact() == 12) {
                        igstTaxValue = allTaxPerc.get("12igst")!!
                        taxableVal = allTaxPerc.get("12Taxable")!!
                        allTaxPerc.put("12igst", igstTaxValue + i.cnNetGstAmount!!.toBigDecimal())
                        allTaxPerc.put("12Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))
                    } else if ((cgst + sgst).intValueExact() == 18) {
                        cgstTaxValue = allTaxPerc.get("18cgst")!!
                        sgstTaxValue = allTaxPerc.get("18sgst")!!
                        taxableVal = allTaxPerc.get("18Taxable")!!
                        allTaxPerc.put(
                            "18cgst",
                            cgstTaxValue + i.cnNetGstAmount!!.toBigDecimal().divide(BigDecimal(2)).setScale(2, RoundingMode.CEILING)
                        )
                        allTaxPerc.put(
                            "18sgst",
                            sgstTaxValue + i.cnNetGstAmount!!.toBigDecimal().divide(BigDecimal(2)).setScale(2, RoundingMode.CEILING)
                        )
                        allTaxPerc.put("18Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))

                    } else if (igst.intValueExact() == 18) {
                        igstTaxValue = allTaxPerc.get("18igst")!!
                        taxableVal = allTaxPerc.get("18Taxable")!!
                        allTaxPerc.put("18igst", igstTaxValue + i.cnNetGstAmount!!.toBigDecimal())
                        allTaxPerc.put("18Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))
                    } else if ((cgst + sgst).intValueExact() == 28) {
                        cgstTaxValue = allTaxPerc.get("28cgst")!!
                        sgstTaxValue = allTaxPerc.get("28sgst")!!
                        taxableVal = allTaxPerc.get("28Taxable")!!
                        allTaxPerc.put(
                            "28cgst",
                            cgstTaxValue + i.cnNetGstAmount!!.toBigDecimal().divide(BigDecimal(2)).setScale(2, RoundingMode.CEILING)
                        )
                        allTaxPerc.put(
                            "28sgst",
                            sgstTaxValue + i.cnNetGstAmount!!.toBigDecimal().divide(BigDecimal(2)).setScale(2, RoundingMode.CEILING)
                        )
                        allTaxPerc.put("28Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))

                    } else if (igst.intValueExact() == 28) {
                        igstTaxValue = allTaxPerc.get("28igst")!!
                        taxableVal = allTaxPerc.get("28Taxable")!!
                        allTaxPerc.put("28igst", igstTaxValue + i.cnNetGstAmount!!.toBigDecimal())
                        allTaxPerc.put("28Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))
                    } else {
                        if (creditNote.noteType in allowedNoteTypes) {
                            taxableVal = allTaxPerc.get("0Taxable")!!
                            allTaxPerc.put("0Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))
                        }
                    }
                    saleReturnItems.add(
                        SaleReturnItemsDto(
                            id = "1",
                            createdOn = "${it.createdOn}",
                            ucode = "${i.itemCode}",
                            batch = i.batchNumber,
                            amount = "${i.mrp}",
                            quantity = "${i.returnQuantity}",
                            epr = "${i.mrp}",
                            trueEpr = "${i.mrp}",
                            discountPercentage = "${i.discountPercentage}",
                            discountAmount = "${i.discountAmount}",
                            cgst = "${i.cgstPercent?:0.0}",
                            cgstAmt = if((i.cgstPercent?: 0.0) > 0.0) String.format("%.2f", (i.netGstAmount?:0.0)/2) else "0.0",
                            sgst = "${i.sgstPercent?:0.0}",
                            sgstAmt = if((i.sgstPercent?: 0.0) > 0.0) String.format("%.2f", (i.netGstAmount?:0.0)/2) else "0.0",
                            igst = "${i.igstPercent?:0.0}",
                            igstAmt = if((i.igstPercent?: 0.0) > 0.0) String.format("%.2f", i.netGstAmount) else "0.0",
                            hsn = i.hsnCode,
                            invoiceAmount = "${i.payableAmount}",
                            netGstAmt = "${i.netGstAmount}",
                            abtMrp = "${i.abettedMrp}",
                            name = i.itemName,
                            taxableAmount = "${i.taxableAmount}",
                            invoiceNumber = detailsObj.invoiceNumber,
                            returnQty = "${i.returnQuantity}",
                            conversion = "${i.conversionPercentage}",
                            cnTaxableValue = if (creditNote.noteType in allowedNoteTypes) "${i.cnTaxableAmount}" else "${i.totalMrp}",
                            cnNetGST = if (creditNote.noteType in allowedNoteTypes) "${i.cnNetGstAmount}" else "${i.payableAmount?.times(
                                i.returnQuantity!!
                            )}",
                            cnBillValue = if (creditNote.noteType in allowedNoteTypes) "${i.cnPayableAmount}"
                            else if (creditNote.noteType == NoteTypes.DISCOUNT) "${i.discountAmount}"
                            else "${i.totalMrp}",
                            expiry =i.expiryDate.toString() ,
                            invoiceDate = if(it.invoiceAt != null) "${it.invoiceAt!!.toLocalDate()}" else "",
                            returnDiscount = "0.0",
                            baseBillingRate = (i.effectivePerItemCost?:0.0).toString()
                        )
                    )
                }
            }else {
                var invoiceNumberList = it.debitItems.map { it.invoiceNumber }.distinctBy { it }
                var invoiceListMap = bkInvoiceReadRepo.getByInvoiceNumberList(invoiceNumberList,creditNote.tenant?:"").associateBy { it?.invoiceNum }
                it.debitItems.forEach { i ->

                    var cgst =
                        if (creditNote.noteType in allowedNoteTypes) i.cgst?: BigDecimal.ZERO else BigDecimal.ZERO
                    var igst =
                        if (creditNote.noteType in allowedNoteTypes) i.igst?: BigDecimal.ZERO  else BigDecimal.ZERO
                    var sgst =
                        if (creditNote.noteType in allowedNoteTypes) i.sgst?: BigDecimal.ZERO  else BigDecimal.ZERO
                    taxableAmt += if (creditNote.noteType in allowedNoteTypes) i.cnTaxableValue!! else i.cnMrpValue!!
                    totalTax += if (creditNote.noteType in allowedNoteTypes) i.cnNetGST!! else BigDecimal.ZERO
                    taxPer =
                        if (creditNote.noteType in allowedNoteTypes) ((i.cgst?: BigDecimal.ZERO)  + (i.sgst?: BigDecimal.ZERO)  + (i.igst?: BigDecimal.ZERO) ) else BigDecimal.ZERO

                    var cgstTaxValue: BigDecimal
                    var sgstTaxValue: BigDecimal
                    var igstTaxValue: BigDecimal
                    var taxableVal: BigDecimal
                    var cntaxablval =
                        if (creditNote.noteType in allowedNoteTypes) i.cnTaxableValue else BigDecimal(
                            i.amount
                        )
                    if ((cgst + sgst).intValueExact() == 5) {
                        cgstTaxValue = allTaxPerc.get("5cgst")!!
                        sgstTaxValue = allTaxPerc.get("5sgst")!!
                        taxableVal = allTaxPerc.get("5Taxable")!!
                        allTaxPerc.put(
                            "5cgst",
                            cgstTaxValue + i.cnNetGST!!.divide(BigDecimal(2)).setScale(2, RoundingMode.CEILING)
                        )
                        allTaxPerc.put(
                            "5sgst",
                            sgstTaxValue + i.cnNetGST!!.divide(BigDecimal(2)).setScale(2, RoundingMode.CEILING)
                        )
                        allTaxPerc.put("5Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))

                    } else if (igst.intValueExact() == 5) {
                        igstTaxValue = allTaxPerc.get("5igst")!!
                        taxableVal = allTaxPerc.get("5Taxable")!!
                        allTaxPerc.put("5igst", igstTaxValue + i.cnNetGST!!)
                        allTaxPerc.put("5Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))
                    } else if ((cgst + sgst).intValueExact() == 12) {
                        cgstTaxValue = allTaxPerc.get("12cgst")!!
                        sgstTaxValue = allTaxPerc.get("12sgst")!!
                        taxableVal = allTaxPerc.get("12Taxable")!!
                        allTaxPerc.put(
                            "12cgst",
                            cgstTaxValue + i.cnNetGST!!.divide(BigDecimal(2)).setScale(2, RoundingMode.CEILING)
                        )
                        allTaxPerc.put(
                            "12sgst",
                            sgstTaxValue + i.cnNetGST!!.divide(BigDecimal(2)).setScale(2, RoundingMode.CEILING)
                        )
                        allTaxPerc.put("12Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))

                    } else if (igst.intValueExact() == 12) {
                        igstTaxValue = allTaxPerc.get("12igst")!!
                        taxableVal = allTaxPerc.get("12Taxable")!!
                        allTaxPerc.put("12igst", igstTaxValue + i.cnNetGST!!)
                        allTaxPerc.put("12Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))
                    } else if ((cgst + sgst).intValueExact() == 18) {
                        cgstTaxValue = allTaxPerc.get("18cgst")!!
                        sgstTaxValue = allTaxPerc.get("18sgst")!!
                        taxableVal = allTaxPerc.get("18Taxable")!!
                        allTaxPerc.put(
                            "18cgst",
                            cgstTaxValue + i.cnNetGST!!.divide(BigDecimal(2)).setScale(2, RoundingMode.CEILING)
                        )
                        allTaxPerc.put(
                            "18sgst",
                            sgstTaxValue + i.cnNetGST!!.divide(BigDecimal(2)).setScale(2, RoundingMode.CEILING)
                        )
                        allTaxPerc.put("18Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))

                    } else if (igst.intValueExact() == 18) {
                        igstTaxValue = allTaxPerc.get("18igst")!!
                        taxableVal = allTaxPerc.get("18Taxable")!!
                        allTaxPerc.put("18igst", igstTaxValue + i.cnNetGST!!)
                        allTaxPerc.put("18Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))
                    } else if ((cgst + sgst).intValueExact() == 28) {
                        cgstTaxValue = allTaxPerc.get("28cgst")!!
                        sgstTaxValue = allTaxPerc.get("28sgst")!!
                        taxableVal = allTaxPerc.get("28Taxable")!!
                        allTaxPerc.put(
                            "28cgst",
                            cgstTaxValue + i.cnNetGST!!.divide(BigDecimal(2)).setScale(2, RoundingMode.CEILING)
                        )
                        allTaxPerc.put(
                            "28sgst",
                            sgstTaxValue + i.cnNetGST!!.divide(BigDecimal(2)).setScale(2, RoundingMode.CEILING)
                        )
                        allTaxPerc.put("28Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))

                    } else if (igst.intValueExact() == 28) {
                        igstTaxValue = allTaxPerc.get("28igst")!!
                        taxableVal = allTaxPerc.get("28Taxable")!!
                        allTaxPerc.put("28igst", igstTaxValue + i.cnNetGST!!)
                        allTaxPerc.put("28Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))
                    } else {
                        if (creditNote.noteType == NoteTypes.SR_ACCEPTED || creditNote.noteType == NoteTypes.ICS_RETURN || creditNote.noteType == NoteTypes.ST_RETURN || creditNote.noteType == NoteTypes.SR_EXPIRED) {
                            taxableVal = allTaxPerc.get("0Taxable")!!
                            allTaxPerc.put("0Taxable", taxableVal + (cntaxablval ?: BigDecimal.ZERO))
                        }
                    }
                    var invoiceDate = invoiceListMap.get(i.invoiceNumber)

                    saleReturnItems.add(
                        SaleReturnItemsDto(
                            id = "${i.id}",
                            createdOn = "${i.createdOn}",
                            ucode = "${i.ucode}",
                            batch = i.batch,
                            amount = "${i.amount}",
                            quantity = "${i.quantity}",
                            epr = "${i.epr}",
                            trueEpr = "${i.trueEpr}",
                            discountPercentage = if (creditNote.client.name == "RIO") "${i.dnTaxableRate}" else "${i.discountPercentage}",
                            discountAmount = "${i.discountAmount}",
                            cgst = if(i.showGstSeparately == false) "0" else "${i.cgst?:0.0}",
                            cgstAmt = if((i.cgst?: BigDecimal.ZERO) > BigDecimal.ZERO && i.showGstSeparately == true) String.format("%.2f", (i.cnNetGST?:BigDecimal.ZERO)/BigDecimal(2)) else if(i.showGstSeparately == false) "0"  else "0.0",
                            sgst = if(i.showGstSeparately == false) "0" else "${i.sgst?:0.0}",
                            sgstAmt = if((i.sgst?: BigDecimal.ZERO) > BigDecimal.ZERO && i.showGstSeparately == true) String.format("%.2f", (i.cnNetGST?:BigDecimal.ZERO)/BigDecimal(2)) else if(i.showGstSeparately == false) "0"  else "0.0",
                            igst = if(i.showGstSeparately == false) "0" else "${i.igst?:0.0}",
                            igstAmt = if((i.igst?: BigDecimal.ZERO) > BigDecimal.ZERO && i.showGstSeparately == true) String.format("%.2f",i.cnNetGST) else if(i.showGstSeparately == false) "0" else "0.0",
                            hsn = i.hsn,
                            invoiceAmount = "${i.invoiceAmount}",
                            netGstAmt = if(i.showGstSeparately == false) "0" else "${i.netGstAmt}",
                            abtMrp = "${i.abtMrp}",
                            name = i.name,
                            taxableAmount = "${i.taxableAmount}",
                            invoiceNumber = i.invoiceNumber,
                            returnQty = "${i.cnQty?:i.returnQty}",
                            conversion = "${i.conversion}",
                            cnTaxableValue = if (creditNote.noteType in allowedNoteTypes) "${i.cnTaxableValue}" else "${i.amount}",
                            cnNetGST = if(i.showGstSeparately == false) "0" else if (creditNote.noteType in allowedNoteTypes) "${i.cnNetGST}" else "${i.amount * i.returnQty!!}",
                            cnBillValue = if (creditNote.noteType in allowedNoteTypes) "${i.cnBillValue}"
                            else if (creditNote.noteType == NoteTypes.DISCOUNT) "${i.discountAmount}"
                            else "${i.cnMrpValue}",
                            invoiceDate = invoiceDate?.createdOn?.toLocalDate()?.toString() ?: "",
                            expiry = i.expiry.toString(),
                            baseBillingRate = (i.baseBillingRate?:BigDecimal.ZERO).toString(),
                            returnDiscount = (i.returnDiscount?:0.0).toString()
                        )
                    )
                }
            }
        }

        var tenantMapping = companyService.getCompanyTenantMappingObject(debitNotes?.get(0)!!.tenant)?: throw RequestException("Source ${debitNotes?.get(0)!!.tenant} info not found! ")

        var sourcePdi = getSourcePdi(tenantMapping,creditNote)
        var sourceSupplierList = supplierProxy.supplier(null,sourcePdi)
        var sourceSupplier = if (sourceSupplierList.isNotEmpty()) sourceSupplierList.get(0) else throw RequestException("Could not get Source Supplier List for partnerId ${tenantMapping.partnerDetailId} for tenant " +
                "${debitNotes?.get(0)!!.tenant} ,while sale return credit note PDF creation.")
        var fromStateId = sourceSupplier?.partnerDetailList!!.get(0)?.stateId

//        var pdi = getMappedPdi(debitNotes.get(0).partnerDetailId!!.toLong(),creditNote.createdOn!!)

        var supplierList = supplierProxy.supplier(null, debitNotes?.get(0)?.partnerDetailId)
        var supplier = if (supplierList.isNotEmpty()) supplierList.get(0) else throw RequestException("Could not get Supplier List for partnerId ${creditNote.partnerDetailId} for tenant " +
        "${debitNotes?.get(0)!!.tenant} ,while sale return credit note PDF creation.")

        var toStateId = supplier?.partnerDetailList!!.get(0)?.stateId

        log.debug("debitnote data : ${ObjectMapper().writeValueAsString(debitNotes)}")
        var partnerDetail = supplier.partnerDetailList!![0]

        var totalCgst = allTaxPerc.get("5cgst")!! + allTaxPerc.get("12cgst")!! + allTaxPerc.get("18cgst")!! + allTaxPerc.get("28cgst")!!
        var totalSgst = allTaxPerc.get("5sgst")!! + allTaxPerc.get("12sgst")!! + allTaxPerc.get("18sgst")!! + allTaxPerc.get("28sgst")!!
        var totalIgst = allTaxPerc.get("5igst")!! + allTaxPerc.get("12igst")!! + allTaxPerc.get("18igst")!! + allTaxPerc.get("28igst")!!
        var totalTaxable = allTaxPerc.get("5Taxable")!! + allTaxPerc.get("12Taxable")!! + allTaxPerc.get("18Taxable")!! + allTaxPerc.get("28Taxable")!! + allTaxPerc.get("0Taxable")!!

        var creditNoteDateIST = creditNote.createdOn?.plusHours(5)?.plusMinutes(30)
        log.debug("utc date and time -->${creditNote.createdOn}, ist converted date and time-->${creditNoteDateIST}")

        var supplierObjects: PartnerGenericDetailDTO? =
            supplierProxy.partnerGenericDto(mutableListOf(creditNote.partnerDetailId?:0,sourcePdi?:0))?:
            throw RequestException("Supplier info not found!")
        var map: Map<Int?, Data?>? = supplierObjects?.data?.associateBy { it?.partnerDetailId }?:
        throw RequestException("Supplier info not found!")
        var supplierGen: Data = map?.get(creditNote.partnerDetailId?.toInt()?:0) ?:throw RequestException("source info not found")
        var sourceSupplierGen = map.get(sourcePdi?.toInt())?:throw RequestException("Supplier info not found!")

        var amount =  creditNote.amount!!.toDouble()
        val num: Float = amount.toFloat()
        val rupee = floor(num.toDouble()).toInt()
        val paisa = (("0."+amount.toString().split(".").get(1)).toFloat()*100).toInt()

        val amountWords = (englishNumberToWords.convert(rupee.toDouble())?.toUpperCase() + " RUPEES AND "
                + englishNumberToWords.convert(paisa.toDouble())?.toUpperCase() +" PAISA ONLY")
        var fromDL = sourceSupplierGen.compliance?.filter { it.licenseType == LicenseType.DRUG_LICENSE.type && it.enabled==true }
        var fssai = sourceSupplierGen.compliance?.filter { it.licenseType == LicenseType.FSSAI.type && it.enabled==true  }
        var toDL = supplierGen.compliance?.filter { it.licenseType == LicenseType.DRUG_LICENSE.type && it.enabled==true  }

        var cin = sourceSupplierGen.compliance?.filter { it.licenseType == LicenseType.CIN.type && it.enabled==true }
        var headers = SaleReturnHeaderItem(
                creditNoteId = creditNote.creditNoteNumber,
                creditNoteDate = "${creditNoteDateIST?.toLocalDate()}",
                saleReturnType = "${creditNote.noteType}",
                billToAddress = (partnerDetail!!.address1?:"") +","+ (partnerDetail!!.address2?:"") +
            "\n ${partnerDetail!!.city} , ${partnerDetail!!.state} - ${partnerDetail!!.pincode}",

            fromAddress =  "${sourceSupplier!!.partnerDetailList?.get(0)!!.address1} \n ${sourceSupplier!!.partnerDetailList?.get(0)!!.address2} " +
                        "\n ${sourceSupplier!!.partnerDetailList?.get(0)?.city} , ${sourceSupplier!!.partnerDetailList?.get(0)?.state} - ${sourceSupplier!!.partnerDetailList?.get(0)?.pincode}",
                billState = partnerDetail.state,
                storeName = supplier!!.partnerName,
                fromGst = sourceSupplier!!.partnerDetailList?.get(0)!!.gst,
                fromState = sourceSupplier!!.partnerDetailList?.get(0)!!.state,
                colMrp = "CN Taxable Value",
                colMrpValue ="CN Tax Amount",
                taxPer = "${taxPer}",
                tax = "${totalTax}",
                cnValue = creditNote.amount.toString(),
                taxableValue = "${taxableAmt}",
                invoiceNo = if(debitNotes?.get(0)?.apiVersion == APIVersionType.V2) invoiceNumber else if(debitNotes?.get(0)!!.consolidatedStoreInvoiceInvoiceId == null && debitNotes?.get(0)?.debitItems?.isNotEmpty() == true) debitNotes?.get(0)?.debitItems[0]?.invoiceNumber else debitNotes?.get(0)?.consolidatedStoreInvoiceInvoiceId?:"",
                invoiceDate = "${debitNotes?.get(0)!!.invoiceAt?.toLocalDate()}",
                conversion = if(saleReturnItems.isNotEmpty()) {
                    "${saleReturnItems[0].conversion}"
                }else{"0"},
                toGst = partnerDetail.gst?:"",
                eighteenPercCGSTTaxValue = "${allTaxPerc.get("18cgst")}" ,
                eighteenPercIGSTTaxValue = "${allTaxPerc.get("18igst")}",
                eighteenPercSGSTTaxValue = "${allTaxPerc.get("18sgst")}" ,
                eighteenPercTaxValue = "${allTaxPerc.get("18Taxable")}",
                fivePercCGSTTaxValue = "${allTaxPerc.get("5cgst")}",
                fivePercIGSTTaxValue = "${allTaxPerc.get("5igst")}",
                fivePercSGSTTaxValue = "${allTaxPerc.get("5sgst")}",
                fivePercTaxValue = "${allTaxPerc.get("5Taxable")}",
                fromStateId = "$fromStateId",
                toStateId = "$toStateId",
                totalCGSTTaxValue = "$totalCgst",
                totalIGSTTaxValue = "$totalIgst",
                totalSGSTTaxValue ="$totalSgst" ,
                totalTaxValue = "$totalTaxable",
                twelvePercCGSTTaxValue = "${allTaxPerc.get("12cgst")}",
                twelvePercIGSTTaxValue = "${allTaxPerc.get("12igst")}",
                twelvePercSGSTTaxValue = "${allTaxPerc.get("12sgst")}",
                twelvePercTaxValue = "${allTaxPerc.get("12Taxable")}",
                twentyEightPercCGSTTaxValue ="${allTaxPerc.get("28cgst")}",
                twentyEightPercIGSTTaxValue = "${allTaxPerc.get("28igst")}",
                twentyEightPercSGSTTaxValue = "${allTaxPerc.get("28sgst")}",
                twentyEightPercTaxValue ="${allTaxPerc.get("28Taxable")}",
                qr = creditNote.qrCode,
                irn = creditNote.irn,
                zeroPercTaxValue = "${allTaxPerc.get("0Taxable")}",
                sourceName = sourceSupplier.partnerName,
                fromDL = if(fromDL.isNullOrEmpty()) "" else fromDL.get(0)?.licenseNo?:"",
                fssai =if(fssai.isNullOrEmpty()) "" else fssai.get(0)?.licenseNo?:"",
                toDL = if(toDL.isNullOrEmpty()) "" else toDL.get(0)?.licenseNo?:"",
                cin = if(cin.isNullOrEmpty()) "" else cin.get(0)?.licenseNo?:"",
                creditNoteAmountInWords = amountWords
        )



        log.debug("header object : $headers")
        return  SaleReturnCreditnoteDto(headers,saleReturnItems,creditNote.client.name)
    }

    private fun einvoiceObjCreation(creditNote: CreditNote, supplier: Supplier, einvoiceList: MutableList<EinvoiceItem?>, sourceSupplier: Supplier?,totalAssessable:BigDecimal,theaId:Long,totalValue:BigDecimal,totalCgst:BigDecimal?,totalSgst:BigDecimal?,totalIgst:BigDecimal?,ds: String? = null,tenant: String): EIClientResponseDto? {
        log.info("inside einvoice object creation for creditNote ${creditNote.creditNoteNumber}")
        var invoiceDocDate = getISTDateTime(creditNote.createdOn)
        var eInvoiceObj =
                Einvoice(
                        invoiceId = creditNote.creditNoteNumber,
                        invoiceType = EInvoiceType.SALES_RET,
                        invoiceCategory = "B2B",
                        buyerDetail =
                        BuyerDetail("${supplier!!.partnerDetailList?.get(0)!!.address1}  ${supplier!!.partnerDetailList?.get(0)!!.address2}",
                                supplier!!.partnerDetailList?.get(0)!!.gst,
                                supplier!!.partnerName,
                                supplier!!.partnerDetailList?.get(0)!!.city,
                                supplier!!.partnerDetailList?.get(0)!!.pincode,
                                supplier.partnerDetailList?.get(0)!!.stateId,
                                supplier.partnerDetailList?.get(0)!!.state

                        ),
                        itemList = einvoiceList,
                        sellerDetail =
                        SellerDetail(
                                "${sourceSupplier!!.partnerDetailList?.get(0)!!.address1}  ${sourceSupplier!!.partnerDetailList?.get(0)!!.address2}",
                                sourceSupplier!!.partnerDetailList?.get(0)!!.gst,
                                sourceSupplier!!.partnerName,
                                sourceSupplier!!.partnerDetailList?.get(0)!!.city,
                                sourceSupplier!!.partnerDetailList?.get(0)!!.pincode,
                                sourceSupplier!!.partnerDetailList?.get(0)!!.state),
                        sellerId = if(ds==null) theaId else sourceSupplier.partnerDetailList?.get(0)!!.id,
                        sellerType = if(ds==null && tenant.substring(0,2) == "th") "THEA"
                        else if(ds==null && tenant.substring(0,2) == "ar") "ARSENAL"
                        else "DARK_STORE",
                        valueDetail = ValueDetail(totalAssessable ,totalValue,totalCgst,totalSgst,totalIgst),
                        invoiceDocCreatedOn = DateTimeFormatter.ofPattern("dd/MM/yyyy").format(invoiceDocDate)
                )

        var einvoice: EIClientResponseDto? = null
        try {
            var sellerGstin = eInvoiceObj.sellerDetail
            var buyerGstin = eInvoiceObj.buyerDetail
            if((sellerGstin?.gstin).equals(buyerGstin?.gstin)){
                log.debug("buyer seller gstin same for ${eInvoiceObj.invoiceId}")
                return null
            }else if(!(sellerGstin?.gstin).equals(buyerGstin?.gstin)){
                log.info("einvoice proxy call for creditNote ${creditNote.creditNoteNumber}")
                einvoice = eInvoiceProxy.createEinvoice(eInvoiceObj, "VAULT")
            }
        } catch (e: Exception) {
            log.error("error while fetching einvoice : $e", e)
            throw e
        }
        return einvoice
    }



    fun deleteNsrItems(id: Long): Int {
        log.debug("Inside delete NSR items: $id")

        return draftNsrItemRepo.removeItems(id)
    }

    @Transactional
    fun createNSRCreditNoteEntry(user: String, draftNsrCreditNoteDto: DraftNsrCreditNoteDto, tenant: String, oldId: Long?, ds: String? = null): DraftNsr {

        if(ds==null) {
            return saveNSRCreditNoteEntry(user, draftNsrCreditNoteDto, tenant, oldId, ds)
        } else {
            var companyMappingObj = companyService.getCompanyTenantMappingObject(ds?:draftNsrCreditNoteDto.tenant)
            if(companyMappingObj?.partnerDetailId == null && ds != null) throw RequestException("DarkStore is not Configured for this Warehouse, cannot proceed!")
            var dsInfo = supplierProxy.supplier(null, companyMappingObj?.partnerDetailId)
            var customerName = draftNsrCreditNoteDto.partnerName
            var pdi = draftNsrCreditNoteDto.partnerDetailId
            var pid = draftNsrCreditNoteDto.partnerId
            draftNsrCreditNoteDto.partnerId = dsInfo[0]?.partnerId!!
            draftNsrCreditNoteDto.partnerDetailId = companyMappingObj?.partnerDetailId
            draftNsrCreditNoteDto.partnerName = companyMappingObj?.tenantName!!
            var warehouseNsr = saveNSRCreditNoteEntry(user, draftNsrCreditNoteDto, tenant, null, null)
            draftNsrCreditNoteDto.partnerId = pid
            draftNsrCreditNoteDto.partnerDetailId = pdi
            draftNsrCreditNoteDto.partnerName = customerName
            saveNSRCreditNoteEntry(user, draftNsrCreditNoteDto, tenant, null, ds)
            return warehouseNsr

        }

    }

    fun saveNSRCreditNoteEntry(user: String, draftNsrCreditNoteDto: DraftNsrCreditNoteDto, tenant: String, oldId: Long?, ds: String? = null): DraftNsr {
        log.debug("Creating NSR credit note $draftNsrCreditNoteDto : $oldId")

        var companyMappingObj = companyService.getCompanyTenantMappingObject(ds?:draftNsrCreditNoteDto.tenant)
                ?: throw RequestException("no tenant mapping found for ${draftNsrCreditNoteDto.tenant}")

        if(ds!=null) draftNsrCreditNoteDto.tenant = companyMappingObj.tenant

        val dcn: DraftNsr
        if (oldId != null && ds==null ) {
            try {
                dcn = draftNsrRepo.getOne(oldId)
            }
            catch(e: Exception) {
                throw RequestException("draft NSR entry not found for id: $oldId : ${e.message}")
            }

            deleteNsrItems(oldId)

            dcn.updatedBy = user
            dcn.updatedByName = draftNsrCreditNoteDto.senderName
            dcn.totalMrp = draftNsrCreditNoteDto.totalMrp!!
            dcn.creditNoteAmount = draftNsrCreditNoteDto.amount
            dcn.totalQuantity = draftNsrCreditNoteDto.totalQuantity
            dcn.status = Status.PENDING_APPROVAL
        }
        else {
            var draftNsr = DraftNsr (
                    id = 0,
                    createdBy = user,
                    createdOn = now(),
                    updatedBy = user,
                    updatedOn = now(),
                    partnerName = draftNsrCreditNoteDto.partnerName,
                    partnerId = draftNsrCreditNoteDto.partnerId,
                    totalMrp = draftNsrCreditNoteDto.totalMrp!!,
                    companyId = companyMappingObj.companyId,
                    status = Status.PENDING_APPROVAL,
                    creditNoteNumber = null,
                    approvalDate = null,
                    assignedTo = null,
                    assignedToId = null,
                    createdByName = draftNsrCreditNoteDto.senderName,
                    updatedByName = draftNsrCreditNoteDto.senderName,
                    tenant = companyMappingObj.tenant,
                    debitNoteNumber = null,
                    creditNoteAmount = draftNsrCreditNoteDto.amount,
                    totalQuantity = draftNsrCreditNoteDto.totalQuantity,
                    nsrEntryId = draftNsrCreditNoteDto.nsrEntryId,
                    comment = draftNsrCreditNoteDto.remarks,
                    partnerDetailId = draftNsrCreditNoteDto.partnerDetailId,
                    client = draftNsrCreditNoteDto.client
            )

            var checkers = checkerService.findCheckers(tenant,null,ds)

            if (checkers.isNotEmpty()) {

                var random = Random()
                val index = random.nextInt(checkers.size)
                var checker = checkers[index]
                if (checker?.userId.equals(user)) throw RequestException("Users having Checker rights, cannot Create NSR credit note. Please request Maker!")
                draftNsr.assignedTo = checker?.userName
                draftNsr.assignedToId = checker?.userId

//                emailService.sendMessage("Discount credit note Review","Please review new discount credit note entry in vault",checker?.email!!,null)
            } else {
                throw RequestException("No checker found for this company !")
            }

            dcn = draftNsrRepo.save(draftNsr)
        }

        val draftItems = mutableListOf<DraftNsrItem>()
        draftNsrCreditNoteDto.items.forEach { item ->
            val draftItem = DraftNsrItem(
                    id = 0,
                    createdOn = now(),
                    createdBy = user,
                    updatedOn = now(),
                    name = item.name,
                    ucode = item.ucode,
                    batch = item.batch,
                    mrp = item.mrp,
                    draftNsrId = dcn.id,
                    expiry = item.expiry,
                    quantity = item.quantity,
                    reason = item.reason,
                    conversionAmount = item.conversionAmount,
                    conversionPercent = item.conversionPercent,
                    tenant = companyMappingObj.tenant
            )

            draftItems.add(draftItem)
        }

        dcn.items = draftNsrItemRepo.saveAll(draftItems)

        return dcn
    }

    fun getNsrEntries(page: Int?, size: Int?, creditNoteNumber: String?, partnerId: Long?, draftId: Long?, approvedFrom: LocalDate?,
                      approvedTo: LocalDate?, createdOnFrom: LocalDate?, createdOnTo: LocalDate?, createdBy: String?,
                      pendingFor: String?, updatedBy: String?, tenant: String, statuses: MutableList<String?>?, ds: String? = null): PaginationDto {

        log.debug("Getting NSR Entries $createdOnFrom : $createdOnTo : $approvedFrom : $approvedTo")

        var sortBy = Sort.by(Sort.Direction.DESC, "createdOn").and(Sort.by(Sort.Direction.DESC, "creditNoteAmount"))
        val pagination = PageRequest.of(page ?: 0, size ?: 10, sortBy)

        val tenants = companyService.findTenants(ds?:tenant)
        if (tenants.isEmpty()) throw RequestException("Your tenant: $tenant is not mapped to any Company")

        var statuslist: MutableList<Status?> = mutableListOf()
        if (!statuses.isNullOrEmpty()) {
            statuses.forEach {
                try {
                    if (it != null) statuslist.add(Status.valueOf(it.toUpperCase()))
                } catch (e: Exception) {
                    throw RequestException("unable to set status")
                }
            }
        }

        if (statuslist.isNotEmpty() && statuslist[0] == Status.PENDING_APPROVAL) {
            statuslist.add(Status.EDIT_IN_PROGRESS_VAULT)
            statuslist.add(Status.EDIT_IN_PROGRESS_MERCURY)
        }

        var companyMappingObj: CompanyTenantMapping = companyService.getCompanyTenantMappingObject(ds?:tenant)
                ?: throw RequestException("No tenant mapping found for ${tenant} : ds : $ds")

        val res = draftNsrRepo.findAll(DraftNsrSpecification(creditNoteNumber, draftId, approvedFrom, approvedTo, createdOnFrom,
                createdOnTo, createdBy, pendingFor, updatedBy, companyMappingObj.tenant, statuslist, companyMappingObj!!.companyId, partnerId), pagination)

        log.debug("Found ${res.totalElements} DraftNsr elements for company: ${companyMappingObj.companyId} : $tenant")

        res.content.forEach { draftNsr ->
            draftNsr.items = draftNsrItemRepo.getItemsByDraftNsrId(draftNsr.id)
            log.debug("draft items for DCN - ${draftNsr.id} : ${draftNsr.items} ")
        }

        return PaginationDto(res.totalElements, res.totalPages, res.hasPrevious(), res.hasNext(), res.content)
    }

    fun createNsrCreditNote(draftNsr: DraftNsr, user: String, tenant: String, debitNote: DebitNote): CreditNote {
        log.debug("Creating NSR Credit note: $draftNsr : $user : $tenant")

        val cn = CreditNote(
                id = 0,
                createdOn = now(),
                createdBy = draftNsr.createdBy,
                updatedOn = now(),
                updatedBy = draftNsr.createdBy,
                status = NoteStatus.PENDING,
                noteType = NoteTypes.NSR,
                amount = draftNsr.creditNoteAmount!!,
                supplierId = draftNsr.partnerId,
                supplierName = draftNsr.partnerName!!,
                remarks = "NSR Credit Note",
                settlementId = null,
                invoiceId = 0,
                debitNotes = mutableListOf(debitNote),
                closureType = CreditNoteClosureType.SETTLEMENT,
                partnerId = draftNsr.partnerId,
                partnerDetailId = draftNsr.partnerDetailId,
                tenant = draftNsr.tenant,
                creditNoteNumber = null,
                type = PartnerType.CUSTOMER,
                client = draftNsr.client!!,
                receiptStatus = ReceiptStatus.NOT_RECEIVED,
                expectedDate = LocalDate.now().plusDays(60),
                vendorCreditNoteDate = null,
                vendorCreditNoteAmount = null,
                vendorCreditNoteNumber = null,
                conversion = null,
                remainingAmount = draftNsr.creditNoteAmount!!,
                debitItems = debitNote.debitItems
        )

        return save(user, cn, tenant)
    }

    fun createNsrDebitNote(draftNsr: DraftNsr, user: String, tenant: String): DebitNote {
        log.debug("Inside create NSR Debit Note")

        val ndn = NsrDebitNoteDto(
                amountReceivable = draftNsr.creditNoteAmount!!,
                remarks = "",
                status = NoteStatus.PENDING,
                items = draftNsrItemRepo.getItemsByDraftNsrId(draftNsr.id),
                partnerId = draftNsr.partnerId,
                partnerName = draftNsr.partnerName!!,
                partnerDetailId = draftNsr.partnerDetailId,
                tenant = draftNsr.tenant,
                type = PartnerType.CUSTOMER,
                client = draftNsr.client!!
        )

        return debitNoteService.createNsrDebitNote(ndn, user)
    }

    @Transactional
    fun updateDraftNsr(user: String, draftNsr: DraftNsr, tenant: String, userName: String, ds: String? = null): CreditNote? {
        log.debug("Inside updateDraftNsrStatus $user : ${draftNsr.id}")

        var exDraftNsr = draftNsrRepo.getDraftNsrWithLock(draftNsr.id) ?: throw RequestException("Draft NSR not found for id: ${draftNsr.id}")

        var creditNote: CreditNote? = null

        if (Status.EDIT_IN_PROGRESS_VAULT == exDraftNsr.status) {

            var checkers = checkerService.findCheckers(tenant,null,ds)
            if (checkers.isEmpty()) throw RequestException("No checker found for this company !")
            var checker = checkerService.compareChecker(checkers,exDraftNsr.assignedToId?:"")

            if (!exDraftNsr.assignedToId.equals(user) || checker == null) {
                log.debug(" ${exDraftNsr.assignedToId} : ${checker?.userId} : $user ")
                throw RequestException("You are not authorized to change draft credit note status")
            }

            exDraftNsr.updatedBy = user
            exDraftNsr.updatedByName = userName
            exDraftNsr.updatedOn = now()
            exDraftNsr.status = Status.APPROVED
            exDraftNsr.approvalDate = LocalDate.now()
            exDraftNsr.comment = draftNsr.comment
            exDraftNsr.creditNoteAmount = draftNsr.creditNoteAmount
            exDraftNsr.totalMrp = draftNsr.totalMrp

            exDraftNsr.items = draftNsrItemRepo.getItemsByDraftNsrId(exDraftNsr.id)
            if (exDraftNsr.items.isNullOrEmpty()) throw RequestException("draft NSR items not found for draft NSR id: ${exDraftNsr.id}")

            exDraftNsr.creditNoteAmount = draftNsr.creditNoteAmount
            exDraftNsr.items!!.forEach { item ->
                val newItem = draftNsr.items!!.single { it.id == item.id }
                item.conversionAmount = newItem.conversionAmount
                item.conversionPercent = newItem.conversionPercent
            }
            val debitNote = createNsrDebitNote(exDraftNsr, user, tenant)
            creditNote = createNsrCreditNote(exDraftNsr, user, tenant, debitNote)
            exDraftNsr.creditNoteNumber = creditNote.creditNoteNumber
            exDraftNsr.debitNoteNumber = debitNote.debitNoteNumber

            if(ds==null) {
                val result = inwardProxy.updateNsrEntryStatusExternal(exDraftNsr.tenant, exDraftNsr.nsrEntryId, Status.APPROVED, exDraftNsr.tenant, user, creditNote.creditNoteNumber)
                if (!result.successful) throw RequestException("update status failed with mesg: ${result.message}")

            }
            exDraftNsr = draftNsrRepo.save(exDraftNsr)
        }
        else {
            throw RequestException("The DraftNSR is in ${exDraftNsr.status}")
        }

        return creditNote
    }

    @Transactional
    fun updateDraftNsrEntryStatusExternal(draftId: Long, status: Status, user: String, tenant: String, ds: String? = null): RequestStatusDto {
        log.debug("Update Draft NSR Entry status $status for : $draftId : $tenant")

        val ex = draftNsrRepo.getDraftNsrByDraftIdWithLock(draftId, tenant) ?: throw RequestException("NSR entry for id: $draftId not found")
        var result = RequestStatusDto(true, "")
        if (((status == Status.REJECTED || status == Status.PENDING_APPROVAL) && ex.status != Status.EDIT_IN_PROGRESS_MERCURY)
                || (status == Status.EDIT_IN_PROGRESS_MERCURY && ex.status != Status.PENDING_APPROVAL)){
            result.successful = false
            result.message = "Request failed due to NSR entry in Non-Editable status: ${ex.status}"
        }
        else {
            val v: User? = userProxy.getUser(user)
            var userName = user
            if(v != null)
                userName  = v.displayName

            ex.updatedBy = user
            ex.updatedOn = now()
            ex.updatedByName = userName
            ex.status = status
            draftNsrRepo.save(ex)
            result.message = "Request successful"
        }

        return result
    }

    @Transactional
    fun updateDraftNsrEntryStatusInternal(id: Long, status: Status, user: String, userName: String,ds: String? = null): RequestStatusDto {
        log.debug("Update Draft NSR Entry status $status for : $id")

        val ex = draftNsrRepo.getDraftNsrWithLock(id) ?: throw RequestException("NSR entry for id: $id not found")
        var result = RequestStatusDto(true, "")

        if ((status == Status.EDIT_IN_PROGRESS_VAULT && ex.status != Status.PENDING_APPROVAL)
                || (status == Status.PENDING_APPROVAL && ex.status != Status.EDIT_IN_PROGRESS_VAULT)) {
            result.successful = false
            result.message = "Request failed due to NSR entry in Non-Editable status: ${ex.status}"
        }
        else {
            ex.updatedBy = user
            ex.updatedByName = userName
            ex.updatedOn = now()
            ex.status = status
            draftNsrRepo.save(ex)
            result.message = "Request successful"

            if(ds==null)
            result = inwardProxy.updateNsrEntryStatusExternal(ex.tenant, ex.nsrEntryId, status, ex.tenant, user, null)
        }

        return result
    }

    fun getNsrCreditNoteData(creditNoteNumber: String): NsrCreditNotePrintDto {
        log.debug("Printing NSR Credit note pdf for credit note number: $creditNoteNumber")
        val nsrItems = mutableListOf<NsrItemDto>()
        val cn = creditNoteRepo.getByNumber(creditNoteNumber)?: throw RequestException("NSR CN with cn number : $creditNoteNumber not found")
        var taxVal = 0.0
        var taxableVal = 0.0
        var singleGstPercentage = BigDecimal.ZERO
        if(cn.noteType == NoteTypes.NSR) {
            val nsrCn = draftNsrRepo.getDraftNsrByCnNumber(creditNoteNumber)
                ?: throw RequestException("NSR CN with cn number : $creditNoteNumber not found")
            val items = draftNsrItemRepo.getItemsByDraftNsrId(nsrCn.id)
            if (items.isNullOrEmpty()) throw RequestException("NSR items for credit note number : $creditNoteNumber not found")
            items.forEach { item ->
                val quant = item.quantity
                if (quant > 0) {
                val nsrItem = NsrItemDto(
                    name = item.name!!,
                    batch = item.batch,
                    mrp = item.mrp.toDouble(),
                    expiryDate = item.expiry.toString(),
                    quant = quant,
                    conversionPercent = item.conversionPercent!!,
                    conversionAmount = item.conversionAmount!!,
                    reason = item.reason,
                    cgst =  "0",
                    cgstAmt = "0",
                    cnTaxableValue ="0",
                    cnTaxAmt = "0",
                    igst = "0",
                    igstAmt = "0",
                    sgst = "0",
                    sgstAmt ="0",
                    hsn ="",
                    returnDiscount = "0",
                    baseBillingRate = "0"
                )
                    nsrItems.add(nsrItem)
                }
            }
        }else {
            cn.debitItems.forEach { item ->
                val quant = (item.cnQty ?: item.returnQty ?: 0).toLong()
                if (quant > 0) {
                    val nsrItem = NsrItemDto(
                        name = item.name!!,
                        batch = item.batch,
                        mrp = item.amount.toDouble(),
                        expiryDate = item.expiry.toString(),
                        quant = quant,
                        conversionPercent = item.conversion?.toBigDecimal()!!,
                        conversionAmount = item.cnBillValue!!,
                        reason = cn.noteType.name,
                        cgst =  if(item.showGstSeparately == false) "0" else (item.cgst?:0.0).toString(),
                        cgstAmt = if(item.showGstSeparately == false) "0" else if(item.cgst?: BigDecimal.ZERO > BigDecimal.ZERO)
                        {
                                ((item.cnNetGST?: BigDecimal.ZERO).div(BigDecimal(2))).toString()
                        }else{
                             "0.0"
                        },
                        cnTaxableValue = item.cnTaxableValue.toString(),
                        cnTaxAmt = if(item.showGstSeparately == false) "0" else item.cnNetGST.toString(),
                        igst = if(item.showGstSeparately == false) "0" else item.igst.toString(),
                        igstAmt = if(item.showGstSeparately == false) "0" else if(item.igst?: BigDecimal.ZERO > BigDecimal.ZERO)
                        {
                            item.cnNetGST.toString()
                        } else {
                            "0.0"
                        },
                        sgst = if(item.showGstSeparately == false) "0" else item.sgst.toString(),
                        sgstAmt =if(item.showGstSeparately == false) "0" else if(item.sgst?: BigDecimal.ZERO > BigDecimal.ZERO)
                        {
                            (item.cnNetGST?.div(BigDecimal(2))).toString()
                        } else {
                            "0.0"
                        },
                        hsn = item.hsn ?: "",
                        baseBillingRate = (item.baseBillingRate ?: BigDecimal.ZERO).toString(),
                        returnDiscount = (item.returnDiscount ?: 0.0).toString()
                    )
                    if (item.showGstSeparately == true) {
                        taxVal = taxVal.plus((item.cnNetGST ?: BigDecimal.ZERO).toDouble())
                        taxableVal = taxableVal.plus((item.cnTaxableValue ?: BigDecimal.ZERO).toDouble())
                    }
                    nsrItems.add(nsrItem)
                    singleGstPercentage = item.gst?: BigDecimal(0)
                }
            }
        }

        log.debug("NSR Items = $nsrItems")

        val ctmObj = companyService.getCompanyTenantMappingObject(cn.tenant!!)?: throw RequestException("Company Mapping not found id: ${cn.tenant}")
        var companyMigrationCheck = companyLogsRepo.getCompanyData(ctmObj.companyId!!)
        var pdi = ctmObj.partnerDetailId
        if (companyMigrationCheck != null) {
            if (cn.createdOn!! <= companyMigrationCheck.migrationDate) {
                if(ctmObj.tenant.contains("th")) pdi = companyMigrationCheck.oldWhPdi
                else if(ctmObj.tenant.contains("ar")) pdi = companyMigrationCheck.oldArPdi
            }
        }
        var supplierObjects: PartnerGenericDetailDTO? =
            supplierProxy.partnerGenericDto(mutableListOf(cn.partnerDetailId?:0,pdi?:0))?:
            throw RequestException("Supplier info not found!")
        var map: Map<Int?, Data?>? = supplierObjects?.data?.associateBy { it?.partnerDetailId }?:
        throw RequestException("Supplier info not found!")
        var supplier: Data = map?.get(cn.partnerDetailId?.toInt()?:0) ?:throw RequestException("source info not found")
        var sourceSupplier = map.get(pdi?.toInt())?:throw RequestException("Supplier info not found!")
        val supplierForAddress = supplierProxy.supplier(null,cn.partnerDetailId)
        if (supplierForAddress.isEmpty()) throw RequestException("Wrong Supplier ID: ${cn.partnerId}")
        val sourceSupplierForAddress = supplierProxy.supplier(null,pdi)
        if (supplierForAddress.isEmpty()) throw RequestException("Wrong Supplier ID: ${cn.partnerId}")
        val addr2 = supplierForAddress[0]!!.partnerDetailList!![0]!!.address2 ?: ""
        var amount =  cn.amount!!.toDouble()
        val num: Float = amount.toFloat()
        val rupee = floor(num.toDouble()).toInt()
        val paisa = (("0."+amount.toString().split(".").get(1)).toFloat()*100).toInt()
        var fromDL = sourceSupplier.compliance?.filter { it.licenseType == LicenseType.DRUG_LICENSE.type && it.enabled==true }
        var fssai = sourceSupplier.compliance?.filter { it.licenseType == LicenseType.FSSAI.type && it.enabled==true  }
        var toDL = supplier.compliance?.filter { it.licenseType == LicenseType.DRUG_LICENSE.type && it.enabled==true  }

        var cin = sourceSupplier.compliance?.filter { it.licenseType == LicenseType.CIN.type && it.enabled==true }

        val amountWords = (englishNumberToWords.convert(rupee.toDouble())?.toUpperCase() + " RUPEES AND "
                + englishNumberToWords.convert(paisa.toDouble())?.toUpperCase() +" PAISA ONLY")
        val nsr = NsrCreditNotePrintDto(
                creditNoteNumber = cn.creditNoteNumber!!,
                supplierName = sourceSupplierForAddress[0]!!.partnerName!!,
                supplierAddress = "${sourceSupplierForAddress[0]!!.partnerDetailList?.get(0)!!.address1} \n ${sourceSupplierForAddress[0]!!.partnerDetailList?.get(0)!!.address2} " +
                        "\n ${sourceSupplierForAddress[0]!!.partnerDetailList?.get(0)?.city} , ${sourceSupplierForAddress[0]!!.partnerDetailList?.get(0)?.state} - ${sourceSupplierForAddress[0]!!.partnerDetailList?.get(0)?.pincode}",
                customerName = cn.supplierName!!,
                customerAddress = supplierForAddress[0]!!.partnerDetailList!![0]!!.address1 + " " + addr2 +
                        "\n ${supplierForAddress[0]!!.partnerDetailList?.get(0)?.city} , ${supplierForAddress[0]!!.partnerDetailList?.get(0)?.state} - ${supplierForAddress[0]!!.partnerDetailList?.get(0)?.pincode}",

                creditNoteDate = cn.createdOn!!.toLocalDate().toString(),
                creditNoteAmount = cn.amount!!.toDouble(),
                creditNoteAmountInWords = amountWords,
                creditNoteType = cn.noteType.name,
                itemList = nsrItems,
                fromGst = sourceSupplier.information.gst ?:"",
                toGst = supplier.information.gst ?:"",
                toStateId = supplierForAddress[0]?.partnerDetailList!!.get(0)?.stateId.toString(),
                toStateName =supplier.information.state ?:"",
                irn = cn.irn?:"",
                qr = cn.qrCode?:"",
                taxableValue = taxableVal,
                taxValue = taxVal,
                fromDL = if(fromDL.isNullOrEmpty()) "" else fromDL.get(0)?.licenseNo?:"",
                fssai =if(fssai.isNullOrEmpty()) "" else fssai.get(0)?.licenseNo?:"",
                toDL = if(toDL.isNullOrEmpty()) "" else toDL.get(0)?.licenseNo?:"",
                cin = if(cin.isNullOrEmpty()) "" else cin.get(0)?.licenseNo?:"",
                singleGstPercentage = singleGstPercentage.toString()
        )

        return nsr
    }

    fun getNsrCreditNotePdf(creditNoteNumber: String): String?{
        val nsr = getNsrCreditNoteData(creditNoteNumber)
        return  reportProxy.printNsrCreditNote(nsr)
    }

    @Transactional
    fun updateDiscountCNChecker(id: Long, checkerId: Long, tenant: String, userId: String, userName: String, ds: String? = null): Result {
        var checker = checkerService.findCheckers(tenant,checkerId,ds)

        if(checker.isNullOrEmpty())
            throw RequestException("No active checker found with id  $checkerId , for logged in company!")

        if(checker.size>1)
            throw RequestException("More than 1 checker found $checkerId , for logged in company!")

        var discountCNObj = draftCreditNoteRepo.get(id) ?: throw RequestException("No discount CN entry found with id $id !")

        if(discountCNObj.status != Status.PENDING_APPROVAL) throw RequestException("Discount CN with id $id is not in PENDING_APPROVAL state!!")

        val companyId = companyService.getCompanyTenantMappingObject(ds?:tenant)?.companyId
            ?: throw RequestException("Company not found for the given tenant")

        val checkerUserIdList = vaultCheckerService.getCheckerByCompanyId(companyId).mapNotNull { it?.userId }.toMutableList()

        if(userId != discountCNObj.createdBy && userId !in checkerUserIdList){
            throw RequestException("not a valid user cannot update")
        }

        discountCNObj.assignedToId = checker[0]?.userId
        discountCNObj.assignedTo = checker[0]?.userName
        discountCNObj.updatedBy = userId
        discountCNObj.updatedByName = userName

        draftCreditNoteRepo.save(discountCNObj)

        return success
    }

    @Transactional
    fun updateNSRChecker(id: Long, checkerId: Long, tenant: String, userId: String, userName: String, ds: String? = null): Result {
        var checker = checkerService.findCheckers(tenant,checkerId,ds)

        if(checker.isNullOrEmpty()) throw RequestException("No active checker found with id  $checkerId , for logged in company!")

        var draftNsrObj = draftNsrRepo.getDraftNsr(id) ?: throw RequestException("No nrs entry found with id $id !")

        if(draftNsrObj.status != Status.PENDING_APPROVAL) throw RequestException("NSR with id $id is not in PENDING_APPROVAL state!!")

        draftNsrObj.assignedToId = checker[0]?.userId
        draftNsrObj.assignedTo = checker[0]?.userName
        draftNsrObj.updatedBy = userId
        draftNsrObj.updatedByName = userName

        draftNsrRepo.save(draftNsrObj)

        return success
    }

    fun getDefaultTaxPercentageByNoteType (noteType: NoteTypes): Int {

        return when (noteType){

            NoteTypes.PURCHASE_SLAB_DISCOUNT_CN -> 12
            in listOf(NoteTypes.SALES_INCENTIVE_DISCOUNT_CN, NoteTypes.OTC_MARGIN_LEAKAGE_DISCOUNT_CN, NoteTypes. OFFLINE_PROMOTION_DISCOUNT_CN) -> 18
            NoteTypes.FINANCE_DISCOUNT_CN -> 0
            else -> throw IllegalArgumentException("invalid note type: $noteType")

        }
    }

    fun getItemName (noteType: NoteTypes): String {

        return when (noteType){

            NoteTypes.SLAB_DISCOUNT_CN -> "Slab Discount"
            NoteTypes.SALES_INCENTIVE_DISCOUNT_CN -> "Sales Incentive Discount"
            NoteTypes.OFFLINE_PROMOTION_DISCOUNT_CN -> "Offline Promotion Discount"
            NoteTypes.PURCHASE_SLAB_DISCOUNT_CN -> "Purchase Slab Discount"
            NoteTypes.OTC_MARGIN_LEAKAGE_DISCOUNT_CN -> "OTC Margin Leakage Discount"

            else -> "Discount"

        }
    }

    fun getHsnCode(taxPercentage: Int): String {
        return when (taxPercentage) {
            12 -> "300490"
            18 -> "998599"
            0 -> ""
            else -> throw IllegalArgumentException("Invalid tax percentage: $taxPercentage")
        }

    }


    @Transactional
    fun createEinvoiceObject(eInvoiceCreationKafkaDTOv2: EInvoiceCreationKafkaDTOv2){
        // fetch only id
        var cn: CreditNote = creditNoteRepo.getByNumber(eInvoiceCreationKafkaDTOv2.numberId!!)
            ?: throw NullPointerException("Unable to find Credit not number : ${eInvoiceCreationKafkaDTOv2.numberId.toString()}")
        //Einvoice creation not required for FINANCE_DISCOUNT_CN as tax is 0
        if(cn.noteType ==NoteTypes.FINANCE_DISCOUNT_CN){
            return
        }
        if(cn.client == InvoiceType.EASY_SOL){
            return
        }
        cn.debitNotes = debitNoteRepo.getDebitNotesForCreditNote(cn.id).toMutableList()
        log.info("inside createEinvoiceObject for credit note number: ${cn.creditNoteNumber}")
        //discuss: what is this theaTenant
        var theaTenant: String = eInvoiceCreationKafkaDTOv2.tenant
            ?: if (cn.tenant!!.startsWith("ds")){
                companyService.getTenantForDsTenant(cn.tenant!!)!!.tenant
            }else {
                cn.tenant!!
            }
        var  tenantCode = cn.tenant!!
        var ds : String? = null
        if(tenantCode.substring(0,2).equals("ds")) ds = tenantCode
        if(cn.type.name.equals(PartnerType.CUSTOMER.name) && cn.qrCode == null){
            var einvoiceList = mutableListOf<EinvoiceItem?>()
            var einvoice: EIClientResponseDto?
            var totalCNAmt= BigDecimal.ZERO
            var totalAssessable = BigDecimal.ZERO
            var totalCgst = BigDecimal.ZERO
            var totalSgst = BigDecimal.ZERO
            var totalIgst = BigDecimal.ZERO

            var ucodeList: Any?


            if(cn.debitNotes!![0].apiVersion == APIVersionType.V1){
                ucodeList = cn.debitItems.map { it.ucode }
                var hsnMap: MutableMap<String?, ProductDto?>

                if (cn.client.name.equals(InvoiceType.RIO.name)) {
                    hsnMap = inventoryProxy.getByUCodes(theaTenant!!, ucodeList).associateBy { it?.code }.toMutableMap()
                    cn.debitItems.forEach {
                        var productobj = hsnMap.get(it.ucode)
                        if (productobj != null) {
                            it.hsn = productobj.hsn
                        }
                    }


                }
            }
            var tenantMapping = companyService.getCompanyTenantMappingObject(cn.debitNotes?.get(0)!!.tenant)?: throw RequestException("Source ${cn.debitNotes?.get(0)!!.tenant} info not found! ")
            var sourceSupplierList = supplierProxy.supplier(null, tenantMapping.partnerDetailId)
            var sourceSupplier = if (sourceSupplierList.isNotEmpty()) sourceSupplierList.get(0) else throw RequestException("Could not get Source Supplier List for partnerId ${tenantMapping.partnerDetailId} for tenant " +
                    "${cn.debitNotes?.get(0)!!.tenant} ,while sale return credit note PDF creation.")
            var supplierList = supplierProxy.supplier(null, cn.debitNotes!!.get(0).partnerDetailId!!.toLong())
            var supplier = if (supplierList.isNotEmpty()) supplierList.get(0) else throw RequestException("Could not get Supplier List for partnerId ${cn.debitNotes?.get(0)!!.partnerDetailId} for tenant " +
                    "${cn.debitNotes?.get(0)!!.tenant} ,while sale return credit note PDF creation.")

            var einv = supplier?.partnerDetailList?.get(0)!!.einvoice
            log.info("einvoice flag is $einv for partner Id ${cn.debitNotes?.get(0)!!.partnerDetailId}")
            if(supplier!!.partnerDetailList?.get(0)!!.gst == null || sourceSupplier!!.partnerDetailList?.get(0)!!.gst == null ) {
                log.info("supplier or source supplier gst is null. Supplier gst: ${supplier!!.partnerDetailList?.get(0)!!.gst}, SourceSupplier gst: ${sourceSupplier!!.partnerDetailList?.get(0)!!.gst}")
                return
            }
            var supplierGst = supplier!!.partnerDetailList?.get(0)!!.gst!!.trim().substring(0,2)
            var sourceGst = sourceSupplier!!.partnerDetailList?.get(0)!!.gst!!.trim().substring(0,2)
            var interState =  supplierGst != sourceGst
            if(cn.debitNotes!![0].apiVersion == APIVersionType.V2)
            {
                try{
                    var items = retailIoProxy.getRioDebitItemData(
                        retailIoVersion,
                        retailIoSource,
                        retailIoKey,
                        cn.debitNotes!![0].logisticsPackageId!!,
                        true
                    ) ?: throw RequestException("Error fetching data from rio")
                    items.items.forEachIndexed { x, i ->

                        var cgst =
                            if (cn.noteType.name.equals(NoteTypes.SR_ACCEPTED.name) || cn.noteType.name.equals(NoteTypes.ST_RETURN.name)
                                || cn.noteType.name.equals(NoteTypes.ICS_RETURN.name) || cn.noteType.name.equals(
                                    NoteTypes.SR_EXPIRED.name
                                )
                                || cn.noteType.name.equals(NoteTypes.SR_DAMAGED.name)
                                || cn.noteType.name.equals(NoteTypes.NSR_ACCEPTED.name) || cn.noteType.name.equals(
                                    NoteTypes.NSR_EXPIRED.name
                                )
                                || cn.noteType.name.equals(NoteTypes.NSR_DAMAGED.name)
                            ) (i.cgstPercent ?: 0.0).toBigDecimal() else BigDecimal.ZERO
                        var igst =
                            if (cn.noteType.name.equals(NoteTypes.SR_ACCEPTED.name) || cn.noteType.name.equals(NoteTypes.ST_RETURN.name)
                                || cn.noteType.name.equals(NoteTypes.ICS_RETURN.name) || cn.noteType.name.equals(
                                    NoteTypes.SR_EXPIRED.name
                                )
                                || cn.noteType.name.equals(NoteTypes.SR_DAMAGED.name)
                                || cn.noteType.name.equals(NoteTypes.NSR_ACCEPTED.name) || cn.noteType.name.equals(
                                    NoteTypes.NSR_EXPIRED.name
                                )
                                || cn.noteType.name.equals(NoteTypes.NSR_DAMAGED.name)
                            ) (i.igstPercent ?: 0.0).toBigDecimal() else BigDecimal.ZERO
                        var sgst =
                            if (cn.noteType.name.equals(NoteTypes.SR_ACCEPTED.name) || cn.noteType.name.equals(NoteTypes.ST_RETURN.name)
                                || cn.noteType.name.equals(NoteTypes.ICS_RETURN.name) || cn.noteType.name.equals(
                                    NoteTypes.SR_EXPIRED.name
                                )
                                || cn.noteType.name.equals(NoteTypes.SR_DAMAGED.name)
                                || cn.noteType.name.equals(NoteTypes.NSR_ACCEPTED.name) || cn.noteType.name.equals(
                                    NoteTypes.NSR_EXPIRED.name
                                )
                                || cn.noteType.name.equals(NoteTypes.NSR_DAMAGED.name)
                            ) (i.sgstPercent ?: 0.0).toBigDecimal() else BigDecimal.ZERO

                        var taxPercentage = BigDecimal.ZERO
                        var cgstTotal: BigDecimal? = null
                        var sgstTotal: BigDecimal? = null
                        var igstTotal: BigDecimal? = null

                        if (!interState /*&& !cn.noteType.name.equals(NoteTypes.SR_EXPIRED.name)*/) {
                            cgstTotal = (i.cgstAmount ?: 0.0).toBigDecimal()
                            sgstTotal = (i.sgstAmount ?: 0.0).toBigDecimal()
                            totalCgst += cgstTotal ?: BigDecimal.ZERO
                            totalSgst += sgstTotal ?: BigDecimal.ZERO
                            taxPercentage = cgst + sgst

                        } else if (interState /*&& !cn.noteType.name.equals(NoteTypes.SR_EXPIRED.name)*/) {
                            igstTotal = (i.igstAmount ?: 0.0).toBigDecimal()
                            totalIgst += igstTotal?: BigDecimal.ZERO
                            taxPercentage = igst
                        }
                        var totalAmt = BigDecimal.ZERO
                        var assessableAmount = BigDecimal.ZERO
                        var totalItemVal = BigDecimal.ZERO
                        var unitPrice = BigDecimal.ZERO
                        if (cn.noteType.name.equals(NoteTypes.SR_ACCEPTED.name) || cn.noteType.name.equals(NoteTypes.ST_RETURN.name)
                            || cn.noteType.name.equals(NoteTypes.ICS_RETURN.name) || cn.noteType.name.equals(NoteTypes.SR_EXPIRED.name)
                            || cn.noteType.name.equals(NoteTypes.SR_DAMAGED.name)
                            || cn.noteType.name.equals(NoteTypes.NSR_ACCEPTED.name) || cn.noteType.name.equals(NoteTypes.NSR_EXPIRED.name)
                            || cn.noteType.name.equals(NoteTypes.NSR_DAMAGED.name)
                        ) {
                            totalAmt = i.cnTaxableAmount!!.toBigDecimal()
                            assessableAmount = i.cnTaxableAmount!!.toBigDecimal()
                            totalItemVal = i.cnPayableAmount!!.toBigDecimal()
                            totalAssessable += assessableAmount
                            totalCNAmt += totalItemVal
                            unitPrice = assessableAmount / i.returnQuantity!!.toBigDecimal()
                        }


                        if (cn.noteType.name.equals(NoteTypes.SR_ACCEPTED.name) || cn.noteType.name.equals(NoteTypes.ST_RETURN.name)
                            || cn.noteType.name.equals(NoteTypes.ICS_RETURN.name) || cn.noteType.name.equals(NoteTypes.SR_EXPIRED.name)
                            || cn.noteType.name.equals(NoteTypes.SR_DAMAGED.name)
                            || cn.noteType.name.equals(NoteTypes.NSR_ACCEPTED.name) || cn.noteType.name.equals(NoteTypes.NSR_EXPIRED.name)
                            || cn.noteType.name.equals(NoteTypes.NSR_DAMAGED.name)
                        ) {

                            einvoiceList.add(
                                EinvoiceItem(
                                    assessableAmount,
                                    taxPercentage,
                                    i.hsnCode,
                                    i.returnQuantity,
                                    x,
                                    totalAmt,
                                    totalItemVal,
                                    unitPrice,
                                    BigDecimal.ZERO,
                                    cgstTotal,
                                    sgstTotal,
                                    igstTotal,
                                    i.itemName
                                )
                            )

                        } else {
                            einvoiceList.add(
                                EinvoiceItem(
                                    assessableAmount,
                                    BigDecimal.ZERO,
                                    i.hsnCode,
                                    i.returnQuantity,
                                    x,
                                    totalAmt,
                                    totalItemVal,
                                    unitPrice,
                                    BigDecimal.ZERO,
                                    cgstTotal,
                                    sgstTotal,
                                    BigDecimal.ZERO,
                                    i.itemName
                                )
                            )

                        }
                    }
                }catch (e: Exception){
                    log.error("Error while generating einvoice data via rio proxy, $e")
                    throw e
                }
            }else if((cn.noteType == NoteTypes.SLAB_DISCOUNT_CN||cn.noteType == NoteTypes.OTC_MARGIN_LEAKAGE_DISCOUNT_CN||
                        cn.noteType ==NoteTypes.SALES_INCENTIVE_DISCOUNT_CN||
                        cn.noteType ==NoteTypes.OFFLINE_PROMOTION_DISCOUNT_CN||
                        cn.noteType ==NoteTypes.PURCHASE_SLAB_DISCOUNT_CN) && cn.debitNotes!![0].apiVersion == APIVersionType.V1)
            {
                try{
                    var taxPercentage = getDefaultTaxPercentageByNoteType(cn.noteType)
                    var hsnCode=getHsnCode(taxPercentage)
                    val taxAmount = cn.amount.multiply(taxPercentage.toBigDecimal()).divide(BigDecimal(100))


                    var cgstTotal: BigDecimal? = null
                    var sgstTotal: BigDecimal? = null
                    var igstTotal: BigDecimal? = null

                    if (!interState ) {
                        cgstTotal = taxAmount.divide(BigDecimal(2))
                        sgstTotal = taxAmount.divide(BigDecimal(2))
                        totalCgst = cgstTotal?: BigDecimal.ZERO
                        totalSgst = sgstTotal?: BigDecimal.ZERO

                    } else if (interState) {
                        igstTotal = taxAmount
                        totalIgst = taxAmount
                    }

                    var totalAmt = cn.amount
                    var assessableAmount = cn.amount.subtract(taxAmount)
                    var totalItemVal = cn.amount.subtract(taxAmount)
                    totalAssessable = cn.amount.subtract(taxAmount)
                    totalCNAmt = cn.amount
                    var unitPrice = assessableAmount
                    val serviceHsn = when(hsnCode){
                        "300490" -> "N"
                        "998599" -> "Y"
                        else -> throw IllegalArgumentException("Invalid HSN code: $hsnCode")
                    }
                    einvoiceList.add(
                        EinvoiceItem(
                            assessableAmount,
                            taxPercentage.toBigDecimal(),
                            hsnCode,
                            1,
                            1,
                            totalItemVal,
                            totalAmt,
                            unitPrice,
                            BigDecimal.ZERO,
                            cgstTotal,
                            sgstTotal,
                            igstTotal,
                            getItemName(cn.noteType),
                            serviceHsn
                        )
                    )

                }catch (e: RequestException) {
                    log.error("RequestException while generating e-invoice data for discount CN: ${e.message}")
                    throw e
                } catch (e: IllegalArgumentException) {
                    log.error("Invalid argument while generating e-invoice data for discount CN: ${e.message}")
                    throw e
                } catch (e: Exception) {
                    log.error("Unexpected error while generating e-invoice data for discount CN: ${e.message}")
                    throw e
                }
            }
            else {
                try{
                    cn.debitItems.forEachIndexed { x, i ->

                        var cgst =
                            if (cn.noteType.name.equals(NoteTypes.SR_ACCEPTED.name) || cn.noteType.name.equals(NoteTypes.ST_RETURN.name)
                                || cn.noteType.name.equals(NoteTypes.ICS_RETURN.name) || cn.noteType.name.equals(
                                    NoteTypes.SR_EXPIRED.name
                                )
                                || cn.noteType.name.equals(NoteTypes.SR_DAMAGED.name)
                                || cn.noteType.name.equals(NoteTypes.NSR_ACCEPTED.name) || cn.noteType.name.equals(
                                    NoteTypes.NSR_EXPIRED.name
                                )
                                || cn.noteType.name.equals(NoteTypes.NSR_DAMAGED.name)
                            )
                                i.cgst!!
                            else
                                BigDecimal.ZERO
                        var igst =
                            if (cn.noteType.name.equals(NoteTypes.SR_ACCEPTED.name) || cn.noteType.name.equals(NoteTypes.ST_RETURN.name)
                                || cn.noteType.name.equals(NoteTypes.ICS_RETURN.name) || cn.noteType.name.equals(
                                    NoteTypes.SR_EXPIRED.name
                                )
                                || cn.noteType.name.equals(NoteTypes.SR_DAMAGED.name)
                                || cn.noteType.name.equals(NoteTypes.NSR_ACCEPTED.name) || cn.noteType.name.equals(
                                    NoteTypes.NSR_EXPIRED.name
                                )
                                || cn.noteType.name.equals(NoteTypes.NSR_DAMAGED.name)
                            ) i.igst!! else BigDecimal.ZERO
                        var sgst =
                            if (cn.noteType.name.equals(NoteTypes.SR_ACCEPTED.name) || cn.noteType.name.equals(NoteTypes.ST_RETURN.name)
                                || cn.noteType.name.equals(NoteTypes.ICS_RETURN.name) || cn.noteType.name.equals(
                                    NoteTypes.SR_EXPIRED.name
                                )
                                || cn.noteType.name.equals(NoteTypes.SR_DAMAGED.name)
                                || cn.noteType.name.equals(NoteTypes.NSR_ACCEPTED.name) || cn.noteType.name.equals(
                                    NoteTypes.NSR_EXPIRED.name
                                )
                                || cn.noteType.name.equals(NoteTypes.NSR_DAMAGED.name)
                            ) i.sgst!! else BigDecimal.ZERO

                        var taxPercentage = BigDecimal.ZERO
                        var cgstTotal: BigDecimal? = null
                        var sgstTotal: BigDecimal? = null
                        var igstTotal: BigDecimal? = null

                        if (!interState /*&& !cn.noteType.name.equals(NoteTypes.SR_EXPIRED.name)*/) {
                            cgstTotal = i.cnNetGST?.div(BigDecimal(2)) ?: BigDecimal.ZERO
                            sgstTotal = i.cnNetGST?.div(BigDecimal(2)) ?: BigDecimal.ZERO
                            totalCgst += cgstTotal ?: BigDecimal.ZERO
                            totalSgst += sgstTotal ?: BigDecimal.ZERO
                            taxPercentage = cgst + sgst

                        } else if (interState /*&& !cn.noteType.name.equals(NoteTypes.SR_EXPIRED.name)*/) {
                            igstTotal = i.cnNetGST
                            totalIgst += igstTotal ?: BigDecimal.ZERO
                            taxPercentage = igst
                        }
                        var totalAmt = BigDecimal.ZERO
                        var assessableAmount = BigDecimal.ZERO
                        var totalItemVal = BigDecimal.ZERO
                        var unitPrice = BigDecimal.ZERO
                        if (cn.noteType.name.equals(NoteTypes.SR_ACCEPTED.name) || cn.noteType.name.equals(NoteTypes.ST_RETURN.name)
                            || cn.noteType.name.equals(NoteTypes.ICS_RETURN.name) || cn.noteType.name.equals(NoteTypes.SR_EXPIRED.name)
                            || cn.noteType.name.equals(NoteTypes.SR_DAMAGED.name)
                            || cn.noteType.name.equals(NoteTypes.NSR_ACCEPTED.name) || cn.noteType.name.equals(NoteTypes.NSR_EXPIRED.name)
                            || cn.noteType.name.equals(NoteTypes.NSR_DAMAGED.name)
                        ) {
                            totalAmt = i.cnTaxableValue
                            assessableAmount = i.cnTaxableValue
                            totalItemVal = i.cnBillValue
                            totalAssessable += assessableAmount ?: BigDecimal.ZERO
                            totalCNAmt += totalItemVal ?: BigDecimal.ZERO
                            unitPrice = assessableAmount / i.returnQty!!.toBigDecimal()
                        }


                        if (cn.noteType.name.equals(NoteTypes.SR_ACCEPTED.name) || cn.noteType.name.equals(NoteTypes.ST_RETURN.name)
                            || cn.noteType.name.equals(NoteTypes.ICS_RETURN.name) || cn.noteType.name.equals(NoteTypes.SR_EXPIRED.name)
                            || cn.noteType.name.equals(NoteTypes.SR_DAMAGED.name)
                            || cn.noteType.name.equals(NoteTypes.NSR_ACCEPTED.name) || cn.noteType.name.equals(NoteTypes.NSR_EXPIRED.name)
                            || cn.noteType.name.equals(NoteTypes.NSR_DAMAGED.name)
                        ) {

                            einvoiceList.add(
                                EinvoiceItem(
                                    assessableAmount,
                                    taxPercentage,
                                    i.hsn,
                                    i.returnQty,
                                    x,
                                    totalAmt,
                                    totalItemVal,
                                    unitPrice,
                                    BigDecimal.ZERO,
                                    cgstTotal,
                                    sgstTotal,
                                    igstTotal,
                                    i.name
                                )
                            )

                        } else {
                            einvoiceList.add(
                                EinvoiceItem(
                                    assessableAmount,
                                    BigDecimal.ZERO,
                                    i.hsn,
                                    i.returnQty,
                                    x,
                                    totalAmt,
                                    totalItemVal,
                                    unitPrice,
                                    BigDecimal.ZERO,
                                    cgstTotal,
                                    sgstTotal,
                                    BigDecimal.ZERO,
                                    i.name
                                )
                            )

                        }
                    }
                }catch (e: Exception){
                    log.error("Error while generating data for einvoice, $e")
                    throw e
                }
            }

            if(einv==true){
                einvoice = einvoiceObjCreation(cn, supplier!!, einvoiceList, sourceSupplier,totalAssessable,tenantMapping.theaId,totalCNAmt,totalCgst,totalSgst,totalIgst,ds,tenantMapping.tenant)

            }else{
                einvoice=null
            }
            if(einvoice?.status == EInvoiceType.FAILED){
                log.error("EInvoice creation failed for ${cn.creditNoteNumber!!} Consolidated store invoice. throwing exception for kafka retry")
                throw RequestException("EInvoice creation failed for ${cn.creditNoteNumber!!} Consolidated store invoice.")
            }
            if(einvoice!= null){
                var creditNoteFromDB = creditNoteRepo.getLockedCN(cn.id)
                creditNoteFromDB.qrCode = einvoice.qrCode
                creditNoteFromDB.irn = einvoice.irn
                creditNoteRepo.save(creditNoteFromDB)
            }

        }
    }


    @Transactional
    fun generateEinvoice(creditNoteNumber: String,tenant:String): Result {
        var credit = creditNoteRepo.getByNumber(creditNoteNumber)?:throw RequestException("No CN entry found with id $creditNoteNumber !")
        credit.debitNotes = debitNoteRepo.getByCreditNoteId(credit.id) as MutableList<DebitNote>?
        credit.debitNotes?.forEach {
            it.bkInvoices = mutableListOf()
            it.purchaseReturns = mutableListOf()
        }

        var updatedTenant =if (tenant.startsWith("ds")){
            companyService.getTenantForDsTenant(tenant)!!.tenant
        }else {
            tenant
        }
        //task: change this to EinvoiceKafkaV2 ~ done
        var einvoiceKafka = EinvoiceKafkaDto(cn=credit, tenant=updatedTenant)
        log.info("Einvoice Retry for Credit Note: ${creditNoteNumber}")
        einvoiceSinkPusher.genericEInvoiceCreationProducer(convertPayloadToV2(einvoiceKafka))
        //createEinvoiceObject(credit,tenant)
        return Result(200, "success")
    }

    fun getCreditNotesBySettlementId(settlementId: Long): List<CreditNote>? {
        // this method checks in the credit_note_settlement mapping table after the PARTIAL CN usage.
        // If not found, it will find based on the settlement id in the creditnote table (before PARTIAL CN feature)
        var creditNoteSettlementMappings = creditNoteSettlementRepo.getCNSBySettlementIdV2(settlementId)
        if(creditNoteSettlementMappings != null) {
            var creditNotes = creditNoteRepo.findAllById(creditNoteSettlementMappings.map { it.creditNoteId })
            var creditNoteMappings = mapOf<Long?, CreditNote>()
            creditNoteMappings = creditNotes.associateBy { it.id }
            var resCreditNotesCopy = mutableListOf<CreditNote>()
            for (cns in creditNoteSettlementMappings) {
                var creditNoteCopy = creditNoteMappings.get(cns.creditNoteId)!!.copy()
                creditNoteCopy.amountUsed = cns.paidAmount
                resCreditNotesCopy.add(creditNoteCopy)
            }
            return resCreditNotesCopy
        }
        else {
            var creditNotes = creditNoteRepo.getBySettlementId(settlementId)
            var resCreditNotesCopy = mutableListOf<CreditNote>()
            if (!creditNotes.isNullOrEmpty()) {
                for (cn in creditNotes) {
                    var creditNoteCopy = cn.copy()
                    creditNoteCopy.amountUsed = cn.amount
                    resCreditNotesCopy.add(creditNoteCopy)
                }
            }
            return resCreditNotesCopy
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    fun createCreditNoteForSlabTask(slabTask: SlabTask, slabTaskDetail: SlabTaskDetail, user: String,
                                    ds: String? = null, company: Company?) {
        log.debug("Creating SDCN for partnerid: ${slabTaskDetail.partnerId} in taskId: ${slabTask.id}")
        val amount = SlabTaskUtils.getCreditNoteAmount(slabTaskDetail)
        if(!SlabTaskUtils.isValidCreditNoteAmount(amount)) {
            log.debug("Credit note amount = $amount for partnerId: ${slabTaskDetail.partnerId} in taskId: ${slabTask.id}")
            return
        }
        var creditNote = CreditNote(
            id = 0,
            createdOn = now(),
            updatedOn = now(),
            createdBy = user,
            updatedBy = user,
            status = NoteStatus.PENDING,
            noteType = NoteTypes.SLAB_DISCOUNT_CN,
            amount = amount,
            supplierId = slabTaskDetail.partnerId,
            supplierName = slabTaskDetail.partnerName,
            remarks = "Created by slab task",
            settlementId = null,
            invoiceId = null,
            debitNotes = null,
            closureType = if(ds!=null) CreditNoteClosureType.SETTLEMENT else CreditNoteClosureType.MANUAL,
            partnerId = slabTaskDetail.partnerId,
            partnerDetailId = slabTaskDetail.partnerDetailId,
            tenant = if(ds!=null) slabTask.company.tenant else slabTask.tenant,
            creditNoteNumber = documentMasterService.getDocumentNumber(user, company?.companyCode!!, DocumentType.CX_CN_DISCOUNT),
            type = PartnerType.CUSTOMER,
            client = slabTaskDetail.client,
            receiptStatus = ReceiptStatus.RECEIVED,
            expectedDate = null,
            vendorCreditNoteDate = null,
            vendorCreditNoteAmount = null,
            vendorCreditNoteNumber = null,
            slabTaskDetail = slabTaskDetail
        )
        log.debug("SDCN created for partnerid: ${slabTaskDetail.partnerId} in taskId: ${slabTask.id}")
        creditNote = creditNoteRepo.save(creditNote)

        debitNoteService.createDebitNoteForSlabTask(creditNote, company.companyCode,true)
    }

    @Transactional
    fun reserveCreditNoteForExternalOrderId(creditNoteReservationPayloadDTO: CreditNoteReservationPayloadDTO): List<CreditNoteReservationDTO>? {
        var reservedCNs = creditNoteReservationRepo.findByExternalOrderIdAndStatuses(creditNoteReservationPayloadDTO.externalOrderId, creditNoteReservationPayloadDTO.orderType, mutableListOf(CreditNoteReservationStatus.RESERVED, CreditNoteReservationStatus.COMPLETED))
        if(!reservedCNs.isNullOrEmpty()){
            return reservedCNs
        } else if(now(ZoneId.of("Asia/Kolkata")).hour >= 23){
            // avoiding reservations post 11pm to initiate cleanup activities
            return reservedCNs
        } else {
            var page = 0
            var size = 100
            var hasNext: Boolean = true
            var eligibleCNs : MutableList<CreditNote> = mutableListOf()
            var requiredCNAmount = creditNoteReservationPayloadDTO.payableAmount
            var partnerId: Long? = supplierProxy.getMasterId(creditNoteReservationPayloadDTO.partnerDetailId) ?: return reservedCNs
            var supplierList = supplierProxy.supplier(listOf(partnerId))
            var partnerName = if (supplierList.isNotEmpty()) supplierList.get(0)?.partnerName ?: return reservedCNs else return reservedCNs

            var ds = if(creditNoteReservationPayloadDTO.orderType == OrderType.B2B2B){
                companyService.getTenantByPDI(creditNoteReservationPayloadDTO.partnerDetailId)
            }else{
                null
            }
            var tenant = companyService.findTenants(ds?.tenant?:creditNoteReservationPayloadDTO.tenant)
            val company = companyService.getCompanyAccountDetailsByTenant(tenant[0]!!)
            var exitWhileLoop = false
            var source = if(creditNoteReservationPayloadDTO.orderType == OrderType.B2B) {CNReservationSource.ORDER_SERVICE} else {CNReservationSource.STORE_SERVICE}

            val config = cnConfigService.getNoteTypesByAutoSettlementEnabled(partnerId= partnerId!!, partnerName = partnerName, tenant = tenant.first(), partnerDetailId = creditNoteReservationPayloadDTO.partnerDetailId)
            val enabledNoteTypes = config.map { it.noteType }.toMutableList()
            val noteTypeAmountMap = config.associateBy({it.noteType}, {it.autoSettlementLimit})

            while(requiredCNAmount > BigDecimal.ZERO && hasNext && !exitWhileLoop) {
                var modifiedCreditNotes : MutableList<CreditNote> = mutableListOf()
                val pagination = PageRequest.of(page, size)
                var pageResult = creditNoteRepo.getCustomerCreditNotesByStatusAndPartnerDetailId(statuses=mutableListOf(NoteStatus.PENDING, NoteStatus.PARTIAL_REALIZED), partnerId = partnerId, tenant = tenant[0]!!, eligibleNoteTypes = enabledNoteTypes, pageable = pagination)
                for (it in pageResult.content) {
                    if(enabledNoteTypes.contains(it.noteType)) {
                        if (company.isFullCnSettlementEnabled && it.remainingAmount > requiredCNAmount) {
                            exitWhileLoop = true
                            break
                        }

                        if ((requiredCNAmount > BigDecimal.ZERO) and (it.remainingAmount > BigDecimal.ZERO)) {
                            var cnCopy = creditNoteRepo.getLockedCN(it.id)!!.copy()

                            if(BigDecimal(noteTypeAmountMap[cnCopy.noteType]?:0.0) < cnCopy.remainingAmount){
                                continue
                            }
                            var eligibleCNAmount = if (requiredCNAmount < cnCopy.remainingAmount) {
                                requiredCNAmount
                            } else {
                                cnCopy.remainingAmount
                            }
                            requiredCNAmount -= eligibleCNAmount
                            cnCopy.remainingAmount -= eligibleCNAmount
                            cnCopy.amountUsed = eligibleCNAmount
                            cnCopy.status = if (cnCopy.remainingAmount > BigDecimal.ZERO) {
                                NoteStatus.PARTIAL_REALIZED
                            } else {
                                NoteStatus.REALIZED
                            }
                            eligibleCNs.add(cnCopy)
                            modifiedCreditNotes.add(cnCopy)
                        }
                    }
                }
                if (modifiedCreditNotes.isNotEmpty()) {
                    creditNoteRepo.saveAll(modifiedCreditNotes)
                    modifiedCreditNotes = mutableListOf()
                }
                hasNext = pageResult.hasNext()
                page += 1
            }


            if(eligibleCNs.isNullOrEmpty()){
                return reservedCNs
            }
            var cnReservation = CreditNoteReservation(0L, now(), null, null, CreditNoteReservationStatus.RESERVED,
                    creditNoteReservationPayloadDTO.payableAmount, creditNoteReservationPayloadDTO.payableAmount - requiredCNAmount, creditNoteReservationPayloadDTO.externalOrderId,
                    partnerId!!, creditNoteReservationPayloadDTO.partnerDetailId, creditNoteReservationPayloadDTO.tenant, creditNoteReservationPayloadDTO.orderType, source)
            creditNoteReservationRepo.save(cnReservation)
            var cnReservationMappings: MutableList<CreditNoteReservationMapping> = mutableListOf()
            var cnReservationDTOs: MutableList<CreditNoteReservationDTO> = mutableListOf()
            eligibleCNs.forEach{cn ->
                cnReservationMappings.add(CreditNoteReservationMapping(0L, now(), cn, cn.amountUsed!!, cnReservation))
                cnReservationDTOs.add(CreditNoteReservationDTO(cn.id, cn.creditNoteNumber!!, cn.amountUsed!!, cn.amount,cn.noteType))
            }
            creditNoteReservationMappingRepo.saveAll(cnReservationMappings)
            return cnReservationDTOs
        }
    }

    @Async
    @Transactional
    fun releaseReservedCNs(){
        var unUsedCNReservations = creditNoteReservationRepo.getCNReservations()
        if(unUsedCNReservations.isNullOrEmpty()){
            return
        }
        var emailBody: String = ""
        unUsedCNReservations.forEach { cnr ->
            var lockedCNR = creditNoteReservationRepo.getLockedCNR(cnr.id)
            if(lockedCNR.status == CreditNoteReservationStatus.RESERVED) {
                try {
                    storeProxy.getRetailerInvoice(cnr.externalOrderId,cnr.tenant)?: throw RequestException("Store invoice not found for externalOrderId : ${cnr.externalOrderId}!")
                } catch (e: Exception) {
                    when(e) {
                        is ProxyException, is RequestException -> {
                            lockedCNR.status = CreditNoteReservationStatus.RELEASED
                            lockedCNR.releasedOn = now()
                            creditNoteReservationRepo.save(lockedCNR)
                            var blockedCNs = creditNoteReservationRepo.findByCNRId(cnr.id)
                            if (!blockedCNs.isNullOrEmpty()) {
                                blockedCNs.forEach { cnrDto ->
                                    var dbCN = creditNoteRepo.getLockedCN(cnrDto.creditNoteId)
                                    dbCN.remainingAmount = dbCN.remainingAmount + cnrDto.amountUsed
                                    dbCN.status = if (dbCN.amount == dbCN.remainingAmount) {
                                        NoteStatus.PENDING
                                    } else {
                                        NoteStatus.PARTIAL_REALIZED
                                    }
                                    dbCN.updatedOn = now()
                                    creditNoteRepo.save(dbCN)
                                }
                            }
                            emailBody = emailBody.plus("Order: ${cnr.externalOrderId}, Reserved Amount: ${cnr.reservedAmount}\n")
                        }
                        else -> {
                            emailBody = emailBody.plus("Exception Order: ${cnr.externalOrderId}, Reserved Amount: ${cnr.reservedAmount} ${e}\n")
                        }
                    }
                }
            }
        }
        if(emailBody.isNotEmpty()) {
            emailService.sendMessage("Alert: Cleaned Up Reservations on ${now()}", emailBody.toString(),
                    "<EMAIL>","<EMAIL>",false)
        }
        return
    }
    fun getAllFirms(): List<FirmDTO?>? {
        var firms = supplierProxy.getFirmTypes(true,null,null)
        return firms
    }

    fun createAdvancePaymentCNWithInvoice(amount: BigDecimal, bkInvoice: BkInvoice): CreditNote? {
        return createCreditNoteForExcessPayment(amount, bkInvoice.tenant!!, bkInvoice.partnerId!!,
                                                bkInvoice.partnerDetailId!!, "SYSTEM")
    }

    fun createAdvancePaymentCNWithoutInvoice(amount: BigDecimal, partnerDetailId: Long, distributorId: Long?, tenants: String?): CreditNote? {
        val tenant = tenants
            ?: when (distributorId) {
                null -> coreServiceProxy.getRetailerByPartnerDetailId(partnerDetailId)?.tenant
                else -> invoiceService.getTenantCodeAndCustomerType(distributorId).tenant
            }

        if (tenant.isNullOrEmpty()){
            throw RequestException("Tenant not found. distributorId: ${distributorId} partnerDetailId: ${partnerDetailId}")
        }

        val pdiInfo = supplierProxy.getPartnerDetails(null, null, null, null, partnerDetailId.toInt())!![0]
        val storeExternalRetailerMappingList = supplierProxy.getStoreExternalRetailerMappingList(partnerDetailId.toInt(),true).data
        val ds = !storeExternalRetailerMappingList.isNullOrEmpty()
        return createCreditNoteForExcessPayment(
            amount, companyService.findTenants(tenant)[0]!!,
            pdiInfo.partnerId!!.toLong()!!, partnerDetailId, "SYSTEM")
    }

    @Transactional(rollbackFor = [Exception::class])
    fun createCreditNoteForExcessPayment(amount: BigDecimal, tenant: String, partnerId: Long,
                                         partnerDetailId: Long, user: String): CreditNote? {
        log.debug("Creating Advance Payment for partner id: ${partnerId} for amount: $amount")
        if(amount<BigDecimal(1)){
            return null
        }
        var company = companyRepo.getCompanyByTenant(tenant)
        var partnerInfo = supplierProxy.supplier(listOf(partnerId))[0]!!
        var creditNote = CreditNote(
            id = 0,
            createdOn = now(),
            updatedOn = now(),
            createdBy = user,
            updatedBy = user,
            status = NoteStatus.PENDING,
            noteType = NoteTypes.ADVANCE_PAYMENT,
            amount = amount,
            supplierId = partnerId,
            supplierName = partnerInfo.partnerName!!,
            remarks = "Created via excess payment",
            settlementId = null,
            invoiceId = null,
            debitNotes = null,
            closureType = CreditNoteClosureType.SETTLEMENT,
            partnerId = partnerId,
            partnerDetailId = partnerDetailId,
            tenant = tenant,
            creditNoteNumber = documentMasterService.getDocumentNumber(user, company?.companyCode!!, DocumentType.CN_ADVANCE_PAYMENT),
            type = PartnerType.CUSTOMER,
            client = InvoiceType.RIO,
            receiptStatus = ReceiptStatus.RECEIVED,
            expectedDate = null,
            vendorCreditNoteDate = null,
            vendorCreditNoteAmount = null,
            vendorCreditNoteNumber = null
        )
        log.debug("Advance CN created for partner id: $partnerId for amount: ${amount}")
        creditNote = creditNoteRepo.save(creditNote)
        debitNoteService.createDebitNoteForAdvanceCN(creditNote, company.companyCode)
        return creditNote
    }


    @Transactional
    fun reserveCreditNoteV2ForExternalOrderId(creditNoteReservationV2PayloadDTO: CreditNoteReservationV2PayloadDTO): List<CreditNoteReservationDTO>? {
        log.info("reserveCreditNoteV2ForExternalOrderId externalOrderId: ${creditNoteReservationV2PayloadDTO.externalOrderId}, distributorId: ${creditNoteReservationV2PayloadDTO.distributorId}, amount: ${creditNoteReservationV2PayloadDTO.amount}, retailerId: ${creditNoteReservationV2PayloadDTO.retailerId}")
        val distributorPartnerMapping = invoiceService.getTenantCodeAndCustomerType(
            creditNoteReservationV2PayloadDTO.distributorId
        )

        val distributorTenant = distributorPartnerMapping.tenant
        val orderType = distributorPartnerMapping.fulfilmentType
        val isB3Order = orderType==OrderType.B2B2B
        val ds = if(isB3Order){
            companyService.getTenantByPDI(creditNoteReservationV2PayloadDTO.distributorId)
        }else{
            null
        }
        val companyTenantMapping = companyService.getCompanyTenantMappingObject(ds?.tenant?:distributorTenant)
        if (companyTenantMapping == null) {
            throw RequestException("CompanyTenantMapping not found for tenant: ${ds?.tenant?:distributorTenant}")
        }
        val companyId = companyTenantMapping.companyId
        val tenant = companyTenantMapping.tenant
        val company = companyService.getCompanyAccountDetailsByTenant(tenant!!)
        var exitWhileLoop = false
        var reservation = creditNoteReservationRepo.findByExternalOrderIdAndOrderType(creditNoteReservationV2PayloadDTO.externalOrderId, orderType)
        var reservedCNs = creditNoteReservationRepo.findByExternalOrderIdAndStatuses(
            creditNoteReservationV2PayloadDTO.externalOrderId,
            orderType,
            mutableListOf(CreditNoteReservationStatus.RESERVED, CreditNoteReservationStatus.COMPLETED)
        )
        if (!reservedCNs.isNullOrEmpty() || reservation?.status == CreditNoteReservationStatus.COMPLETED) {
            return reservedCNs
        } else if (now(ZoneId.of("Asia/Kolkata")).hour >= 23) {
            // TODO: Check why is this written, avoiding reservations post 11pm to initiate cleanup activities
            return reservedCNs
        } else {
            var page = 0
            val size = 100
            var hasNext: Boolean = true
            val eligibleCNs: MutableList<CreditNote> = mutableListOf()
            var requiredCNAmount = creditNoteReservationV2PayloadDTO.amount
            val partnerId: Long =
                supplierProxy.getMasterId(creditNoteReservationV2PayloadDTO.retailerId) ?: return reservedCNs
            val supplierList = supplierProxy.supplier(listOf(partnerId))
            val partnerName = if (supplierList.isNotEmpty()) supplierList.get(0)?.partnerName ?: return reservedCNs else return reservedCNs

            val config = cnConfigService.getNoteTypesByAutoSettlementEnabled(partnerId= partnerId!!, partnerName = partnerName, tenant = tenant, partnerDetailId = creditNoteReservationV2PayloadDTO.retailerId)
            val enabledNoteTypes = config.map { it.noteType }.toMutableList()
            val noteTypeAmountMap = config.associateBy({it.noteType}, {it.autoSettlementLimit})

            while (requiredCNAmount > BigDecimal.ZERO && hasNext && !exitWhileLoop)  {
                log.info("reserveCreditNoteV2ForExternalOrderId before query iteration: $page, $requiredCNAmount")
                val pagination = PageRequest.of(page, size)
                val pageResult = creditNoteRepo.getCustomerCreditNotesByStatusAndPartnerDetailId(
                    statuses = mutableListOf(
                        NoteStatus.PENDING,
                        NoteStatus.PARTIAL_REALIZED
                    ), partnerId = partnerId, tenant = tenant, eligibleNoteTypes = enabledNoteTypes, pageable = pagination
                )
                log.info("reserveCreditNoteV2ForExternalOrderId after query iteration: $page, $requiredCNAmount")
                for (it in pageResult.content) {
                    val cnCopy = creditNoteRepo.getLockedCN(it.id)
                    log.info("reserveCreditNoteV2ForExternalOrderId page iteration $page, cnId, ${cnCopy.id}")
                    if (company.isFullCnSettlementEnabled && it.remainingAmount > requiredCNAmount) {
                        exitWhileLoop = true
                        break
                    }

                    if(BigDecimal(noteTypeAmountMap[cnCopy.noteType]?:0.0) < cnCopy.remainingAmount){
                        continue
                    }
                    if ((requiredCNAmount > BigDecimal.ZERO) and (cnCopy.remainingAmount > BigDecimal.ZERO)) {
                        val eligibleCNAmount = if (requiredCNAmount < cnCopy.remainingAmount) {
                            requiredCNAmount
                        } else {
                            cnCopy.remainingAmount
                        }
                        requiredCNAmount -= eligibleCNAmount
                        cnCopy.remainingAmount -= eligibleCNAmount
                        cnCopy.amountUsed = eligibleCNAmount
                        cnCopy.status = if (cnCopy.remainingAmount > BigDecimal.ZERO) {
                            NoteStatus.PARTIAL_REALIZED
                        } else {
                            NoteStatus.REALIZED
                        }
                        creditNoteRepo.save(cnCopy)
                        eligibleCNs.add(cnCopy)
                    }
                }
                hasNext = pageResult.hasNext()
                page += 1
                log.info("reserveCreditNoteV2ForExternalOrderId end: whileloop iteration: $page, $requiredCNAmount, $hasNext")
            }



            var cnReservation = CreditNoteReservation(
                0L,
                now(),
                null,
                null,
                if ((creditNoteReservationV2PayloadDTO.amount - requiredCNAmount) > BigDecimal.ZERO) CreditNoteReservationStatus.RESERVED else CreditNoteReservationStatus.COMPLETED,
                creditNoteReservationV2PayloadDTO.amount,
                creditNoteReservationV2PayloadDTO.amount - requiredCNAmount,
                creditNoteReservationV2PayloadDTO.externalOrderId,
                partnerId!!,
                creditNoteReservationV2PayloadDTO.retailerId,
                tenant!!,
                orderType,
                CNReservationSource.RIO_SERVICE,
                creditNoteReservationV2PayloadDTO.distributorId
            )
            creditNoteReservationRepo.save(cnReservation)
            val cnReservationMappings: MutableList<CreditNoteReservationMapping> = mutableListOf()
            val cnReservationDTOs: MutableList<CreditNoteReservationDTO> = mutableListOf()
            eligibleCNs.forEach { cn ->
                cnReservationMappings.add(
                    CreditNoteReservationMapping(
                        0L,
                        now(),
                        cn,
                        cn.amountUsed!!,
                        cnReservation
                    )
                )
                cnReservationDTOs.add(
                    CreditNoteReservationDTO(
                        cn.id,
                        cn.creditNoteNumber!!,
                        cn.amountUsed!!,
                        cn.amount,
                        cn.noteType
                    )
                )
            }
            creditNoteReservationMappingRepo.saveAll(cnReservationMappings)
            return cnReservationDTOs
        }
    }
        @Transactional(rollbackFor = [Exception::class])
        fun createCreditNoteForFlatDiscountTask(
            flatTask: FlatDiscountTask, flatTaskDetail: FlatDiscountTaskDetail, user: String,
            ds: String? = null, company: Company?
        ) {
            log.info("Creating discount CN for partnerId: ${flatTaskDetail.partnerId} in taskId: ${flatTask.id}")
            val documentNumber = when(flatTask.type){
                NoteTypes.FINANCE_DISCOUNT_CN -> documentMasterService.getDocumentNumber(user, company?.companyCode!!, DocumentType.FINANCE_DISCOUNT_CN)
                else-> documentMasterService.getDocumentNumber(user, company?.companyCode!!, DocumentType.CX_CN_DISCOUNT)
            }
            val reasonCode = bulkCnReasonCodeMappingService.getBulkCNReasonCodeMapping(flatTaskDetail.reasonCode?:DEFAULT_REASON_CODE)
            var creditNote = CreditNote(
                id = 0,
                createdOn = now(),
                updatedOn = now(),
                createdBy = user,
                updatedBy = user,
                status = NoteStatus.PENDING,
                noteType = flatTask.type,
                amount = flatTaskDetail.discountAmount,
                supplierId = flatTaskDetail.partnerId,
                supplierName = flatTaskDetail.partnerName,
                remarks = "Created by flat discount task",
                settlementId = null,
                invoiceId = null,
                debitNotes = null,
                closureType = if (ds!=null) CreditNoteClosureType.SETTLEMENT else CreditNoteClosureType.MANUAL,
                partnerId = flatTaskDetail.partnerId,
                partnerDetailId = flatTaskDetail.partnerDetailId,
                tenant = if (ds!=null) flatTask.company.tenant else flatTask.tenant,
                creditNoteNumber = documentNumber,
                type = PartnerType.CUSTOMER,
                client = InvoiceType.RIO,
                receiptStatus = ReceiptStatus.RECEIVED,
                expectedDate = null,
                vendorCreditNoteDate = null,
                vendorCreditNoteAmount = null,
                vendorCreditNoteNumber = null,
                slabTaskDetail = null,
                flatTaskDetail = flatTaskDetail,
                reasonCode = reasonCode
            )
            log.info("flat CN created for partnerid: ${flatTaskDetail.partnerId} in taskId: ${flatTask.id}")
            creditNote = creditNoteRepo.save(creditNote)

            debitNoteService.createDebitNoteForSlabTask(creditNote, company.companyCode, false)
            var einvoiceKafka = EinvoiceKafkaDto(cn=creditNote, tenant=flatTask.tenant)
            einvoiceSinkPusher.genericEInvoiceCreationProducer(convertPayloadToV2(einvoiceKafka))
        }

        fun getFlatDiscountCreditNoteInfo(
            creditNoteNumber: String,
            tenant: String,
            ds: String? = null
        ): FlatDiscountCreditNotePrintDto {
            val creditNote = creditNoteRepo.getByNumber(creditNoteNumber)
            val flatTaskDetail = flatTaskDetailRepo.getFlatCreditNoteDetail(listOf(creditNote?.flatTaskDetail?.id)!!)
            if (flatTaskDetail.isEmpty()) throw RequestException("no flat discount found for cn!")

            val taxableAmount = flatTaskDetail[0].taxableAmount
            val sgst = flatTaskDetail[0].sgst
            val cgst = flatTaskDetail[0].cgst
            val igst = flatTaskDetail[0].igst
            val taxValue = flatTaskDetail[0].taxAmount

            val gst = sgst + cgst + igst
            val company = companyService.getCompanyTenantMappingObject(ds?:tenant)

            val srcSupplier = supplierProxy.supplier(null, company?.partnerDetailId)
            val supplier = supplierProxy.supplier(null, creditNote?.partnerDetailId)

            var emailSrcSupplier = supplierProxy.partners(srcSupplier[0]!!.partnerId)
            val emailCustomer = supplierProxy.partners(creditNote?.partnerId)

            val genericCustomerDto = supplierProxy.partnerGenericDto(mutableListOf(creditNote?.partnerDetailId!!))
            val genericSupplierDto = supplierProxy.partnerGenericDto(mutableListOf(company?.partnerDetailId!!))
            var fromDL: String = ""
            var toDL: String = ""
            if (genericCustomerDto != null) {
                genericCustomerDto.data?.get(0)?.compliance?.forEach {
                    if(it.licenseType.equals("Drug Licence") && it.enabled == true){
                        it.licenseNo?.let { license -> toDL = license }
                    }
                }
            }

            if (genericSupplierDto != null) {
                genericSupplierDto.data?.get(0)?.compliance?.forEach {
                    if(it.licenseType.equals("Drug Licence") && it.enabled == true){
                        fromDL = it.licenseNo?:""
                    }
                }
            }
        log.debug("fdcn email : $emailSrcSupplier")
        var supplerEmail: String = emailSrcSupplier?.email?:""
        var customerEmail: String = emailCustomer?.email?:""

            val comp = companyRepo.getCompanyByTenant(tenant!!)
                ?: throw RequestException("Company mapping not found for ${tenant}")
            var supplierSignature = if (creditNote?.tenant!!.contains("ds")) {
                "For Aarush Tirupathi Enterprises Pvt Ltd"
            } else {
                "For ${comp.name}"
            }
            val taxPercentage = getDefaultTaxPercentageByNoteType(creditNote.noteType)
            val hsnCode = getHsnCode(taxPercentage)
            return FlatDiscountCreditNotePrintDto(
                creditNoteNumber = creditNoteNumber,
                creditNoteDate = creditNote?.createdOn!!.plusHours(5)?.plusMinutes(30)!!.toLocalDate(),
                supplierName = srcSupplier[0]!!.partnerName!!,
                supplierAddress = srcSupplier[0]!!.partnerDetailList?.get(0)!!.address1 + " , " + srcSupplier[0]!!.partnerDetailList?.get(
                    0
                )!!.address2 + " , "+srcSupplier[0]!!.partnerDetailList?.get(0)?.city + " , "+ srcSupplier[0]!!.partnerDetailList?.get(0)?.state + " , " + srcSupplier[0]!!.partnerDetailList?.get(0)?.pincode,
                customerName = supplier[0]!!.partnerName!!,
                customerAddress = supplier[0]!!.partnerDetailList?.get(0)!!.address1 + " " + supplier[0]!!.partnerDetailList?.get(
                    0
                )!!.address2+ " , "+supplier[0]!!.partnerDetailList?.get(0)?.city + " , "+ supplier[0]!!.partnerDetailList?.get(0)?.state + " , " + supplier[0]!!.partnerDetailList?.get(0)?.pincode,
                creditNoteAmountInWords = "",
                cnDetail =
                FlatDiscountCreditNoteDetail(
                    period = SlabTaskUtils.getPeriodString(flatTaskDetail[0].startDate, flatTaskDetail[0].endDate),
                    discountTax = taxValue.toDouble(),
                    taxableValue = taxableAmount,
                    partnerId = flatTaskDetail[0].partnerId,
                    partnerDetailId = flatTaskDetail[0].partnerDetailId,
                    cgst = cgst.toBigDecimal(),
                    sgst = sgst.toBigDecimal(),
                    igst = igst.toBigDecimal(),
                    total = flatTaskDetail[0].discountAmount,
                    createdOn = creditNote.createdOn!!.toLocalDate(),
                    gstPercent = gst,
                    hsnCode = hsnCode
                ),
                customerGstin = supplier[0]!!.partnerDetailList?.get(0)!!.gst,
                emailCustomer = customerEmail,
                supplierEmail = supplerEmail,
                supplierGstin = srcSupplier[0]?.partnerDetailList?.get(0)?.gst,
                supplierSignature = supplierSignature,
                type = flatTaskDetail[0].type.name,
                remark = flatTaskDetail[0].remark,
                toDL = toDL,
                fromDL = fromDL,
                irn = creditNote.irn,
                qrCode = creditNote.qrCode,
                reasonTitle = creditNote.reasonCode?.bulkCnCategory,
                reasonDesc = creditNote.reasonCode?.bulkCnCategoryDescription
            )
    }

    fun createNsrCreditNoteV2(rioReturnDebitNoteDto: RioReturnEventDto, debitNote: DebitNote): CreditNote {
        log.debug("Creating NSR Credit note: $rioReturnDebitNoteDto")

        val cn = CreditNote(
            id = 0,
            createdOn = now(),
            createdBy = rioReturnDebitNoteDto.createdBy,
            updatedOn = now(),
            updatedBy = rioReturnDebitNoteDto.createdBy,
            status = NoteStatus.PENDING,
            noteType = NoteTypes.NSR,
            amount = rioReturnDebitNoteDto.totalCnValue!!,
            supplierId = debitNote.partnerId!!,
            supplierName = debitNote.supplierName!!,
            remarks = "NSR Credit Note",
            settlementId = null,
            invoiceId = 0,
            debitNotes = mutableListOf(debitNote),
            closureType = CreditNoteClosureType.SETTLEMENT,
            partnerId = debitNote.partnerId,
            partnerDetailId = debitNote.partnerDetailId,
            tenant = debitNote.tenant,
            creditNoteNumber = null,
            type = PartnerType.CUSTOMER,
            client = debitNote.client!!,
            receiptStatus = ReceiptStatus.NOT_RECEIVED,
            expectedDate = LocalDate.now().plusDays(60),
            vendorCreditNoteDate = null,
            vendorCreditNoteAmount = null,
            vendorCreditNoteNumber = null,
            conversion = null,
            remainingAmount = rioReturnDebitNoteDto.totalCnValue!!,
            debitItems = debitNote.debitItems
        )

        return save(debitNote.createdBy?:"", cn, debitNote.tenant)
    }

    fun getSourcePdi(
        company: CompanyTenantMapping?,
        creditNote: CreditNote
    ): Long? {
        var companyMigrationCheck = companyLogsRepo.getCompanyData(company?.companyId!!)
        var pdi = company.partnerDetailId
        if (companyMigrationCheck != null) {
            if (creditNote.createdOn!! <= companyMigrationCheck.migrationDate) {
                if(company.tenant.contains("th")) pdi = companyMigrationCheck.oldWhPdi
               else if(company.tenant.contains("ar")) pdi = companyMigrationCheck.oldArPdi
            }
        }
        return pdi
    }

    fun getMappedPdi(pdi:Long,cnDate:LocalDateTime): Long {
        var companyMigrationCheckWh = companyLogsRepo.getMappedWhPdiData(pdi)
        var companyMigrationCheckAr = companyLogsRepo.getMappedArPdiData(pdi)
        if (companyMigrationCheckWh != null && cnDate<=companyMigrationCheckWh.migrationDate) {
            return companyMigrationCheckWh.oldWhPdi
        }
        if (companyMigrationCheckAr != null && cnDate<=companyMigrationCheckAr.migrationDate) {
            return companyMigrationCheckAr.oldArPdi
        }
        return pdi
    }
    fun generateRioCnPdf(creditNoteNumber:MutableList<String>): Result {
        var cnData = creditNoteRepo.getCreditNotePdfDto(creditNoteNumber)

        cnData.forEach {
            eventPublisherUtil.createVaultCnPdfEvent(
                    CreditNotePdfEventDto(it.packageId,it.creditNoteNumber,it.totalCreditAmount,"",it.noteType?.name, it.tenant,it.retailerPdi,it.distributorPdi)
            )
        }

        return success

    }

    fun getCreditNoteByOrderId(orderId: String): List<CreditNoteNumberAndTypeMappingDTO>{
        return creditNoteRepo.getCreditNoteNumbersByOrderId(orderId)
    }

    fun getOpenCnAmount(pdi: Long, tenant: String, cnType: List<NoteTypes>?): BigDecimal {
        if (cnType.isNullOrEmpty()) {
            return creditNoteRepo.getCreditNoteOpenAmtForAllTypes(pdi, tenant)?: BigDecimal.ZERO
        }
        else {
            return creditNoteRepo.getCreditNoteOpenAmt(pdi, tenant, cnType)?: BigDecimal.ZERO
        }
    }
    private fun convertPayloadToV2(einvoiceKafka: EinvoiceKafkaDto): EInvoiceCreationKafkaDTOv2 {
        return EInvoiceCreationKafkaDTOv2(
            numberId = einvoiceKafka.cn.creditNoteNumber, einvoiceType = EInvoiceType.SALES_RET,
            tenant = einvoiceKafka.tenant, dbId = null)
    }

    fun getCreditNoteById(id: Long): CreditNote? {
        return creditNoteRepo.get(id)
    }

}
