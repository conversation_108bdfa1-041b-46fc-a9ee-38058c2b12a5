package com.pharmeasy.service

import com.pharmeasy.data.VaultDocument
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.DocumentSeries
import com.pharmeasy.model.VaultDocumentUpdateDto
import com.pharmeasy.repo.VaultDocumentRepo
import com.pharmeasy.type.DocumentType
import com.pharmeasy.type.PaymentMode
import com.pharmeasy.util.DateUtils
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Duration
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.Calendar
import java.time.ZoneId



@Service
class DocumentMasterService {

    companion object {
        private val log = LoggerFactory.getLogger(DocumentMasterService::class.java)
    }

    @Autowired
    private lateinit var vaultDocumentRepo: VaultDocumentRepo

    @Autowired
    private lateinit var redisUtilityService: RedisUtilityService

    @Transactional
    fun createDocument(user: String, document: VaultDocument): VaultDocument {
        log.debug("creating vault document: $document")

//        val exist = vaultDocumentRepo.findDuplicate(document.type, document.suffix)
//        if (exist != null) throw RequestException("Document of type: ${document.type} already exists")

        document.createdBy = user
        document.updatedBy = user
        document.createdOn = LocalDateTime.now()
        document.updatedOn = LocalDateTime.now()

        return vaultDocumentRepo.save(document)
    }

    @Transactional
    fun updateDocument(user: String, documentId: Long, vaultDocumentUpdateDto: VaultDocumentUpdateDto): VaultDocument {
        log.debug("updating document $documentId : $vaultDocumentUpdateDto")

        var doc: VaultDocument
        try {
            doc = vaultDocumentRepo.getOne(documentId)
        }
        catch (e: Exception) {
            throw RequestException("document : $documentId is not present in DB : ${e.message}")
        }

        doc.updatedOn = LocalDateTime.now()
        doc.updatedBy = user
        doc.year = vaultDocumentUpdateDto.year
        doc.fromNumber = vaultDocumentUpdateDto.fromNo
        doc.toNumber = vaultDocumentUpdateDto.toNo
        doc.currentNumber = vaultDocumentUpdateDto.currentNo

        return vaultDocumentRepo.save(doc)
    }

    @Transactional
    fun getDocumentNumber(user: String, companyCode: String, type: DocumentType): String {

        if(type==DocumentType.CX_RECEIPT){
            return generateDocumentNumber (companyCode, type)
        }

        val doc = vaultDocumentRepo.getVaultDocumentWithLock(type, companyCode.substring(1))
                ?: throw RequestException("document with $type not found for company: $companyCode")

        val curNum = if(doc.v2Enabled) doc.currentNumberV2 else doc.currentNumber
        doc.updatedBy = user
        doc.updatedOn = LocalDateTime.now()
        if(doc.v2Enabled)
            doc.currentNumberV2 = doc.currentNumberV2!!.substringBeforeLast("/") + "/" + doc.currentNumberV2!!.substringAfterLast("/").toLong().plus(1).toString()
        else
            doc.currentNumber = doc.currentNumber!!.substringBeforeLast("/") + "/" + doc.currentNumber!!.substringAfterLast("/").toLong().plus(1).toString()

        log.debug("document number generated - $curNum")
        vaultDocumentRepo.save(doc)
        return curNum!!
    }

//    @Scheduled(cron = "0 0 0 1 4 ?", zone = "IST")
    @Transactional
    fun updateDocuments() {
    val istDate: LocalDate = LocalDate.now(ZoneId.of("Asia/Kolkata"))
    log.debug("Document master update started on ${istDate}")
    if(istDate.monthValue == 4 && istDate.dayOfMonth == 1  ) {
        if (redisUtilityService.setIfAbsent("documentUpdateVaultKey", "1", Duration.ofDays(1))) {
            val docs = vaultDocumentRepo.findAll()
            for (doc in docs) {
                doc.year = Calendar.getInstance().get(Calendar.YEAR).toString().substring(2).toInt()
                doc.fromNumber = "${doc.series ?: 20}${doc.year}${doc.suffix}/********"
                doc.toNumber = "${doc.series ?: 20}${doc.year}${doc.suffix}/********"
                doc.currentNumber = "${doc.series ?: 20}${doc.year}${doc.suffix}/1"
                doc.fromNumberV2 = "${doc.seriesV2 ?: 20}${doc.year}${doc.suffix}/********"
                doc.toNumberV2 = "${doc.seriesV2 ?: 20}${doc.year}${doc.suffix}/********"
                doc.currentNumberV2 = "${doc.seriesV2 ?: 20}${doc.year}${doc.suffix}/1"
                doc.updatedOn = LocalDateTime.now()

                log.debug("Updated doc - $doc")
            }

            vaultDocumentRepo.saveAll(docs)
        }else{
            log.debug("duplicate update!")
        }
    }
    }

    fun getDocumentNumberForCashBank(type: PaymentMode, primaryKey: Long,companyCode: String): String {
        log.debug("getting the document number for $type")
        var companyCode = companyCode.substring(1)
        return "${getSeries(type)}${DateUtils.getFiscalYearCode()}$companyCode/${primaryKey}"
    }


    private fun getSeries(docType: PaymentMode): String {
        return when(docType) {
            PaymentMode.BANK -> DocumentSeries.BANK.seriesNumber
            PaymentMode.CASH -> DocumentSeries.CASH.seriesNumber
            else -> {
                throw Exception("Payment mode not supported $docType")
            }
        }
    }

    fun generateDocumentNumber(companyCode: String, type: DocumentType): String{
        val suffix =  companyCode.substring(1)
        return vaultDocumentRepo.getDocumentNumber(suffix, type.name)
    }
}
