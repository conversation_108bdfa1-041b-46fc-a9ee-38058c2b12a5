package com.pharmeasy.service

import com.pharmeasy.data.*
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.*
import com.pharmeasy.proxy.*
import com.pharmeasy.repo.*
import com.pharmeasy.stream.InvoiceSettlementUpdateHandler
import com.pharmeasy.type.*
import com.pharmeasy.util.DateUtils
import com.pharmeasy.util.EventPublisherUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.*
import kotlin.math.floor

@Service
class RetailerDebitNoteService(
    @Value("\${app.dnToRetailerCBHsn}") val dnToRetailerCBHsn: String,
    @Value("\${app.dnToRetailerCBTemplate}") val dnToRetailerCBTemplate: String,
    @Value("\${app.dnToRetailerCBTax}") val dnToRetailerCBTax: Int,
    @Value("\${app.retail-io.version}") val retailIoVersion: String,
    @Value("\${app.retail-io.source}") val retailIoSource: String,
    @Value("\${app.retail-io.key}") val retailIoKey: String,
    private val retailerDebitNoteSettlementMappingService: RetailerDebitNoteSettlementMappingService,
) {
    companion object {
        private val log = LoggerFactory.getLogger(RetailerDebitNoteService::class.java)
        private const val PR = "Purchase Return"
        private const val SOURCE = "vault-client"
    }

    @Autowired
    private lateinit var retailerDebitNoteRepo: RetailerDebitNoteRepo

    @Autowired
    private lateinit var supplierProxy: SupplierProxy

    @Autowired
    private lateinit var partnerMasterProxy: PartnerMasterProxy

    @Autowired
    private lateinit var companyService: CompanyService

    @Autowired
    private lateinit var documentMasterService: DocumentMasterService

    @Autowired
    private lateinit var companyRepo: CompanyRepo

    @Autowired
    private lateinit var partnerService: PartnerService

    @Autowired
    private lateinit var retailerDebitNoteItemsRepo: RetailerDebitNoteItemsRepo

    @Autowired
    private lateinit var paperServiceProxy: PaperServiceProxy

    @Autowired
    private lateinit var englishNumberToWords: EnglishNumberToWords

    @Autowired
    private lateinit var eInvoiceProxy: EInvoiceProxy

    @Autowired
    private lateinit var eventPublisherUtil: EventPublisherUtil

    @Autowired
    private lateinit var debitNoteService: DebitNoteService

    @Autowired
    private lateinit var advancePaymentService: AdvancePaymentService

    @Autowired
    private lateinit var debitNoteDetailRepo: DebitNoteDetailRepo

    @Autowired
    private lateinit var debitNoteRepo: DebitNoteRepo

    @Autowired
    private lateinit var retailIoProxy: RetailIoProxy

    @Autowired
    private lateinit var checkerService: CheckerService

    @Autowired
    private lateinit var retailerDebitNoteMappingService: RetailerDebitNoteMappingService

    @Autowired
    private lateinit var invoiceSettlementUpdateHandler: InvoiceSettlementUpdateHandler

    @Autowired
    private lateinit var creditNoteService: CreditNoteService



    @Transactional
    fun createRetailerDebitNotes(
        user: String, retailerDebitNoteDto: List<RetailerDebitNoteDto>, tenant: String
    ): List<RetailerDebitNoteDto> {
        retailerDebitNoteDto.forEach {
            createRetailerDebitNote(user, it, tenant)
        }
        return retailerDebitNoteDto
    }

    @Transactional
    fun createRetailerDebitNote(
        user: String, retailerDebitNoteDto: RetailerDebitNoteDto,
        tenant: String,
        bkChequeHandle: ChequeHandle? = null
    ): RetailerDebitNote {
        val tenantMapping = companyService.getCompanyTenantMappingObject(tenant, false)
            ?: throw RequestException("Source $tenant info not found! ")

        validateRetailerDebitNotes(retailerDebitNoteDto, tenant, bkChequeHandle)

        val partners = partnerMasterProxy.getPartnerGeneralData(
            listOf(tenantMapping.partnerDetailId!!,
            retailerDebitNoteDto.partnerDetailId))?.data

        if (partners?.isEmpty() == true) {
            throw RequestException("Source $tenant info not found! and Retailer details not found ")
        }

        val source = partners?.firstOrNull{ it.partnerDetailId == tenantMapping.partnerDetailId }
        val retailer = partners?.firstOrNull{ retailerDebitNoteDto.partnerDetailId == it.partnerDetailId }
        if ( source == null || retailer == null) {
            throw RequestException("Source info not found! or Retailer details not found ")
        }
        val supplierStateId = source.information?.state
        val retailerStateId = retailer.information?.state
        if (supplierStateId == null || retailerStateId == null) {
            throw RequestException("Source info not found! or Retailer details not found ")
        }

        val interState = supplierStateId != retailerStateId



        var taxAmount = if (retailerDebitNoteDto.taxRate == null) {
            BigDecimal(retailerDebitNoteDto.taxableValue ?: 0.0).multiply(
                (BigDecimal(retailerDebitNoteDto.taxPercent)).divide(BigDecimal(100), 2, RoundingMode.HALF_UP)
            )
        } else {
            BigDecimal(retailerDebitNoteDto.taxRate!!)
        }

        val company = companyRepo.getCompanyByTenant(tenant)

        val documentNumber = documentMasterService.generateDocumentNumber(company!!.companyCode, DocumentType.RETAILER_DN)
        val narration = when (retailerDebitNoteDto.type) {
            DnType.BOUNCE_CHARGE -> "BEING DN ISSUED AGAINST BOUNCE CHEQUE REF CHEQUE NO"
            DnType.CREDIT_NOTE -> "BEING DN ISSUED AGAINST CREDIT NOTE"
            DnType.ADHOC -> "BEING DN ISSUED AGAINST ADHOC"
        }

        if (retailerDebitNoteDto.type == DnType.CREDIT_NOTE) {
            val itemData =
                getCNItemLevelInfo(retailerDebitNoteDto.refId ?: throw RequestException("Credit Note Id is null"))
            retailerDebitNoteDto.taxPercent = (itemData.netCgst + itemData.netSgst + itemData.netIgst)
            retailerDebitNoteDto.taxableValue = itemData.netTaxableAmount.toDouble()
            taxAmount = itemData.netGstAmt
        }

        var retailerDebitNote = RetailerDebitNote(
            status = when (retailerDebitNoteDto.type) {
                DnType.ADHOC -> InvoiceStatus.PENDING_APPROVAL
                else -> InvoiceStatus.PENDING
            },
            updatedBy = user,
            createdBy = user,
            remarks = retailerDebitNoteDto.remarks,
            type = retailerDebitNoteDto.type,
            amount = retailerDebitNoteDto.amount,
            amountReceived = retailerDebitNoteDto.amountReceived ?: 0.0,
            companyId = company.id,
            tenant = tenant,
            partnerDetailId = retailerDebitNoteDto.partnerDetailId,
            taxableValue = retailerDebitNoteDto.taxableValue!!,
            taxAmount = taxAmount.toDouble(),
            interstate = interState,
            documentNumber = documentNumber,
            partnerId = retailer.partnerData!!.id,
            partnerName = retailer.partnerData.name!!,
            qrCode = null,
            irn = null,
            useCase = when (retailerDebitNoteDto.type) {
                DnType.BOUNCE_CHARGE -> UseCaseType.BOUNCE
                DnType.CREDIT_NOTE -> UseCaseType.CN_REVERSAL
                else -> retailerDebitNoteDto.useCase
            },
            isGstApplicable = retailerDebitNoteDto.isGstnApplicable,
            refId = retailerDebitNoteDto.migrationReferenceId,
            isMigrated = retailerDebitNoteDto.isMigrated,
            narration = narration,
            assignedTo = when (retailerDebitNoteDto.type) {
                DnType.ADHOC -> checkerService.findChecker(tenant)[0]?.userName
                    ?: throw RequestException("Checker not found")
                else -> null
            },
            assignedToId = when (retailerDebitNoteDto.type) {
                DnType.ADHOC -> checkerService.findChecker(tenant)[0]?.userId
                    ?: throw RequestException("Checker not found")

                else -> null
            },
            uuid = retailerDebitNoteDto.uuid
        )

        retailerDebitNote = retailerDebitNoteRepo.save(retailerDebitNote)
        retailerDebitNoteItemsRepo.save(
            RetailerDebitNoteItems(
                0,
                LocalDateTime.now(),
                LocalDateTime.now(),
                null,
                null,
                1,
                if (interState) 0.0 else retailerDebitNoteDto.taxPercent / 2,
                if (interState) 0.0 else retailerDebitNoteDto.taxPercent / 2,
                if (interState) retailerDebitNoteDto.taxPercent else 0.0,
                retailerDebitNoteDto.hsn ,
                retailerDebitNote.taxableValue,
                retailerDebitNote.taxAmount,
                retailerDebitNote.amount,
                retailerDebitNote
            )
        )
        if (retailerDebitNote.type != DnType.ADHOC) {
            val creditNote = if(retailerDebitNote.type == DnType.CREDIT_NOTE){
                creditNoteService.getById(retailerDebitNoteDto.refId?:throw RequestException("Credit note id is null"))
                    ?: throw RequestException("Credit note not found for id: ${retailerDebitNote.refId}")
            }else{
                null
            }
            retailerDebitNoteMappingService.save(retailerDebitNote, creditNote, bkChequeHandle)
        }

        if (retailerDebitNote.status == InvoiceStatus.PENDING) {
            addVendorLedgerEntryForDebitNote(user, retailerDebitNote)
            createRetailerDNEinvoice(retailerDebitNoteDto.partnerDetailId, retailerDebitNote.id!!)
            val distributorPdi = companyService.getCompanyTenantMappingObject(retailerDebitNote.tenant)!!.partnerDetailId
            sendRetailerDebitNoteCreationEventToRio(retailerDebitNote, distributorPdi!!)
        }
        return retailerDebitNote
    }

    private fun addVendorLedgerEntryForDebitNote(user: String, retailerDebitNote: RetailerDebitNote) {
        val vl = VendorLedgerDto(
            transactionDate = LocalDate.now(),
            vendorId = retailerDebitNote.partnerId,
            vendorName = retailerDebitNote.partnerName,
            ledgerEntryType = LedgerEntryType.DEBIT,
            documentType = when (retailerDebitNote.type) {
                DnType.BOUNCE_CHARGE -> DocumentType.RETAILER_BOUNCED_DN
                DnType.CREDIT_NOTE -> DocumentType.RETAILER_CN_DN
                else -> DocumentType.RETAILER_ADHOC_DN
            },
            documentNumber = retailerDebitNote.documentNumber,
            referenceNumber = retailerDebitNote.documentNumber,
            externalReferenceNumber = "",
            particulars = "DebitNote against Retailer",
            debitAmount = retailerDebitNote.amount.toBigDecimal(),
            creditAmount = BigDecimal.ZERO,
            tenant = retailerDebitNote.tenant,
            partnerDetailId = retailerDebitNote.partnerDetailId,
            partnerId = retailerDebitNote.partnerId,
            type = PartnerType.CUSTOMER,
            client = InvoiceType.RIO
        )

        partnerService.addVendorLedgerEntry(user, vl)

    }

    fun getAllRetailerDebitNotesData(
        partnerDetailIds: List<Long>?,
        partnerDetailId: Long?,
        from: LocalDate,
        to: LocalDate,
        dnType: DnType?,
        tenant: String,
        page: Int?,
        size: Int?,
        status: InvoiceStatus?,
        ds: String?,
        distributorId: Long?
    ): PaginationDto {
        log.debug("Inside getRetailerDebitNotesData {}: {}: {}: {}", partnerDetailIds, tenant, dnType, partnerDetailId)
        val companyMapping =
            if (distributorId != null) companyService.getTenantByPDI(distributorId) else companyService.getCompanyTenantMappingObject(
                ds ?: tenant
            )
        val tenants = companyService.findTenants(companyMapping!!.tenant)
        if (tenants.isEmpty()) throw RequestException("Your tenant: $tenant is not mapped to any Company")

        val pagination = PageRequest.of(page ?: 0, size ?: 10)

        val res: Page<VendorDebitNoteDto>
        val partnerDetailIdsQueryList: List<Long>? = when {
            partnerDetailId != null -> listOf(partnerDetailId)
            partnerDetailIds.isNullOrEmpty() -> null
            else -> partnerDetailIds
        }

        res = retailerDebitNoteRepo.getRetailerDebitNotesGroup(
            from.atStartOfDay(),
            to.atTime(23, 59, 59),
            dnType,
            partnerDetailIdsQueryList,
            status,
            tenants,
            pagination
        )


        return PaginationDto(res.totalElements, res.totalPages, res.hasPrevious(), res.hasNext(), res)
    }

    fun getRetailerDebitNotesByPdi(
        page: Int?,
        size: Int?,
        partnerDetailId: Long,
        documentNumber: String?,
        from: LocalDate,
        to: LocalDate,
        type: DnType?,
        tenant: String,
        ds: String?,
        status: InvoiceStatus?,
        distributorId: Long?
    ): PaginationDto {
        log.info("Inside getRetailerDebitNotesByPdi $partnerDetailId : $documentNumber : $type")

        val sortBy = Sort.by(Sort.Direction.DESC, "createdOn")
        val pagination = PageRequest.of(page ?: 0, size ?: 10, sortBy)
        val companyMapping =
            if (distributorId != null) companyService.getTenantByPDI(distributorId) else companyService.getCompanyTenantMappingObject(
                ds ?: tenant
            )
        val tenants = companyService.findTenants(companyMapping!!.tenant)
        if (tenants.isEmpty()) throw RequestException("Your tenant: $tenant is not mapped to any Company")
        val res: Page<RetailerDebitNote>
        val fromDate = from.atStartOfDay()
        val toDate = to.atTime(23, 59, 59)
        res = retailerDebitNoteRepo.getRetailerDebitNotes(
            fromDate,
            toDate,
            partnerDetailId,
            documentNumber,
            type,
            tenants,
            status,
            pagination
        )
        return PaginationDto(res.totalElements, res.totalPages, res.hasPrevious(), res.hasNext(), res.content)
    }

    fun generateRetailerDebitUrl(debitNoteId: Long): FileUrl {
        try {
            val retailerDebitNote = retailerDebitNoteRepo.findById(debitNoteId).get()
            val companyTenantObj =
                companyService.getCompanyTenantMappingObject(retailerDebitNote.tenant, false) ?: throw RequestException(
                    "Company info not found"
                )
            val theaData =
                supplierProxy.getPartnerDetails(null, null, null, null, companyTenantObj.partnerDetailId!!.toInt())
                    ?: throw RequestException("tenant partner details not found!")
            val retailerData =
                supplierProxy.getPartnerDetails(null, null, null, null, retailerDebitNote.partnerDetailId.toInt())
                    ?: throw RequestException("retailer partner details not found!")
            val headerMap: MutableMap<String, String> = mutableMapOf()
            headerMap["companyName"] = theaData[0].name ?: ""
            headerMap["companyAddress"] = "${theaData[0].address1 ?: ""} ${theaData[0].address2 ?: ""}"
            headerMap["companyEmail"] = theaData[0].email ?: ""
            headerMap["companyPhone"] = ""
            headerMap["companyGST"] = theaData[0].gst ?: ""
            headerMap["companyDlNo"] = theaData[0].drugLicenses?.map { it.licenseNo }?.joinToString(",") ?: ""
            headerMap["retailerName"] = retailerData[0].name ?: ""
            headerMap["retailerAddress"] = "${retailerData[0].address1 ?: ""} ${retailerData[0].address2 ?: ""}"
            headerMap["retailerGST"] = retailerData[0].gst ?: ""
            headerMap["retailerEmail"] = retailerData[0].email ?: ""
            headerMap["retailerDlNo"] = retailerData[0].drugLicenses?.map { it.licenseNo }?.joinToString(",") ?: ""
            headerMap["retailerStateCode"] = retailerData[0].stateGstCode.toString()
            headerMap["debitNoteNumber"] = retailerDebitNote.documentNumber
            headerMap["debitNoteDate"] = retailerDebitNote.createdOn.toLocalDate().toString()
            headerMap["debitNoteAmount"] = retailerDebitNote.amount.toString()
            headerMap["qrCode"] = "qr{${retailerDebitNote.qrCode ?: "-"}}"
            headerMap["narration"] = retailerDebitNote.narration + " ${retailerDebitNote.remarks}"

            val amount = retailerDebitNote.amount
            val num: Float = amount.toFloat()
            val rupee = floor(num.toDouble()).toInt()
            val paisa = (("0." + amount.toString().split(".")[1]).toFloat() * 100).toInt()

            val amountWords = (englishNumberToWords.convert(rupee.toDouble())
                ?.uppercase(Locale.getDefault()) + " RUPEES AND " + englishNumberToWords.convert(paisa.toDouble())
                ?.uppercase(Locale.getDefault()) + " PAISA ONLY")
            headerMap["amountWords"] = amountWords

            val data = retailerDebitNoteItemsRepo.getByRetailerDebitNoteId(debitNoteId)

            val dataMap: MutableList<MutableMap<String, Any?>> = mutableListOf()
            data.forEach {
                val items: MutableMap<String, Any?> = mutableMapOf()
                items["particular"] =
                    if (retailerDebitNote.type != DnType.BOUNCE_CHARGE) retailerDebitNote.remarks else ""
                items["hsn"] = it.hsn
                items["amt"] = it.taxableAmount
                items["gst"] = (it.cgst + it.sgst + it.igst).toString()
                items["cgst"] = it.cgst
                items["sgst"] = it.sgst
                items["igst"] = it.igst
                items["sgstAmt"] = if (it.sgst > 0) it.netGstAmt / 2 else 0.0
                items["cgstAmt"] = if (it.sgst > 0) it.netGstAmt / 2 else 0.0
                items["igstAmt"] = if (it.igst > 0) it.netGstAmt else 0.0
                dataMap.add(items)
            }
            val templateName = dnToRetailerCBTemplate
            val pdfData: MutableMap<String, Any?> = mutableMapOf()
            pdfData["header"] = headerMap
            pdfData["data"] = dataMap
            val url = paperServiceProxy.generateInvoicePdfByTemplateName(templateName, pdfData)

            return url
        } catch (e: Exception) {
            log.error("Error in paper service for pdf generation", e)
            throw RequestException("Error in paper service for pdf generation, ${e.message}")
        }
    }

    @Transactional
    fun createRetailerDebitNoteEInvoice(debitNoteId: Long) {
        log.info("creating einvoice for $debitNoteId")
        val debitNote = retailerDebitNoteRepo.getOne(debitNoteId)
        val debitNoteItem = retailerDebitNoteItemsRepo.getByRetailerDebitNoteId(debitNoteId)
        val tenant = debitNote.tenant

        val einvoiceList = mutableListOf<EinvoiceItem?>()

        var totalQuantity = 0
        var totalItemVal: Double
        var cgstTotal = 0.00
        var sgstTotal = 0.00
        var igstTotal = 0.00
        var totalAssessable = 0.00
        var totalDnAmt = 0.00
        var taxPercentage = 0.00

        val tenantMapping = companyService.getCompanyTenantMappingObject(debitNote.tenant, false)
            ?: throw RequestException("Source ${debitNote.tenant} info not found! ")
        val theaId = tenantMapping.theaId
        val sourceSupplierList = supplierProxy.supplier(null, tenantMapping.partnerDetailId)
        val sourceSupplier = if (sourceSupplierList.isNotEmpty()) sourceSupplierList[0] else throw RequestException(
            "Could not get Source Supplier List for partnerId ${tenantMapping.partnerDetailId} for tenant " + "${debitNote.tenant} ,while sale return credit note PDF creation."
        )

        val supplierList = supplierProxy.supplier(null, debitNote.partnerDetailId)
        val retailerInfo = if (supplierList.isNotEmpty()) supplierList[0] else throw RequestException(
            "Could not get Supplier List for partnerId ${debitNote.partnerDetailId} for tenant " + "${debitNote.tenant} ,while sale return credit note PDF creation."
        )


        if (retailerInfo?.partnerDetailList?.get(0)!!.gst == null || sourceSupplier!!.partnerDetailList?.get(0)!!.gst == null) return

        val interState = debitNote.interstate
        debitNoteItem.forEach {
            var igst = if (interState == true) {
                it.igst
            } else {
                0.00
            }
            var cgst = if (interState == false) {
                it.cgst
            } else {
                0.00
            }
            var sgst = if (interState == false) {
                it.sgst
            } else {
                0.00
            }


            val totalAmount = it.taxableAmount
            val assessableAmount = it.taxableAmount

            totalAssessable += assessableAmount ?: 0.00
            totalItemVal = it.totalAmount ?: 0.00
            totalQuantity += it.quantity ?: 1
            totalDnAmt += totalItemVal
            val unitPrice = totalItemVal.div(totalQuantity)
            when (interState) {
                true -> {
                    igstTotal += igst
                    taxPercentage = igst
                }

                false -> {
                    cgstTotal += cgst
                    sgstTotal += sgst
                    taxPercentage = (cgst) + sgst
                }
                null -> throw RequestException("Interstate value is null")
            }
            cgst = ((cgst * assessableAmount!!) / 100)
            sgst = ((sgst * assessableAmount) / 100)
            igst = ((igst * assessableAmount) / 100)
            einvoiceList.add(
                EinvoiceItem(
                    BigDecimal(assessableAmount),
                    BigDecimal(taxPercentage),
                    it.hsn,
                    it.quantity,
                    0,
                    BigDecimal(totalAmount!!),
                    BigDecimal(totalItemVal),
                    BigDecimal(unitPrice),
                    BigDecimal.ZERO,
                    BigDecimal(cgst),
                    BigDecimal(sgst),
                    BigDecimal(igst),
                    debitNote.type.name
                )
            )

        }
        val debitnoteDocDate = DateUtils.getISTDateTime(debitNote.createdOn)
        cgstTotal = ((cgstTotal * totalAssessable) / 100)
        sgstTotal = ((sgstTotal * totalAssessable) / 100)
        igstTotal = ((igstTotal * totalAssessable) / 100)
        val eInvoiceObj = Einvoice(
            invoiceId = debitNote.documentNumber,
            invoiceType = EInvoiceType.DBN,
            invoiceCategory = "B2B",
            buyerDetail = BuyerDetail(
                "${retailerInfo.partnerDetailList?.get(0)!!.address1}  ${retailerInfo.partnerDetailList?.get(0)!!.address2}",
                retailerInfo.partnerDetailList?.get(0)!!.gst,
                retailerInfo.partnerName,
                retailerInfo.partnerDetailList?.get(0)!!.city,
                retailerInfo.partnerDetailList?.get(0)!!.pincode,
                retailerInfo.partnerDetailList?.get(0)!!.stateId,
                retailerInfo.partnerDetailList?.get(0)!!.state

            ),
            itemList = einvoiceList,
            sellerDetail = SellerDetail(
                "${sourceSupplier.partnerDetailList?.get(0)!!.address1}  ${sourceSupplier.partnerDetailList?.get(0)!!.address2}",
                sourceSupplier.partnerDetailList?.get(0)!!.gst,
                sourceSupplier.partnerName,
                sourceSupplier.partnerDetailList?.get(0)!!.city,
                sourceSupplier.partnerDetailList?.get(0)!!.pincode,
                sourceSupplier.partnerDetailList?.get(0)!!.state
            ),
            sellerId = theaId,
            sellerType = if (tenant.substring(0, 2) == "th") "THEA"
            else if (tenant.substring(0, 2) == "ar") "ARSENAL"
            else "DARK_STORE",
            valueDetail = ValueDetail(
                BigDecimal(totalAssessable),
                BigDecimal(totalDnAmt),
                BigDecimal(cgstTotal),
                BigDecimal(sgstTotal),
                BigDecimal(igstTotal)
            ),
            invoiceDocCreatedOn = DateTimeFormatter.ofPattern("dd/MM/yyyy").format(debitnoteDocDate)
        )

        var einvoice: EIClientResponseDto? = null
        try {
            val sellerGstin = eInvoiceObj.sellerDetail
            val buyerGstin = eInvoiceObj.buyerDetail
            if ((sellerGstin?.gstin).equals(buyerGstin?.gstin)) {
                log.info("buyer and seller gstin same for debitnote ${debitNote.documentNumber}, skipping einvoice generation")
            } else if (!(sellerGstin?.gstin).equals(buyerGstin?.gstin)) {
                einvoice = eInvoiceProxy.createEinvoice(eInvoiceObj, "VAULT")
            }
        } catch (e: Exception) {
            log.error("error while fetching einvoice $e", e)
        }
        debitNote.irn = einvoice?.irn
        debitNote.qrCode = einvoice?.qrCode
        retailerDebitNoteRepo.save(debitNote)
    }

    @Transactional
    fun processCheckerRequest(id: Long, user: String, action: Status, userId: String) {
        val retailerDebitNote = retailerDebitNoteRepo.getOne(id)
        if(retailerDebitNote.assignedToId != userId){
            throw RequestException("You are not authorized to approve/reject this request")
        }
        if(retailerDebitNote.status == InvoiceStatus.REJECTED){
            throw RequestException("This request is already rejected")
        }
        if ((action == Status.APPROVED || action == Status.REJECTED) && retailerDebitNote.status == InvoiceStatus.PENDING) {
            throw RequestException("Debit not is already approved")
        } else if (action == Status.REJECTED && retailerDebitNote.status == InvoiceStatus.REJECTED) {
            throw RequestException("Debit not is already rejected")
        }
        if (retailerDebitNote.type != DnType.ADHOC) {
            throw RequestException("Only Adhoc Request are allowed for maker checker flow")
        }
        retailerDebitNote.status = when (action) {
            Status.APPROVED -> InvoiceStatus.PENDING
            else -> InvoiceStatus.REJECTED
        }
        retailerDebitNote.updatedOn = LocalDateTime.now()
        retailerDebitNote.updatedBy = user
        retailerDebitNoteRepo.save(retailerDebitNote)
        if (action == Status.APPROVED) {
            val distributorPdi =
                companyService.getCompanyTenantMappingObject(retailerDebitNote.tenant)!!.partnerDetailId
            createRetailerDNEinvoice(retailerDebitNote.partnerDetailId, id)
            addVendorLedgerEntryForDebitNote(user, retailerDebitNote)
            sendRetailerDebitNoteCreationEventToRio(retailerDebitNote, distributorPdi!!)
        }
    }

    private fun sendRetailerDebitNoteCreationEventToRio(retailerDebitNote: RetailerDebitNote, distributorPdi: Long) {
        eventPublisherUtil.createRetailerDebitNoteProducer(
            RetailerDNCreationEventDTO(
                "SB",
                retailerDebitNote.documentNumber,
                retailerDebitNote.id!!,
                retailerDebitNote.createdOn.toString(),
                retailerDebitNote.amount,
                retailerDebitNote.amount - retailerDebitNote.amountReceived,
                retailerDebitNote.type,
                distributorPdi,
                retailerDebitNote.remarks ?: "",
                retailerDebitNote.partnerDetailId
            )
        )
    }

    fun getAdhocDnRequests(
        tenant: String,
        partnerDetailId: Long?,
        fromDate: LocalDate?,
        toDate: LocalDate?,
        useCaseType: UseCaseType?,
        status: List<InvoiceStatus>,
        page: Int?,
        size: Int?,
        ds: String?,
        distributorId: Long?
    ): PaginationDto {
        val pagination = PageRequest.of(page ?: 0, size ?: 10)
        val companyMapping =
            if (distributorId != null) companyService.getTenantByPDI(distributorId) else companyService.getCompanyTenantMappingObject(
                ds ?: tenant
            )
        val tenants = companyService.findTenants(companyMapping!!.tenant)
        val from = fromDate?.atStartOfDay()
        val to = toDate?.atTime(23, 59, 59)
        val res = retailerDebitNoteRepo.getAdhocRetailerDebitNote(
            DnType.ADHOC,
            tenants,
            partnerDetailId,
            from,
            to,
            useCaseType,
            status,
            pagination
        )
        return PaginationDto(res.totalElements, res.totalPages, res.hasPrevious(), res.hasNext(), res.content)
    }

    fun getRetailerDebitNoteBySettlementId(settlementId: Long): List<RetailerDebitNote> {
        return retailerDebitNoteRepo.getRetailerDnBySettlementId(settlementId)
    }

    private fun getCNItemLevelInfo(creditNoteId: Long): DebitNoteDetailsSummarisedDTO {
        val debitNotes = debitNoteRepo.getByCreditNoteId(creditNoteId)
        var cgst = 0.00
        var igst = 0.00
        var sgst = 0.00
        var taxableAmt: BigDecimal = BigDecimal.ZERO
        var totalTax: BigDecimal = BigDecimal.ZERO
        debitNotes?.forEach {
            if (it?.apiVersion == APIVersionType.V1) {
                val data = debitNoteDetailRepo.getDebitNoteDetailsByCreditNoteId(creditNoteId)
                cgst += data?.netCgst ?: 0.00
                sgst += data?.netSgst ?: 0.00
                igst += data?.netIgst ?: 0.00
                taxableAmt += data?.netTaxableAmount ?: BigDecimal.ZERO
                totalTax += data?.netGstAmt ?: BigDecimal.ZERO
            } else {
                val detailsObj = retailIoProxy.getRioDebitItemData(
                    retailIoVersion,
                    retailIoSource,
                    retailIoKey,
                    it?.logisticsPackageId ?: "",
                    true
                )
                detailsObj.items.forEach { i ->
                    cgst += (i.cgstPercent ?: 0.0)
                    sgst += (i.sgstPercent ?: 0.0)
                    igst += (i.igstPercent ?: 0.0)
                    taxableAmt += BigDecimal(i.cnTaxableAmount ?: 0.00)
                    totalTax += BigDecimal(i.cnNetGstAmount ?: 0.00)
                }
            }
        }
        return DebitNoteDetailsSummarisedDTO(cgst, sgst, igst, taxableAmt, totalTax)
    }

    @Transactional
    fun createEWayBill(eWayBillGenericDto: EWayBillGenericDto): EwayBillV2? {
        val nonIRNEwayBillGenerationRequestDto = getNonIrnEwayBillDtoForDebitNote(eWayBillGenericDto.id)
        val ewayBill = eInvoiceProxy.generateNonIrnEwayBill(nonIRNEwayBillGenerationRequestDto, EInvoiceType.DBN)
            ?: throw RequestException("No EWayBill was created for this ${nonIRNEwayBillGenerationRequestDto.debitNoteNumber}")

        return ewayBill
    }

    private fun getNonIrnEwayBillDtoForDebitNote(id: Long): NonIRNEwayBillGenerationDto {
        val lineItems = mutableListOf<EwayItemDto>()
        val debitNote = debitNoteRepo.get(id)


        val debitNoteItems: List<Any> = when (debitNote?.apiVersion) {
            APIVersionType.V1 -> debitNoteDetailRepo.getDebitNoteDetailsByDebitNoteNumber(debitNote.debitNoteNumber)
            APIVersionType.V2 -> retailIoProxy.getRioDebitItemData(
                debitNote.apiVersion.toString(),
                SOURCE,
                retailIoKey,
                debitNote.logisticsPackageId!!,
                true
            ).items

            else -> throw RequestException("API VERSION ${debitNote?.apiVersion} not supported")
        }

        if (debitNoteItems.isEmpty()) {
            throw RequestException("No debit note items for this ${debitNote.debitNoteNumber}")
        }

        var totalTaxableAmount = 0.0
        var totalCgstAmt = 0.0
        var totalIgstAmt = 0.0
        var totalSgstAmt = 0.0
        debitNoteItems.forEach { item ->
            val lineItem = when (item) {
                is DebitNoteDetails -> {
                    val qtyPrice = item.taxableAmount?.div(item.quantity.toBigDecimal())
                    EwayItemDto(
                        ProdName = item.name,
                        ProdDesc = "",
                        HsnCd = item.hsn,
                        Qty = item.quantity.toString(),
                        Unit = "UNT",
                        AssAmt = item.taxableAmount?.toDouble(),
                        IgstRt = item.igst?.toDouble(),
                        CgstRt = item.cgst?.toDouble(),
                        SgstRt = item.sgst?.toDouble(),
                        IgstAmt = ((qtyPrice!! * item.igst!!) / BigDecimal(100) * BigDecimal(item.quantity)).toDouble(),
                        CgstAmt = ((qtyPrice * item.cgst!!) / BigDecimal(100) * BigDecimal(item.quantity)).toDouble(),
                        SgstAmt = (((qtyPrice * item.sgst!!) / BigDecimal(100)) * BigDecimal(item.quantity)).toDouble(),
                        CesRt = 0.0,
                        CesAmt = 0.0,
                        OthChrg = 0.0
                    ).also {
                        totalTaxableAmount += item.taxableAmount!!.toDouble()
                        totalCgstAmt += ((qtyPrice * item.cgst!!) / BigDecimal(100) * BigDecimal(item.quantity)).toDouble()
                        totalSgstAmt += ((qtyPrice * item.sgst!!) / BigDecimal(100) * BigDecimal(item.quantity)).toDouble()
                        totalIgstAmt += ((qtyPrice * item.igst!!) / BigDecimal(100) * BigDecimal(item.quantity)).toDouble()
                    }
                }

                is RioReturnItemsDataDto -> {
                    val qtyPrice = item.taxableAmount?.div(item.returnQuantity!!.toDouble())
                    EwayItemDto(
                        ProdName = item.itemName,
                        ProdDesc = "",
                        HsnCd = item.hsnCode,
                        Qty = item.returnQuantity.toString(),
                        Unit = "UNT",
                        AssAmt = item.taxableAmount,
                        IgstRt = item.igstPercent,
                        CgstRt = item.cgstPercent,
                        SgstRt = item.sgstPercent,
                        IgstAmt = ((qtyPrice!! * item.igstPercent!! / 100) * item.returnQuantity!!),
                        CgstAmt = ((qtyPrice * item.cgstPercent!! / 100) * item.returnQuantity!!),
                        SgstAmt = ((qtyPrice * item.sgstPercent!! / 100) * item.returnQuantity!!),
                        CesRt = 0.0,
                        CesAmt = 0.0,
                        OthChrg = 0.0
                    ).also {
                        totalTaxableAmount += item.taxableAmount!!
                        totalCgstAmt += ((qtyPrice * item.cgstPercent!!).div(100) * item.returnQuantity!!)
                        totalSgstAmt += ((qtyPrice * item.sgstPercent!!).div(100) * item.returnQuantity!!)
                        totalIgstAmt += ((qtyPrice * item.igstPercent!!).div(100) * item.returnQuantity!!)
                    }
                }

                else -> throw RequestException("Unsupported item type: ${item::class.java}")
            }
            lineItems.add(lineItem)
        }

        val formatDate = DateTimeFormatter.ofPattern("dd/MM/yyyy")
        val currentDate = formatDate.format(LocalDate.now())

        // seller details
        val tenantMapping = companyService.getCompanyTenantMappingObject(debitNote.tenant, false)
            ?: throw RequestException("Source ${debitNote.tenant} info not found! ")
        val sourceSupplierList = supplierProxy.supplier(null, tenantMapping.partnerDetailId)
        val sourceSupplier = if (sourceSupplierList.isNotEmpty()) sourceSupplierList[0] else throw RequestException(
            "Could not get Source Supplier List for partnerId ${tenantMapping.partnerDetailId} for tenant " + "${debitNote.tenant} ,while sale return credit note PDF creation."
        )

        val sellerType = if (debitNote.tenant.substring(0, 2) == "th") SellerType.THEA
        else if (debitNote.tenant.substring(0, 2) == "ar") SellerType.ARSENAL
        else SellerType.DARK_STORE

        val sellerDetails = EntityDetailsDto(
            sourceSupplier!!.partnerDetailList!![0]!!.gst!!,
            "",
            "",
            "${sourceSupplier.partnerDetailList?.get(0)!!.address1}",
            "${sourceSupplier.partnerDetailList?.get(0)!!.address2}",
            sourceSupplier.partnerDetailList!![0]!!.city!!,
            sourceSupplier.partnerDetailList!![0]!!.pincode.toInt(),
            sourceSupplier.partnerDetailList!![0]!!.gst!!.substring(0, 2)
        )


        //buyer details
        val supplierList = supplierProxy.supplier(null, debitNote.partnerDetailId)
        val supplier = if (supplierList.isNotEmpty()) supplierList[0] else throw RequestException(
            "Could not get Supplier List for partnerDetailId ${debitNote.partnerDetailId} for tenant " + "${debitNote.tenant} "
        )
        if (supplier!!.partnerDetailList?.get(0)!!.gst == null || sourceSupplier.partnerDetailList?.get(0)!!.gst == null) {
            throw Exception("GST not found for supplier ${supplier.partnerName} and source supplier ${sourceSupplier.partnerName}")
        }

        val buyerDetails = EntityDetailsDto(
            supplier.partnerDetailList!![0]!!.gst!!,
            "",
            "",
            "${supplier.partnerDetailList?.get(0)!!.address1}",
            "${supplier.partnerDetailList?.get(0)!!.address2}",
            supplier.partnerDetailList!![0]!!.city!!,
            supplier.partnerDetailList!![0]!!.pincode.toInt(),
            supplier.partnerDetailList!![0]!!.gst!!.substring(0, 2)
        )

        //debitnote number is invoice no for eway bill
        return NonIRNEwayBillGenerationDto(
            DocumentNumber = debitNote.debitNoteNumber,
            DocumentType = EInvoiceType.OTH.name,
            DocumentDate = currentDate,
            SupplyType = EwayBillSupplyType.OUTWARD.name,
            SubSupplyType = EInvoiceType.OTH.name,
            SubSupplyTypeDesc = PR,
            TransactionType = "1",
            BuyerDtls = buyerDetails,
            SellerDtls = sellerDetails,
            ItemList = lineItems,
            TotalInvoiceAmount = debitNote.amountReceivable.toDouble(),
            TotalAssessableAmount = totalTaxableAmount,
            TotalCgstAmount = totalCgstAmt,
            TotalSgstAmount = totalSgstAmt,
            TotalIgstAmount = totalIgstAmt,
            SellerId = sourceSupplier.partnerDetailList!![0]!!.id!!.toInt(),
            SellerType = sellerType,
            debitNoteNumber = debitNote.debitNoteNumber
        )
    }

    fun getDebitNoteNumber(logisticsPackageId: String): List<String> {
        val debitNotes = debitNoteRepo.getDebitNoteNumberFromPackageId(logisticsPackageId)
        return debitNotes.map { it.debitNoteNumber }
    }

    fun getListOfDebitNo(logisticsPackageIdList: List<String>): Map<String, List<String>> {
        val debitNotes = debitNoteRepo.getDebitNoteNumberFromPackageIds(logisticsPackageIdList)

        // Group results by logisticsPackageId
        return debitNotes.groupBy({ it.logisticsPackageId }, { it.debitNoteNo })
    }

    fun getDebitNoteUrls(logisticsPackageId: String): UrlsDTO? {
        val debitNotes = debitNoteRepo.getPRDebitNoteNumberFromPackageId(logisticsPackageId, NoteTypes.PURCHASE_RETURN)

        val debitNoteNumbers = mutableListOf<String>()
        val debitNoteUrlsList = mutableListOf<String>()
        val urls = UrlsDTO()

        if (debitNotes.isEmpty()) {
            return urls
        }

        debitNotes.forEach { debitNote ->
            val url =
                debitNoteService.prepareS3URLForDebitNotesByDnNumber(debitNote.debitNoteNumber, debitNote.tenant).url
                    ?: throw RequestException("No debit note created for ${debitNote.debitNoteNumber}")

            debitNoteUrlsList.add(url)
            debitNoteNumbers.add(debitNote.debitNoteNumber)
        }

        urls.debitNoteUrls = debitNoteUrlsList
        urls.ewayBillUrls =
            eInvoiceProxy.getEwayBillUrls(EInvoiceType.DBN, debitNotes[0].tenant, "VAULT", debitNoteNumbers) ?: listOf()

        return urls
    }

    fun createRetailerDNEinvoice(partnerDetailId: Long, retailerDNId: Long) {
        val supplierList = supplierProxy.supplier(null, partnerDetailId)
        val supplier =
            if (supplierList.isNotEmpty()) supplierList[0] else throw RequestException("Could not get Supplier List for partnerDetailId $partnerDetailId")
        if (supplier?.partnerDetailList!![0]?.einvoice == true) {
            log.info("Generating einvoice for debitnote id $retailerDNId}")
            eventPublisherUtil.createGenericEInvoiceProducer(
                EinvoiceGenericDto(
                    retailerDNId,
                    EinvoiceCreationType.RETAILER_DEBIT_NOTE
                )
            )
        }
    }

    fun getRetailerDebitNoteByDebitNoteNumber(debitNoteNumber: String): RetailerDebitNote? {
        return retailerDebitNoteRepo.getRetailerDebitNoteByDebitNoteNumber(debitNoteNumber)
    }

    fun saveRetailerDns(retailerDebitNote: List<RetailerDebitNote>) {
        retailerDebitNoteRepo.saveAll(retailerDebitNote)
    }

    private fun validateRetailerDebitNotes(
        retailerDebitNoteDto: RetailerDebitNoteDto, tenant: String,
        bkChequeHandle: ChequeHandle?
    ) {
        val errors = retailerDebitNoteDto.validate()
        if (errors.isNotEmpty()) {
            throw RequestException("Validation errors : ${errors.joinToString(", ")}")
        }

        if (!listOf(DnType.ADHOC, DnType.BOUNCE_CHARGE, DnType.CREDIT_NOTE).contains(retailerDebitNoteDto.type)) {
            throw RequestException("Invalid/Unknown type: ${retailerDebitNoteDto.type}")
        }

        if (retailerDebitNoteDto.type == DnType.CREDIT_NOTE) {
            retailerDebitNoteMappingService.validateCreditNoteMapping(
                retailerDebitNoteDto.refId ?: throw RequestException("Credit Note Id is null"), retailerDebitNoteDto
            )

        } else if (retailerDebitNoteDto.type == DnType.BOUNCE_CHARGE) {
            if (bkChequeHandle == null) {
                throw RequestException("Cheque Handle Id is null for bounce charge")
            }
            retailerDebitNoteMappingService.validateChequeBounceMapping(bkChequeHandle)
        }

        val retailerDebitNote = retailerDebitNoteRepo.getRetailerDebitNoteByUuid(retailerDebitNoteDto.uuid)
        if (retailerDebitNote != null) {
            throw RequestException("Retailer Debit Note already $retailerDebitNoteDto.uuid exists")
        }
        if (retailerDebitNoteDto.type == DnType.CREDIT_NOTE || retailerDebitNoteDto.type == DnType.ADHOC) {
            if (!companyService.getEnabledRetailerDnByTenant(tenant)) {
                throw RequestException("Retailer debit note of type ${retailerDebitNoteDto.type} is not enabled.")
            }
        }
    }

    @Transactional
    fun closeLessThanOneRupeeDebitNote(createdOnBuffer: LocalDateTime){
        val retailerDebitNotes = retailerDebitNoteRepo.getLessThanRupeeOpenDebitNotes(createdOnBuffer)
        if(retailerDebitNotes.isNotEmpty()){
            retailerDebitNotes.forEach {
                it.amount = 0.0
                it.amountReceived = 0.0
                it.status = InvoiceStatus.PAID
                it.updatedOn = LocalDateTime.now()
                it.updatedBy = "SYSTEM"
                invoiceSettlementUpdateHandler.createSettlementConsumer(it.id!!, CreationType.DEBIT_NOTE)
            }
            saveRetailerDns(retailerDebitNotes)
        }
    }

    fun updateRetailerDebitNoteAmtForBounceCheque(retailerDN: List<RetailerDebitNote>?, dnAmtMap: Map<Long, RetailerDebitNoteSettlementMapping>){
        if(retailerDN.isNullOrEmpty()){
            throw RequestException("Retailer Debit Note not found")
        }
        retailerDN.forEach {
            if(dnAmtMap.containsKey(it.id)){
                val amountReceived = it.amountReceived
                val settledDn = dnAmtMap[it.id]
                val settledAmt = settledDn?.paidAmount?:0.0
                val diff = amountReceived - settledAmt
                if(it.amount == settledAmt || diff <= 0.0){
                    it.status = InvoiceStatus.PENDING
                    it.amountReceived = 0.0
                }else{
                    it.status = InvoiceStatus.PARTIAL_PAID
                    it.amountReceived = diff
                }
                invoiceSettlementUpdateHandler.createSettlementConsumer(it.id!!, CreationType.DEBIT_NOTE)
            }
        }
        saveRetailerDns(retailerDN)
    }

    fun updateRetailerDebitNotes(user: String, settlement: Settlement):MutableList<RetailerDebitNoteSettlementMapping>{
        val debitNoteList: MutableList<RetailerDebitNote> = mutableListOf()
        val debitNoteSettlementLists: MutableList<RetailerDebitNoteSettlementMapping> = mutableListOf()
        settlement.retailerDebitNotes.forEach{
            val rdn = retailerDebitNoteRepo.getRetailerDebitNoteByDebitNoteNumber(it.documentNumber)
            if(rdn == null){
                throw RequestException("Retailer debit note number ${it.documentNumber} not found.")
            }

            if(rdn.status == InvoiceStatus.PAID){
                throw RequestException("Trying to change the invoice in a terminal state - ${rdn.status}")
            }
            rdn.updatedOn = LocalDateTime.now()
            rdn.updatedBy = user

            if (rdn.amount < it.amountReceived) {
                throw RequestException("Received amount is greater than debit note amount")
            }
            val paidAmount = it.amountReceived - rdn.amountReceived
            if (rdn.amountReceived > it.amountReceived) {
                val remainingAmount = rdn.amount - rdn.amountReceived
                throw RequestException("Remaining amount against debit note ${rdn.documentNumber} is $remainingAmount. Cannot settle $paidAmount against this transaction.")
            }

            rdn.amountReceived = it.amountReceived
            val allowedDiff = 0.09
            if(rdn.amount - it.amountReceived > allowedDiff){
                rdn.status = InvoiceStatus.PARTIAL_PAID
            }else{
                rdn.status = InvoiceStatus.PAID
            }
            debitNoteList.add(rdn)
            debitNoteSettlementLists.add(RetailerDebitNoteSettlementMapping(rdn, settlement, paidAmount))
        }
        retailerDebitNoteSettlementMappingService.saveAll(debitNoteSettlementLists)
        saveRetailerDns(debitNoteList)
        debitNoteList.forEach {
            invoiceSettlementUpdateHandler.createSettlementConsumer(it.id!!, CreationType.DEBIT_NOTE)
        }
        return debitNoteSettlementLists
    }

    fun getDebitNoteById(id: Long): RetailerDebitNote? {
        return retailerDebitNoteRepo.findByIdOrNull(id)
    }

    fun updateDNSettlement(
        debitNote: RetailerDebitNote,
        settlement: Settlement,
        settlementMapping: DraftReceiptEntityMapping,
    ): Pair<RetailerDebitNote, RetailerDebitNoteSettlementMapping> {
        val dn = (settlement.retailerDebitNotes.find { it.id == debitNote.id }
            ?: throw RequestException("debit note not found for id ${debitNote.id}"))
        if (debitNote.status == InvoiceStatus.PAID) {
            throw RequestException("Trying to change the invoice in a terminal state - ${debitNote.status}")
        }
        debitNote.updatedOn = LocalDateTime.now()
        debitNote.updatedBy = settlement.createdBy!!
        val paidAmount = settlementMapping.entityTxAmount
        debitNote.amountReceived = dn.amountReceived

        if (dn.amount - dn.amountReceived >= 1.00) {
            debitNote.status = InvoiceStatus.PARTIAL_PAID
        } else {
            debitNote.status = InvoiceStatus.PAID
        }

        val savedDn = retailerDebitNoteRepo.save(debitNote)
        val savedMapping = retailerDebitNoteSettlementMappingService.save(
            RetailerDebitNoteSettlementMapping(
                retailerDebitNoteId = savedDn,
                settlementId = settlement,
                paidAmount = paidAmount
            )
        )
        return savedDn to savedMapping
    }
}
