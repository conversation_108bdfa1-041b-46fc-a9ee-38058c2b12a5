package com.pharmeasy.service

import com.pharmeasy.data.Receipt
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.CashBankLedgerDto
import com.pharmeasy.model.TradeCreditDto
import com.pharmeasy.model.VendorLedgerDto
import com.pharmeasy.model.ops.PartnerInfo
import com.pharmeasy.model.ops.PaymentBatchRequest
import com.pharmeasy.model.ops.SettlementGroupNumber
import com.pharmeasy.type.AdvancePaymentSource
import com.pharmeasy.type.DocumentType
import com.pharmeasy.type.InvoiceType
import com.pharmeasy.type.LedgerEntryType
import com.pharmeasy.type.PartnerType
import com.pharmeasy.type.PaymentMode
import com.pharmeasy.type.PaymentType
import com.pharmeasy.type.SettlementType
import com.pharmeasy.util.DateUtils
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDate

@Service
class ReceiptOrchestratorService(
    private val receiptService: ReceiptProcessingStrategyImpl,
    private val draftReceiptService: DraftReceiptProcessingStrategyImpl,
    private val receiptStrategy: ReceiptStrategy,
    private val tradeCreditPaymentService: TradeCreditPaymentService,
    private val partnerService: PartnerService,
    private val cashBankLedgerService: CashBankLedgerService,
    private val companyService: CompanyService,
    private val documentService: DocumentMasterService
) {
    fun processPayment(
        paymentBatch: PaymentBatchRequest,
        source: AdvancePaymentSource? = null
    ) {
        generateSettlementGroupNumber(paymentBatch)
        with(paymentBatch) {
            payments.forEach { payment ->
                val receiptStrategy = receiptStrategy.getStrategy(payment)
                when (receiptStrategy) {
                    ReceiptStrategy.DraftReceipt -> {
                        payment.paymentSource = source ?: AdvancePaymentSource.RIO_COLLECTIONS
                        draftReceiptService.createDraftReceipt(
                            payment,
                            paymentBatch.partnerInfo,
                            preApprove = false
                        )
                    }

                    ReceiptStrategy.PreApprovedReceipt -> {
                        payment.paymentSource = source ?: AdvancePaymentSource.RIO_PAY
                        val draft = draftReceiptService.createDraftReceipt(
                            payment,
                            partnerInfo,
                            preApprove = true
                        )
                        val receipt = receiptService.createReceipt(draft, paymentBatch.settlementGroupNumber)

                        // Handling trade credit payment
                        if (payment.paymentType == PaymentType.TRADE_CREDIT) {
                            val tradeCreditDto =
                                TradeCreditDto(
                                    tenant = partnerInfo.tenant,
                                    partnerId = partnerInfo.partnerId,
                                    partnerName = partnerInfo.partnerName,
                                    transactionId = payment.customerTransactionId,
                                    partnerDetailId = partnerInfo.partnerDetailId,
                                    creditAmount = 0.00,
                                    debitAmount = payment.transactionAmount,
                                    userId = payment.initiatedBy,
                                    dueDate = DateUtils.getDateTimeFromEpochStamp(payment.creditDetails!!.dueDate)!!,
                                    repaymentReferenceNumber = null
                                )
                            tradeCreditPaymentService.saveTradeCreditPayment(tradeCreditDto)
                        }

                        updateLedgerEntries(partnerInfo, receipt)
                    }
                }
            }
        }
    }

    private fun generateSettlementGroupNumber(paymentBatch: PaymentBatchRequest) {
        val company = companyService.getCompanyByTenant(paymentBatch.partnerInfo.tenant)
            ?: throw RequestException("Company not found for tenant ${paymentBatch.partnerInfo.tenant}")
        val type = DocumentType.CX_RECEIPT // Considering type as CX receipt . TODO: Fetch actualy type
        val settlementGroupNumber = documentService.generateDocumentNumber(company.companyCode, type)
        paymentBatch.settlementGroupNumber = SettlementGroupNumber(settlementGroupNumber)
    }

    private fun updateLedgerEntries(partnerInfo: PartnerInfo, receipt: Receipt) {

        // Considering type as Customer and client as RIO for now. TODO: Check the actual logic
        var type = PartnerType.CUSTOMER
        var client = InvoiceType.RIO

        if (receipt.paymentType != PaymentType.CHEQUE) {
            // Ledger entries
            val vl = VendorLedgerDto(
                transactionDate = LocalDate.now(),
                vendorId = partnerInfo.partnerId,
                vendorName = partnerInfo.partnerName,
                ledgerEntryType = if (type == PartnerType.CUSTOMER) LedgerEntryType.CREDIT else LedgerEntryType.DEBIT,
                documentType = if (type == PartnerType.CUSTOMER) DocumentType.CX_RECEIPT else DocumentType.VENDOR_PAYMENT,
                documentNumber = receipt.receiptNumber, // Mapping to the receipt itself instead of a specific invoice / DN
                referenceNumber = receipt.paymentTransactionId,
                externalReferenceNumber = receipt.receiptNumber,
                particulars = receipt.paymentType?.particular ?: "",
                debitAmount = if (type == PartnerType.CUSTOMER) BigDecimal.ZERO else receipt.amount!!.toBigDecimal(),
                creditAmount = if (type == PartnerType.CUSTOMER) receipt.amount!!.toBigDecimal() else BigDecimal.ZERO,
                tenant = receipt.tenant!!,
                partnerDetailId = receipt.partnerDetailId,
                partnerId = receipt.partnerId,
                type = type,
                client = client,
                remark = receipt.remarks
            )
            partnerService.checkAndAddLedgerEntry(receipt.updatedBy!!, vl)
        }

        if (receipt.paymentType in listOf(
                PaymentType.CASH,
                PaymentType.CHEQUE,
                PaymentType.NEFT,
                PaymentType.RIO_PAY,
                PaymentType.UPI
            )
        ) {
            val particulars =
                if (type == PartnerType.VENDOR) {
                    "Paid to ${partnerInfo.partnerName}"
                } else {
                    "Received from ${partnerInfo.partnerName}"
                }

            val cl = CashBankLedgerDto(
                transactionDate = LocalDate.now(),
                ledgerEntryType = if (type == PartnerType.VENDOR) LedgerEntryType.CREDIT else LedgerEntryType.DEBIT,
                documentType = if (type == PartnerType.VENDOR) SettlementType.PAYMENT else SettlementType.RECEIPT,
                documentNumber = receipt.receiptNumber,
                referenceNumber = receipt.receiptNumber,
                externalReferenceNumber = receipt.paymentTransactionId,
                particulars = particulars,
                debitAmount = if (type == PartnerType.VENDOR) BigDecimal.ZERO else BigDecimal(receipt.amount!!),
                creditAmount = if (type == PartnerType.VENDOR) BigDecimal(receipt.amount!!) else BigDecimal.ZERO,
                tenant = receipt.tenant!!,
                partnerDetailId = receipt.partnerDetailId,
                partnerId = receipt.partnerId,
                paymentType = receipt.paymentType!!,
                paymentMode = if (receipt.paymentType == PaymentType.CASH) PaymentMode.CASH else PaymentMode.BANK
            )

            cashBankLedgerService.checkAndAddCashBankLedgerEntry(receipt.updatedBy!!, cl)
        }
    }
}

