package com.pharmeasy.service

import com.pharmeasy.data.*
import com.pharmeasy.repo.*
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class CheckerService {

    companion object {
        private val log = LoggerFactory.getLogger(CheckerService::class.java)
    }

    @Autowired
    private lateinit var checkerRepo: CheckerRepo

    @Autowired
    private lateinit var companyService: CompanyService

    @Transactional
    fun findCheckers(tenant: String,checkerId: Long? = null,ds: String? = null): MutableList<CheckerDetails?> {

        var companyMappingObj = companyService.getCompanyTenantMappingObject(ds?:tenant)
        if (companyMappingObj != null) {
            return checkerRepo.getByCompany(companyMappingObj.companyId!!, checkerId)
        }
        return mutableListOf()
    }

    fun compareChecker(checkers: MutableList<CheckerDetails?>, userId: String): CheckerDetails? {
        checkers.forEach {
            if (it!!.userId.equals(userId)) {
                return it
            }
        }
        return null
    }

    fun getCheckerByCompanyId(companyId: Long): MutableList<CheckerDetails?> {
        return checkerRepo.getByCompany(companyId)
    }

    fun findChecker(tenant: String): MutableList<CheckerDetails?> {
        val companyMappingObj = companyService.getCompanyTenantMappingObject(tenant)
        if (companyMappingObj != null) {
            return getCheckerByCompanyId(companyMappingObj.companyId)
        }
        return mutableListOf()
    }
}