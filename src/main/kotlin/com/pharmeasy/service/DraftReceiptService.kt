package com.pharmeasy.service

import com.pharmeasy.data.DraftReceipt
import com.pharmeasy.model.InvoiceStatus
import com.pharmeasy.model.ops.PartnerInfo
import com.pharmeasy.model.ops.PaymentInfo
import com.pharmeasy.repo.DraftReceiptRepository
import com.pharmeasy.service.abstracts.SettleableProcessorFactory
import com.pharmeasy.type.AdvancePaymentSource
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.LocalDate
import java.time.LocalDateTime
import javax.transaction.Transactional

@Service
class DraftReceiptService(
    private val settleableProcessorFactory: SettleableProcessorFactory,
    private val draftReceiptRepo: DraftReceiptRepository
) {
    val source: AdvancePaymentSource = AdvancePaymentSource.RIO_PAY

    companion object {
        private val log = LoggerFactory.getLogger(DraftReceiptProcessingStrategyImpl::class.java)
    }

    @Transactional
    fun saveDraftReceipt(draft: DraftReceipt): DraftReceipt {
        return draftReceiptRepo.save(draft)
    }

    fun getReceiptByPaymentTransactionId(txId: String): DraftReceipt? {
        val receipt = draftReceiptRepo.findByPaymentTransactionId(txId)
        return receipt
    }

    fun findDuplicatesByPaymentTransactionId(
        paymentTransactionId: String,
        partnerDetailId: Long,
        tenant: String
    ): List<DraftReceipt> {
        return draftReceiptRepo.findDuplicatesByPaymentTransactionId(
            paymentTransactionId,
            partnerDetailId,
            tenant
        )
    }

    fun findCashTotalForPartner(
        partnerDetailId: Long,
        startDate: LocalDateTime,
        endDate: LocalDateTime
    ): Double {
        return draftReceiptRepo.findCashTotalForPartner(partnerDetailId, startDate, endDate)
    }

    fun findDuplicateCheque(
        chequeNo: String,
        chequeDate: LocalDate,
        partnerDetailId: Long,
        tenant: String
    ): List<DraftReceipt> {
        return draftReceiptRepo.findDuplicateCheque(
            chequeNo,
            chequeDate,
            partnerDetailId,
            tenant
        )
    }

    fun findDuplicateNeftPayments(
        neftId: String,
        partnerDetailId: Long,
        tenant: String
    ): List<DraftReceipt> {
        return draftReceiptRepo.findDuplicateNeftPayments(
            neftId,
            partnerDetailId,
            tenant
        )
    }

    fun save(receipt: DraftReceipt): DraftReceipt {
        return draftReceiptRepo.save(receipt)
    }

    /**
     * Saves the draft receipt and its entity mappings.
     * If the settlement amount is higher than the outstanding amount, it will be adjusted accordingly.
     */
    @Transactional
    fun updateSettleableMappings(
        draftReceipt: DraftReceipt,
        payment: PaymentInfo,
        partnerInfo: PartnerInfo
    ) {
        payment.settleables.forEach {
            val settleableType = it.type.settleableType
            val settleableProcessor = settleableProcessorFactory.getSettleableProcessor(settleableType)
            val settleableDetails = settleableProcessor.getSettleableDetails(it.number, partnerInfo.tenant)
            requireNotNull(settleableDetails)

            val isPaidOff = settleableDetails.pendingAmount <= 0.0 || settleableDetails.status in listOf(
                InvoiceStatus.PAID,
                InvoiceStatus.WRITE_OFF
            )

            // If the invoice is paid off, no need to settle. Handling softly instead of throwing an error
            if(isPaidOff){
                return
            }

            // Settle the amount up to the outstanding amount
            val amountToBeSettled = (it.amount).coerceAtMost(settleableDetails.pendingAmount)
            draftReceipt.addSettleableMapping(settleableDetails, amountToBeSettled)
        }
    }
}
