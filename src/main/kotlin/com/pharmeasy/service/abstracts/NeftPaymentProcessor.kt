package com.pharmeasy.service.abstracts

import com.pharmeasy.model.ops.PartnerInfo
import com.pharmeasy.model.ops.PaymentInfo
import com.pharmeasy.service.DraftReceiptService
import com.pharmeasy.util.DateUtils
import org.springframework.stereotype.Service

@Service
class NeftPaymentProcessor(
    private val draftReceiptService: DraftReceiptService
) : PaymentProcessor(draftReceiptService) {
    override fun validatePayment(payment: PaymentInfo, partnerInfo: PartnerInfo): String? {
        super.validatePayment(payment, partnerInfo)?.let { return it }
        var remark =
            if (payment.bankDetails?.neftId.isNullOrBlank()) {
                "NEFT ID is required for NEFT payments"
            } else {
                null
            }

        val neftDate = DateUtils.getDateFromEpochStamp(payment.transactionDate)!!
        val duplicateNeftPayments = draftReceiptService.findDuplicateNeftPayments(
                payment.bankDetails!!.neftId!!,
            partnerInfo.partnerDetailId,
                partnerInfo.tenant,
            )

        if (duplicateNeftPayments.isNotEmpty()) {
            // TODO: Financial year validation is needed? Add if so add it
            remark = "NEFT ID ${payment.bankDetails.neftId} already exists for the the partner" +
                " in receipt: ${duplicateNeftPayments[0].receiptNumber}"
        }
        return remark
    }
}
