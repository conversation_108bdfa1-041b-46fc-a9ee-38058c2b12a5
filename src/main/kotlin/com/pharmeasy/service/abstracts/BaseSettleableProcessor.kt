package com.pharmeasy.service.abstracts

import com.pharmeasy.data.DraftReceiptEntityMapping
import com.pharmeasy.data.Receipt
import com.pharmeasy.data.Settlement
import com.pharmeasy.dto.SettleableDetails

interface BaseSettleableProcessor {
    /**
     * Get the settleable details for a given number and tenant.
     */
    fun getSettleableDetails(
        number: String,
        tenant: String
    ): SettleableDetails?

    fun getSettleableDetails(id: Long): SettleableDetails?

    fun constructSettlement(
        receipt: Receipt,
        settleableDetails: SettleableDetails,
        settlementMapping: DraftReceiptEntityMapping
    ): Settlement

    fun updateSettlementMapping(
        settlement: Settlement,
        settleableDetails: SettleableDetails,
        settlementMapping: DraftReceiptEntityMapping
    )
}

