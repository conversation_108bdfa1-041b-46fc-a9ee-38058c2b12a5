package com.pharmeasy.service.abstracts

import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.ops.PartnerInfo
import com.pharmeasy.model.ops.PaymentInfo
import com.pharmeasy.service.CompanyService
import com.pharmeasy.service.DocumentMasterService
import com.pharmeasy.service.strategy.PaymentProcessorStrategyFactory
import com.pharmeasy.type.AdvancePaymentSource
import com.pharmeasy.type.DocumentType
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
abstract class BaseReceiptProcessingStrategy(
    private val companyService: CompanyService,
    private val documentService: DocumentMasterService,
    private val paymentProcessorStrategyFactory: PaymentProcessorStrategyFactory,
    private val settleableProcessorFactory: SettleableProcessorFactory
) {
    val source: AdvancePaymentSource = AdvancePaymentSource.SYSTEM
    private val log = LoggerFactory.getLogger(javaClass)

    companion object {
        @JvmStatic
        protected val DEFAULT_RECEIPT_REMARK = "Receipt Created from RIO Payment"
    }

    protected fun validateReceiptRequest(payment: PaymentInfo, partnerInfo: PartnerInfo): String? {
        log.info("Validating receipt details for payment: ${payment.customerTransactionId}")
        require(payment.customerTransactionId.isNotBlank()) { "Payment transaction ID is required" }
        require(payment.transactionAmount > 0) { "Transaction amount must be positive" }

        var remark: String? = null
        payment.settleables.forEach { settleable ->
            val settleableType = settleable.type.settleableType
            val settleableProcessor = settleableProcessorFactory.getSettleableProcessor(settleableType)

            // TODO: Should this be a failure or reject with remark?
            requireNotNull(settleableProcessor.getSettleableDetails(settleable.number, partnerInfo.tenant)) {
                "Invalid $settleableType number: ${settleable.number} for tenant ${partnerInfo.tenant}"
            }
        }
        val paymentProcessor = paymentProcessorStrategyFactory.getInstance(payment.paymentType)
        paymentProcessor.validatePayment(payment, partnerInfo)?.let {
            remark = it
        }

        return remark
    }

    protected fun generateReceiptNumber(tenant: String): String {
        val company = companyService.getCompanyByTenant(tenant)
                ?: throw RequestException("Company not found for tenant $tenant")
        val receiptNumber = documentService.generateDocumentNumber(company.companyCode, DocumentType.RECEIPT_ENTRY)
        return receiptNumber
    }

}
