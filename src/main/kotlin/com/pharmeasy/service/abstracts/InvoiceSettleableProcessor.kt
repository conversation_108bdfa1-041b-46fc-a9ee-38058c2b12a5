package com.pharmeasy.service.abstracts

import com.pharmeasy.data.BkInvoice
import com.pharmeasy.data.DraftReceiptEntityMapping
import com.pharmeasy.data.InvoiceSettlement
import com.pharmeasy.data.Receipt
import com.pharmeasy.data.Settlement
import com.pharmeasy.dto.SettleableDetails
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.InvoiceList
import com.pharmeasy.model.InvoiceStatus
import com.pharmeasy.model.SettlementInvoiceListDto
import com.pharmeasy.proxy.SupplierProxy
import com.pharmeasy.service.InvoiceService
import com.pharmeasy.service.SlipService
import com.pharmeasy.stream.BlockedVendorSettlementPusher
import com.pharmeasy.stream.InvoiceSettlementUpdateHandler
import com.pharmeasy.stream.SettlementInvoiceListGateway
import com.pharmeasy.type.CreationType
import com.pharmeasy.type.PartnerType
import com.pharmeasy.type.SettleableType
import com.pharmeasy.type.SlipStatus
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.LocalDate

@Service
class InvoiceSettleableProcessor(
    private val invoiceService: InvoiceService,
    private val supplierProxy: SupplierProxy,
    private val slipService: SlipService,
    private val invoiceSettlementUpdateHandler: InvoiceSettlementUpdateHandler,
    private val blockedVendorSettlementPusher: BlockedVendorSettlementPusher,
    private val settlementInvoiceListGateway: SettlementInvoiceListGateway
) : BaseSettleableProcessor {
    private val log = LoggerFactory.getLogger(javaClass)

    override fun getSettleableDetails(
        number: String,
        tenant: String
    ): SettleableDetails? {
        val invoice =
            invoiceService.findCustomerInvoiceByInvoiceNumAndTenant(number, tenant)
                ?: return null
        return SettleableDetails(
            invoice.id,
            number,
            invoice.amount,
            (invoice.amount - invoice.paidAmount),
            invoice.status,
            SettleableType.INVOICE
        )
    }

    override fun getSettleableDetails(id: Long): SettleableDetails? {
        return invoiceService.getBkInvoiceById(id)?.let { bkInvoice ->
            SettleableDetails(
                bkInvoice.id,
                bkInvoice.invoiceNum!!,
                bkInvoice.amount,
                (bkInvoice.amount - bkInvoice.paidAmount),
                bkInvoice.status,
                SettleableType.INVOICE
            )
        }
    }

    override fun updateSettlementMapping(
        settlement: Settlement,
        settleableDetails: SettleableDetails,
        settlementMapping: DraftReceiptEntityMapping
    ) {
        val invoiceNumberTenantList = settlement.invoices.map { it.invoiceNum!! to it.tenant!! }
        val invoices =
            invoiceService.findInvoicesByNumberAndTenant(invoiceNumberTenantList)
                .associateBy { it.invoiceNum }

        val partnerIds = mutableListOf<Long?>()
        val paidInvoiceIds = mutableListOf<String>()
        val settlementInvoiceList = mutableListOf<InvoiceList>()
        val invoiceSettlements = mutableListOf<InvoiceSettlement>()

        val updatedInvoices = mutableListOf<BkInvoice>()

        settlement.invoices.forEach {
            invoices[it.invoiceNum]?.let { bki ->
                val (invoice, invoiceSettlement) =
                    invoiceService.updateInvoiceForSettlement(
                        bki,
                        settlementMapping,
                        settlement
                    )
                updatedInvoices.add(invoice)
                if (invoice.status == InvoiceStatus.PAID) {
                    partnerIds.add(invoice.partnerId!!)
                    paidInvoiceIds.add(invoice.invoiceId!!)
                }
                invoiceSettlements.add(invoiceSettlement)
                settlementInvoiceList.add(
                    InvoiceList(
                        invoice.invoiceNum ?: "",
                        invoice.status,
                        invoiceSettlement.paidAmount!!
                    )
                )
            }
        }
        settlement.invoices = updatedInvoices
        sendPostSettlementEvents(settlementInvoiceList, settlement, partnerIds, paidInvoiceIds)

        // TODO: Add settlement distribution mapping when  using advances. ( To be done in web flow )
    }

    private fun sendPostSettlementEvents(
        settlementInvoiceList: MutableList<InvoiceList>,
        settlement: Settlement,
        partnerIds: MutableList<Long?>,
        paidInvoiceIds: MutableList<String>
    ) {
        settlementInvoiceListGateway.sendInvoiceListProducerEvent(SettlementInvoiceListDto(settlementInvoiceList))
        settlement.invoices.onEach {
            invoiceSettlementUpdateHandler.createSettlementConsumer(it.id, CreationType.INVOICE)
        }
        partnerIds.let {
            blockedVendorSettlementPusher.blockedVendorSettlementSinkProducer(it)
        }

        try {
            slipService.statusChangeSlip(paidInvoiceIds, SlipStatus.CLOSED, "Invoice closed by settlement")
        } catch (requestException: RequestException) {
            log.error("Failed to close slips due to request error: ${requestException.message}")
        } catch (e: Exception) {
            log.error("Failed to close slips due to an unexpected error: ${e.message}", e)
        }
    }

    override fun constructSettlement(
        receipt: Receipt,
        settleableDetails: SettleableDetails,
        settlementMapping: DraftReceiptEntityMapping
    ): Settlement {
        val partnerInfo = supplierProxy.supplier(supplierId = listOf(receipt.partnerId))[0]!!
        val invoice =
            invoiceService.findCustomerInvoiceByInvoiceNumAndTenant(settleableDetails.number, receipt.tenant!!)
                ?: throw RequestException("Invoice not found for invoice number: ${settleableDetails.number}")
        val exInvoiceOutstanding = (invoice.amount - invoice.paidAmount)

        require(
            invoice.status !in listOf(InvoiceStatus.PAID, InvoiceStatus.WRITE_OFF)
        ) { "Invoice ${invoice.invoiceNum} is already paid" }
        require(invoice.paidAmount + settlementMapping.entityTxAmount <= invoice.amount) {
            "Receipt amount ${settlementMapping.entityTxAmount} is greater than " +
                "invoice outstanding $exInvoiceOutstanding"
        }

        invoice.paidAmount = invoice.paidAmount.plus(settlementMapping.entityTxAmount)
        return Settlement(
            user = receipt.updatedBy,
            supplierId = invoice.partnerId!!,
            supplierName = partnerInfo.partnerName,
            amount = exInvoiceOutstanding,
            paidAmount = settlementMapping.entityTxAmount,
            remarks = "Settled via RIO Payment", // FixMe: Update as per source
            settlementNumber = null,
            invoices = mutableListOf(invoice.copy()),
            creditNotes = mutableListOf(),
            paymentType = receipt.paymentType!!,
            paymentReference = receipt.paymentTransactionId!!,
            paymentDate = LocalDate.now(),
            partnerId = invoice.partnerId,
            partnerDetailId = invoice.partnerDetailId,
            type = PartnerType.CUSTOMER,
            tenant = receipt.tenant!!,
            chequeDate = null,
            bankId = null,
            bankName = null,
            advancePayment = mutableListOf(),
            chargeInvoice = mutableListOf(),
            paymentSource = receipt.source,
            debitNotes = mutableListOf()
        )
    }
}
