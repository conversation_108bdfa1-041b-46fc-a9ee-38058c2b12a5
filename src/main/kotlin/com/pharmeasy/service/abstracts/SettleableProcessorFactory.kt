package com.pharmeasy.service.abstracts

import com.pharmeasy.type.SettleableType
import org.springframework.stereotype.Service

@Service
class SettleableProcessorFactory(
    private val invoiceSettleableProcessor: InvoiceSettleableProcessor,
    private val debitNoteSettleableProcessor: DebitNoteSettleableProcessor
) {
    fun getSettleableProcessor(type: SettleableType): BaseSettleableProcessor {
        return when (type) {
            SettleableType.INVOICE -> invoiceSettleableProcessor
            SettleableType.DEBIT_NOTE -> debitNoteSettleableProcessor
        }
    }
}
