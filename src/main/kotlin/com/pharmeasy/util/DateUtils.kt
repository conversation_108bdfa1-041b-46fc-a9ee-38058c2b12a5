package com.pharmeasy.util

import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.*

object DateUtils {
    const val DATE_FORMAT_DDMMYYY = "dd-MM-yyyy"

    private fun getCurrentFiscalYear(): Int {
        val month = LocalDate.now().monthValue
        val year = LocalDate.now().year

        return if (month > 3) year else (year - 1)
    }

    fun getFiscalYearCode(): String {
        return getCurrentFiscalYear().toString().substring(2, 4)
    }

    fun getUTCDateTime(datetime: LocalDateTime): LocalDateTime {
        val ldtZoned: ZonedDateTime = datetime.atZone(ZoneId.of("Asia/Kolkata"))
        val utcZoned = ldtZoned.withZoneSameInstant(ZoneId.of("UTC"))
        return utcZoned.toLocalDateTime()
    }

    fun getISTDateTime(datetime: LocalDateTime?): LocalDateTime? {
        if (datetime == null) {
            return null
        }
        val ldtZoned: ZonedDateTime = datetime.atZone(ZoneId.of("UTC"))
        val istZoned = ldtZoned.withZoneSameInstant(ZoneId.of("Asia/Kolkata"))
        return istZoned.toLocalDateTime()
    }

    fun getDateFromEpochStamp(epochStamp: Long?): LocalDate? {
        if (epochStamp == null) {
            return null
        }
        return Instant.ofEpochMilli(epochStamp!!).atZone(ZoneId.systemDefault()).toLocalDate()
    }

    fun getDateTimeFromEpochStamp(epochStamp: Long?): LocalDateTime? {
        if (epochStamp == null) {
            return null
        }
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(epochStamp), TimeZone.getDefault().toZoneId())
    }
}
