package com.pharmeasy.util

import java.lang.Exception
import java.math.BigDecimal

class FlatDiscountTaskUtils {

    companion object {
        val flatDiscountCsvColumns = listOf(
            "PDI",
            "Discount Amount",
            "Raised By",
            "Remarks",
            "Reason Code"
        )

        fun isValidAmount(amount: String?): <PERSON><PERSON><PERSON> {
            return try {
                val amt = BigDecimal.valueOf(amount?.trim()?.toDouble()!!)
                amt > BigDecimal.ZERO
            } catch (e: Exception) {
                false
            }
        }

        fun isIntegerAmount(amount: String?): <PERSON><PERSON>an {
            return try {
                val amt = BigDecimal.valueOf(amount?.trim()?.toDouble()!!)
                amt.stripTrailingZeros().scale() <= 0
            } catch (e: Exception) {
                false
            }
        }

    }
}