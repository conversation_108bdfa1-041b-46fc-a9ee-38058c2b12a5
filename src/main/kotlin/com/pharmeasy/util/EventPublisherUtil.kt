package com.pharmeasy.util

import com.fasterxml.jackson.databind.ObjectMapper
import com.pharmeasy.model.*
import com.pharmeasy.transactionaloutbox.EventPublisher
import com.pharmeasy.transactionaloutbox.OutboxMessage
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class EventPublisherUtil {

    @Autowired
    private lateinit var eventPublisher: EventPublisher<Any>

    companion object {
        private val log = LoggerFactory.getLogger(EventPublisherUtil::class.java)
        val objectMapper = ObjectMapper()
    }


    @Value("\${spring.cloud.stream.bindings.rioCreditNotePdfProducer.destination}")
    private var rioCreditNotePdfProducer: String? = ""

    @Value("\${spring.cloud.stream.bindings.createInvoiceSettlementProducer.destination}")
    private var createInvoiceSettlementProducer: String? = ""

    @Value("\${spring.cloud.stream.bindings.slipEventCallback.destination}")
    private var rioSlipStatusCallbackProducer: String? = ""

    @Value("\${spring.cloud.stream.bindings.retailerDebitNoteCreationProducer.destination}")
    private var createRetailerDebitNoteProducer: String? = ""

    @Value("\${spring.cloud.stream.bindings.makerCheckerApprovalProducer.destination}")
    private var makerCheckerApprovalProducer: String? = ""

    @Value("\${spring.cloud.stream.bindings.paymentTransactionUpdates.destination}")
    private var paymentTransactionUpdates: String? = ""

    @Value("\${spring.cloud.stream.bindings.createGenericEInvoiceProducer.destination}")
    private var createGenericEInvoiceProducer: String? = ""


    fun createVaultCnPdfEvent(data: CreditNotePdfEventDto) {
        log.info("Publishing event to topic: $rioCreditNotePdfProducer")
        eventPublisher.publish(
            OutboxMessage.builder<Any>()
                .payload(data)
                .topic(rioCreditNotePdfProducer)
                .partitionKey(data.creditNoteNumber)
                .build()
        )
    }

    fun createInvoiceSettlementProducer(data: InvoiceSettlementProducerEventDto) {
        log.info("Publishing payment event to topic: $createInvoiceSettlementProducer")
        eventPublisher.publish(
            OutboxMessage.builder<Any>()
                .payload(data)
                .topic(createInvoiceSettlementProducer)
                .partitionKey(data.retailerId.toString())
                .build()
        )
    }

    fun sendSlipStatusUpdateEvent(slipEventCallBackDto: SlipEventCallBackDto) {
        log.info("Publishing event to topic: $rioSlipStatusCallbackProducer")
        eventPublisher.publish(
            OutboxMessage.builder<Any>()
                .payload(slipEventCallBackDto)
                .topic(rioSlipStatusCallbackProducer)
                .partitionKey(slipEventCallBackDto.slipNumber)
                .build()
        )
    }

    fun createRetailerDebitNoteProducer(data: RetailerDNCreationEventDTO) {
        log.info("Publishing retailer debit note event to topic: $createRetailerDebitNoteProducer")
        eventPublisher.publish(
            OutboxMessage.builder<Any>()
                .payload(data)
                .topic(createRetailerDebitNoteProducer)
                .partitionKey(data.debitNoteType.toString())
                .build()
        )
    }

    fun makerCheckerApprovalProducer(data: MakerCheckerRequestApprovalDto) {
        log.info("Publishing maker checker approval to topic: $makerCheckerApprovalProducer")
        eventPublisher.publish(
            OutboxMessage.builder<Any>()
                .payload(data)
                .topic(makerCheckerApprovalProducer)
                .partitionKey(data.entityType.name)
                .build()
        )
    }

    fun sendPaymentTransactionUpdates(event: PaymentTransactionUpdatesData) {
        log.info("Publishing transaction updates back to topic: $paymentTransactionUpdates")
        eventPublisher.publish(
            OutboxMessage.builder<Any>()
                .payload(event)
                .topic(paymentTransactionUpdates)
                .partitionKey(event.data.hashCode().toString())
                .build()
        )
    }

    fun createGenericEInvoiceProducer(event: EinvoiceGenericDto) {
        log.info("Publishing EInvoice transaction updates back to topic: $createGenericEInvoiceProducer")
        eventPublisher.publish(
            OutboxMessage.builder<Any>()
                .payload(event)
                .topic(createGenericEInvoiceProducer)
                .partitionKey(event.type.toString())
                .build()
        )
    }

}

