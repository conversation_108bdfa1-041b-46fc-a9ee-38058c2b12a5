package com.pharmeasy.specification

import com.pharmeasy.data.BkInvoice
import org.springframework.data.jpa.domain.Specification
import javax.persistence.criteria.CriteriaBuilder
import javax.persistence.criteria.CriteriaQuery
import javax.persistence.criteria.Predicate
import javax.persistence.criteria.Root

class InvoiceMultiGetSpecification(
    val invoiceTenantPairs: List<Pair<String, String>>
) : Specification<BkInvoice> {
    override fun toPredicate(
        root: Root<BkInvoice?>,
        cq: CriteriaQuery<*>,
        cb: CriteriaBuilder
    ): Predicate? {
        val predicates = mutableListOf<Predicate>()
        invoiceTenantPairs.forEach {
            predicates.add(
                cb.and(
                    cb.equal(root.get<String>("tenant"), it.second),
                    cb.equal(root.get<String>("invoiceNum"), it.first)
                )
            )
        }
        return cb.or(*predicates.toTypedArray())
    }
}
