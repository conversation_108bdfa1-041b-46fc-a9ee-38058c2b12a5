package com.pharmeasy.config

import com.pharmeasy.model.AuthenticationToken
import org.springframework.data.domain.AuditorAware
import org.springframework.security.core.context.SecurityContextHolder
import java.util.*


class AuditAwareImpl : AuditorAware<String?> {
    override fun getCurrentAuditor(): Optional<String?> {
        return Optional.ofNullable(SecurityContextHolder.getContext())
            .map {
                val token = it.authentication as AuthenticationToken?
                token?.user ?: "SYSTEM"
            }
    }
}