package resources

import com.pharmeasy.data.*
import com.pharmeasy.model.ops.RIOPaymentDTO
import com.pharmeasy.data.ops.PaymentTransaction
import com.pharmeasy.data.ops.Payment
import com.pharmeasy.data.ops.PaymentExcess
import com.pharmeasy.model.*
import com.pharmeasy.model.advancepayment.AdvancePaymentDto
import com.pharmeasy.model.advancepayment.PaymentRefItem
import com.pharmeasy.model.advancepayment.UpdateAdvancePaymentDto
import com.pharmeasy.type.*
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import com.pharmeasy.type.ops.OPSPaymentMode
import org.springframework.stereotype.Component



object Constants{

    val rioPaymentDTO = RIOPaymentDTO(
        invoiceNumber = "B0124-24A0002559-Unit",  // Invoice number
        retailerTxnId = "staging_CL0011737198",  // Retailer Transaction ID
        retailerTxnAmount = 254.0,  // Retailer Transaction Amount
        retailerTxnType = "UPI",  // Retailer Transaction Type
        initiatedBy = "RETAILER",  // Initiator of the transaction
        paymentMode = "DIGITAL_PAYMENT",  // Payment Mode
        type = CreationType.INVOICE,  // Type of the payment, mapped to CreationType enum
        retailerTxnDate = 1730898301000,  // Retailer Transaction Date (timestamp)
        retailerName = "Opt_NoGST",  // Retailer Name
        retailerPartyCode = "40567",  // Retailer Party Code
        retailerFrontEndPartyCode = "40567",  // Retailer Front End Party Code
        distributorInvoiceId = "230761Unit",  // Distributor Invoice ID
        distributorInvoiceAmount = 254.0,  // Distributor Invoice Amount
        distributorInvoiceOutstandingAmount = 254.0,  // Outstanding Amount for the distributor invoice
        distributorInvoiceDueDate = 1732320000,  // Distributor Invoice Due Date (timestamp)
        retailerTxnStatus = "SUCCESS",  // Retailer Transaction Status
        advanceAmount = null,  // Advance Amount (nullable)
        invoicePrefix = "SB",  // Invoice Prefix
        distributorInvoiceDate = 1729728000,  // Distributor Invoice Date (timestamp)
        category = "INVOICE",  // Category of payment
        retailerTotalTxnAmount = 254.0,  // Total Retailer Transaction Amount
        distributorId = 1891,  // Distributor ID
        creditTransactionId = null,  // Credit Transaction ID (nullable)
        creditDueDate = null,  // Credit Due Date (nullable)
        creditPartner = null  // Credit Partner (nullable)
    )


    val multipleRioPaymentDTO = listOf(
        RIOPaymentDTO(
            invoiceNumber = "B0124-24A0002559-Unit",  // Invoice number
            retailerTxnId = "staging_CL0011737198",  // Retailer Transaction ID
            retailerTxnAmount = 254.0,  // Retailer Transaction Amount
            retailerTxnType = "UPI",  // Retailer Transaction Type
            initiatedBy = "RETAILER",  // Initiator of the transaction
            paymentMode = "DIGITAL_PAYMENT",  // Payment Mode
            type = CreationType.INVOICE,  // Type of the payment, mapped to CreationType enum
            retailerTxnDate = 1730898301000,  // Retailer Transaction Date (timestamp)
            retailerName = "Opt_NoGST",  // Retailer Name
            retailerPartyCode = "40567",  // Retailer Party Code
            retailerFrontEndPartyCode = "40567",  // Retailer Front End Party Code
            distributorInvoiceId = "230761Unit",  // Distributor Invoice ID
            distributorInvoiceAmount = 254.0,  // Distributor Invoice Amount
            distributorInvoiceOutstandingAmount = 254.0,  // Outstanding Amount for the distributor invoice
            distributorInvoiceDueDate = 1732320000,  // Distributor Invoice Due Date (timestamp)
            retailerTxnStatus = "SUCCESS",  // Retailer Transaction Status
            advanceAmount = null,  // Advance Amount (nullable)
            invoicePrefix = "SB",  // Invoice Prefix
            distributorInvoiceDate = 1729728000,  // Distributor Invoice Date (timestamp)
            category = "INVOICE",  // Category of payment
            retailerTotalTxnAmount = 808.0,  // Total Retailer Transaction Amount
            distributorId = 1891,  // Distributor ID
            creditTransactionId = null,  // Credit Transaction ID (nullable)
            creditDueDate = null,  // Credit Due Date (nullable)
            creditPartner = null  // Credit Partner (nullable)
        ),RIOPaymentDTO(
            invoiceNumber = "B0124-24A0002551-Unit",  // Invoice number
            retailerTxnId = "staging_CL0011737198",  // Retailer Transaction ID
            retailerTxnAmount = 554.0,  // Retailer Transaction Amount
            retailerTxnType = "UPI",  // Retailer Transaction Type
            initiatedBy = "RETAILER",  // Initiator of the transaction
            paymentMode = "DIGITAL_PAYMENT",  // Payment Mode
            type = CreationType.INVOICE,  // Type of the payment, mapped to CreationType enum
            retailerTxnDate = 1730898301000,  // Retailer Transaction Date (timestamp)
            retailerName = "Opt_NoGST",  // Retailer Name
            retailerPartyCode = "40567",  // Retailer Party Code
            retailerFrontEndPartyCode = "40567",  // Retailer Front End Party Code
            distributorInvoiceId = "230762Unit",  // Distributor Invoice ID
            distributorInvoiceAmount = 554.0,  // Distributor Invoice Amount
            distributorInvoiceOutstandingAmount = 554.0,  // Outstanding Amount for the distributor invoice
            distributorInvoiceDueDate = 1732320000,  // Distributor Invoice Due Date (timestamp)
            retailerTxnStatus = "SUCCESS",  // Retailer Transaction Status
            advanceAmount = null,  // Advance Amount (nullable)
            invoicePrefix = "SB",  // Invoice Prefix
            distributorInvoiceDate = 1729728000,  // Distributor Invoice Date (timestamp)
            category = "INVOICE",  // Category of payment
            retailerTotalTxnAmount = 808.0,  // Total Retailer Transaction Amount
            distributorId = 1891,  // Distributor ID
            creditTransactionId = null,  // Credit Transaction ID (nullable)
            creditDueDate = null,  // Credit Due Date (nullable)
            creditPartner = null  // Credit Partner (nullable)
        )
    )



    val rioPaymentAdvanceDTO = listOf(RIOPaymentDTO(
        invoiceNumber = "B0124-24A0002559-Unit",  // Invoice number
        retailerTxnId = "staging_CL0011737198",  // Retailer Transaction ID
        retailerTxnAmount = 254.0,  // Retailer Transaction Amount
        retailerTxnType = "UPI",  // Retailer Transaction Type
        initiatedBy = "RETAILER",  // Initiator of the transaction
        paymentMode = "DIGITAL_PAYMENT",  // Payment Mode
        type = CreationType.INVOICE,  // Type of the payment, mapped to CreationType enum
        retailerTxnDate = 1730898301000,  // Retailer Transaction Date (timestamp)
        retailerName = "Opt_NoGST",  // Retailer Name
        retailerPartyCode = "40567",  // Retailer Party Code
        retailerFrontEndPartyCode = "40567",  // Retailer Front End Party Code
        distributorInvoiceId = "230761Unit",  // Distributor Invoice ID
        distributorInvoiceAmount = 254.0,  // Distributor Invoice Amount
        distributorInvoiceOutstandingAmount = 254.0,  // Outstanding Amount for the distributor invoice
        distributorInvoiceDueDate = 1732320000,  // Distributor Invoice Due Date (timestamp)
        retailerTxnStatus = "SUCCESS",  // Retailer Transaction Status
        advanceAmount = null,  // Advance Amount (nullable)
        invoicePrefix = "SB",  // Invoice Prefix
        distributorInvoiceDate = 1729728000,  // Distributor Invoice Date (timestamp)
        category = "INVOICE",  // Category of payment
        retailerTotalTxnAmount = 1191.0,  // Total Retailer Transaction Amount
        distributorId = 1891,  // Distributor ID
        creditTransactionId = null,  // Credit Transaction ID (nullable)
        creditDueDate = null,  // Credit Due Date (nullable)
        creditPartner = null  // Credit Partner (nullable)
    ),RIOPaymentDTO(
        invoiceNumber = null,  // Invoice number
        retailerTxnId = "staging_CL0011737198",  // Retailer Transaction ID
        retailerTxnAmount = 937.0,  // Retailer Transaction Amount
        retailerTxnType = "UPI",  // Retailer Transaction Type
        initiatedBy = "RETAILER",  // Initiator of the transaction
        paymentMode = "DIGITAL_PAYMENT",  // Payment Mode
        type = null,  // Type of the payment, mapped to CreationType enum
        retailerTxnDate = 1730898301000,  // Retailer Transaction Date (timestamp)
        retailerName = "Opt_NoGST",  // Retailer Name
        retailerPartyCode = "40567",  // Retailer Party Code
        retailerFrontEndPartyCode = "40567",  // Retailer Front End Party Code
        distributorInvoiceId = null,  // Distributor Invoice ID
        distributorInvoiceAmount = 1191.0,  // Distributor Invoice Amount
        distributorInvoiceOutstandingAmount = null,  // Outstanding Amount for the distributor invoice
        distributorInvoiceDueDate = null,  // Distributor Invoice Due Date (timestamp)
        retailerTxnStatus = "SUCCESS",  // Retailer Transaction Status
        advanceAmount = 937.0,  // Advance Amount (nullable)
        invoicePrefix = "SB",  // Invoice Prefix
        distributorInvoiceDate = null,  // Distributor Invoice Date (timestamp)
        category = "INVOICE",  // Category of payment
        retailerTotalTxnAmount = 1191.0,  // Total Retailer Transaction Amount
        distributorId = 1891,  // Distributor ID
        creditTransactionId = null,  // Credit Transaction ID (nullable)
        creditDueDate = null,  // Credit Due Date (nullable)
        creditPartner = null  // Credit Partner (nullable)
    ))

    val completeAdvanceEvent = RIOPaymentDTO(
        invoiceNumber = null,  // Invoice number
        retailerTxnId = "staging_CL0011737198",  // Retailer Transaction ID
        retailerTxnAmount = 937.0,  // Retailer Transaction Amount
        retailerTxnType = "UPI",  // Retailer Transaction Type
        initiatedBy = "RETAILER",  // Initiator of the transaction
        paymentMode = "DIGITAL_PAYMENT",  // Payment Mode
        type = null,  // Type of the payment, mapped to CreationType enum
        retailerTxnDate = 1730898301000,  // Retailer Transaction Date (timestamp)
        retailerName = "Opt_NoGST",  // Retailer Name
        retailerPartyCode = "40567",  // Retailer Party Code
        retailerFrontEndPartyCode = "40567",  // Retailer Front End Party Code
        distributorInvoiceId = null,  // Distributor Invoice ID
        distributorInvoiceAmount = null,  // Distributor Invoice Amount
        distributorInvoiceOutstandingAmount = null,  // Outstanding Amount for the distributor invoice
        distributorInvoiceDueDate = null,  // Distributor Invoice Due Date (timestamp)
        retailerTxnStatus = "SUCCESS",  // Retailer Transaction Status
        advanceAmount = 937.0,  // Advance Amount (nullable)
        invoicePrefix = "SB",  // Invoice Prefix
        distributorInvoiceDate = null,  // Distributor Invoice Date (timestamp)
        category = "INVOICE",  // Category of payment
        retailerTotalTxnAmount = 937.0,  // Total Retailer Transaction Amount
        distributorId = 1891,  // Distributor ID
        creditTransactionId = null,  // Credit Transaction ID (nullable)
        creditDueDate = null,  // Credit Due Date (nullable)
        creditPartner = null  // Credit Partner (nullable)
    )

    val bkInvoice = BkInvoice(
        id = 0L,
        invoiceNum = "B0124-24A0002559-Unit",
        amount = 254.0,
        paidAmount = 0.0,
        tenant = "th124",
        createdOn = LocalDateTime.now(),
        updatedOn = LocalDateTime.now(),
        createdBy = "user",
        updatedBy = "user",
        supplierName = "supplier",
        dueDate = LocalDate.now().plusDays(30),
        settledOn = null,
        partnerDetailId = 40567,
        partnerId = 40537,
        invoiceId = "230761Unit"
    )

    val multipleBkInvoice = listOf(
        BkInvoice(
            id = 0L,
            invoiceNum = "B0124-24A0002559-Unit",
            amount = 254.0,
            paidAmount = 0.0,
            tenant = "th124",
            createdOn = LocalDateTime.now(),
            updatedOn = LocalDateTime.now(),
            createdBy = "user",
            updatedBy = "user",
            supplierName = "supplier",
            dueDate = LocalDate.now().plusDays(30),
            settledOn = null,
            partnerDetailId = 40567,
            partnerId = 40537,
            invoiceId = "230761Unit"
        ),BkInvoice(
            id = 0L,
            invoiceNum = "B0124-24A0002551-Unit",
            amount = 554.0,
            paidAmount = 0.0,
            tenant = "th124",
            createdOn = LocalDateTime.now(),
            updatedOn = LocalDateTime.now(),
            createdBy = "user",
            updatedBy = "user",
            supplierName = "supplier",
            dueDate = LocalDate.now().plusDays(30),
            settledOn = null,
            partnerDetailId = 40567,
            partnerId = 40537,
            invoiceId = "230762Unit"
        )
    )

    val payment = Payment(
        id = 0L,
        referenceId = bkInvoice.invoiceId!!,
        bkInvoiceId = bkInvoice.id,
        invoiceNumber = bkInvoice.invoiceNum!!,
        invoicedAmount = BigDecimal.valueOf(bkInvoice.amount),
        paidAmount = BigDecimal.valueOf(bkInvoice.paidAmount),
        isDisbursed = false,
        isPaid = false,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
        transactionType = CreationType.INVOICE
        )
    val paymentTransaction = PaymentTransaction(
    id = 0L,
    payment = payment,
    amount = BigDecimal.valueOf(254.0),
    paymentMode = OPSPaymentMode.RIO_PAY,
    paymentReferenceId = "staging_CL0011737198",
    createdAt = LocalDateTime.now(),
        transactionAmount = BigDecimal.valueOf(254.0),
    )
    val settlement = Settlement(
        id = 0L,
        createdOn = LocalDateTime.now(),
        updatedOn = LocalDateTime.now(),
        createdBy = "user",
        supplierId = 1L,
        supplierName = "supplier",
        amount = 254.0,
        paidAmount = 254.0,
        remarks = "remarks",
        settlementNumber = "settlementNumber-123",
        paymentType = PaymentType.RIO_PAY,
        paymentReference = "staging_CL0011737198",
        paymentDate = LocalDate.now(),
        partnerId = 40537,
        partnerDetailId = 40567,
        type = PartnerType.CUSTOMER,
        tenant = "th124",
        invoices = TODO(),
        creditNotes = TODO(),
        chequeDate = TODO(),
        bankId = TODO(),
        bankName = TODO(),
        isBounced = TODO(),
        reversed = TODO(),
        advancePayment = TODO(),
        chargeInvoice = TODO(),
        charge = TODO(),
        receipt = TODO(),
        paymentSource = TODO(),
        retailerDebitNotes = TODO(),
        uuid = TODO()
    )

    val multipleSettlement = listOf(Settlement(
        id = 0L,
        createdOn = LocalDateTime.now(),
        updatedOn = LocalDateTime.now(),
        createdBy = "user",
        supplierId = 1L,
        supplierName = "supplier",
        amount = 254.0,
        paidAmount = 254.0,
        remarks = "remarks",
        settlementNumber = "settlementNumber-123",
        paymentType = PaymentType.RIO_PAY,
        paymentReference = "staging_CL0011737198",
        paymentDate = LocalDate.now(),
        partnerId = 40537,
        partnerDetailId = 40567,
        type = PartnerType.CUSTOMER,
        tenant = "th124",
        invoices = TODO(),
        creditNotes = TODO(),
        chequeDate = TODO(),
        bankId = TODO(),
        bankName = TODO(),
        isBounced = TODO(),
        reversed = TODO(),
        advancePayment = TODO(),
        chargeInvoice = TODO(),
        charge = TODO(),
        receipt = TODO(),
        paymentSource = TODO(),
        retailerDebitNotes = TODO(),
        uuid = TODO()
    ),Settlement(
        id = 0L,
        createdOn = LocalDateTime.now(),
        updatedOn = LocalDateTime.now(),
        createdBy = "user",
        supplierId = 1L,
        supplierName = "supplier",
        amount = 554.0,
        paidAmount = 554.0,
        remarks = "remarks",
        settlementNumber = "settlementNumber-124",
        paymentType = PaymentType.RIO_PAY,
        paymentReference = "staging_CL0011737198",
        paymentDate = LocalDate.now(),
        partnerId = 40537,
        partnerDetailId = 40567,
        type = PartnerType.CUSTOMER,
        tenant = "th124",
        invoices = TODO(),
        creditNotes = TODO(),
        chequeDate = TODO(),
        bankId = TODO(),
        bankName = TODO(),
        isBounced = TODO(),
        reversed = TODO(),
        advancePayment = TODO(),
        chargeInvoice = TODO(),
        charge = TODO(),
        receipt = TODO(),
        paymentSource = TODO(),
        retailerDebitNotes = TODO(),
        uuid = TODO()
    )
    )

    val creditNote = CreditNote(
        id = 12345L,
        createdOn = LocalDateTime.now().minusDays(5),
        updatedOn = LocalDateTime.now(),
        createdBy = "TestCreator",
        updatedBy = "TestUpdater",
        status = NoteStatus.PENDING,
        noteType = NoteTypes.PURCHASE_RETURN,
        amount = BigDecimal("1500.75"),
        supplierId = 40537,
        supplierName = "Test Supplier",
        remarks = "Test remark for credit note",
        settlementId = null,
        invoiceId = 67890L,
        closureType = CreditNoteClosureType.SETTLEMENT,
        partnerId = 40537,
        partnerDetailId = 40567,
        tenant = "TestTenant",
        creditNoteNumber = "CN-2024-001",
        type = PartnerType.VENDOR,
        client = InvoiceType.VENDOR,
        receiptStatus = ReceiptStatus.RECEIVED,
        expectedDate = LocalDate.now().plusDays(10),
        vendorCreditNoteDate = LocalDate.now().minusDays(3),
        vendorCreditNoteAmount = BigDecimal("1400.50"),
        vendorCreditNoteNumber = "VENDOR-CN-2024-001",
        qrCode = "QRCODE123",
        irn = "IRN-2024-XYZ",
        remainingAmount = BigDecimal("100.25"),
        parentTenant = "ParentTenant1",
        isMigrated = false,
        referenceId = "REF-2024-ABC",
        baseCNAmount = 1350.50,
        roundOffAmount = 0.25,
        referenceDnId = 1122L,
        documentDate = LocalDateTime.now().minusDays(2)
    )

    val creditNoteSettlement = Settlement(
        id = 1L,
        createdOn = LocalDateTime.now(),
        updatedOn = LocalDateTime.now(),
        createdBy = "user",
        supplierId = 40537,
        supplierName = "supplier",
        amount = 254.0,
        paidAmount = 254.0,
        remarks = "remarks",
        settlementNumber = "settlementNumber-123",
        paymentType = PaymentType.CREDITNOTE,
        paymentReference = "CN-123",
        paymentDate = LocalDate.now(),
        partnerId = 40537,
        partnerDetailId = 40567,
        type = PartnerType.CUSTOMER,
        tenant = "th124",
        creditNotes = mutableListOf(creditNote),
        invoices = TODO(),
        chequeDate = TODO(),
        bankId = TODO(),
        bankName = TODO(),
        isBounced = TODO(),
        reversed = TODO(),
        advancePayment = TODO(),
        chargeInvoice = TODO(),
        charge = TODO(),
        receipt = TODO(),
        paymentSource = TODO(),
        retailerDebitNotes = TODO(),
        uuid = TODO()
    )

    val paymentExcess = PaymentExcess(
        id = 1L,
        paymentTransaction = paymentTransaction,
        amount = BigDecimal.valueOf(937.0),
        utr = "staging_CL0011737198",
        creditNoteNumber = null,
        createdAt = LocalDateTime.now(),
        advancePaymentId = "adv-1"

    )
    val advancePayment = AdvancePayment(
        id =0L,
        createdOn = LocalDateTime.now(),
        updatedOn = LocalDateTime.now(),
        createdBy = "user",
        createdByName = "user",
        updatedBy = "user",
        userEmail = "user",
        assignedTo = "user",
        assignedToId = "user",
        approvalDate = LocalDate.now(),
        vendorName = "vendor",
        documentId = "adv-1",
        typeOfAdvance = AdvanceType.DIRECT,
        type = PartnerType.CUSTOMER,
        status = Status.APPROVED,
        amount = BigDecimal.valueOf(937.0),
        amountPending = BigDecimal.valueOf(937.0),
        partnerDetailId = 40567,
        partnerId = 40537,
        tenant = "th124",
        companyId = 9L,
        client = InvoiceType.RIO,
        remarks = "APPROVED VIA RIO PAY",
        consumed = false,
        paymentType = PaymentType.RIO_PAY,
        paymentReference = "staging_CL0011737198",
        paymentDate = LocalDate.now()
    )
    val supplier = Supplier(
        partnerId = 40537,
        partnerName = "vendor",
        firmTypes = null,
        partnerDetailList = mutableListOf(),
        retailerCommercial = null,
        distributorCommercial = null
    )

    val advancePaymentDto = AdvancePaymentDto(
        amount =  BigDecimal.valueOf(937.0),
        partnerId = 1L,
        partnerName = "vendor",
        partnerDetailId = 40567,
        tenant = "th124",
        type = PartnerType.CUSTOMER,
        client = InvoiceType.RIO,
        typeOfAdvance = AdvanceType.INVOICE,
        remarks = "CREATED FROM RIO AUTO SETTLEMENT",
        createdByName = "SYSTEM",
        userEmail = "",
        refDocuments = listOf(PaymentRefItem("staging_CL0011737198", LocalDate.now())),
        source = AdvancePaymentSource.RIO_PAY
    )
    val updateAdvancePaymentDto = UpdateAdvancePaymentDto(
        amount =  BigDecimal.valueOf(937.0),
        paymentReference = "staging_CL0011737198",
        paymentType = PaymentType.UPI,
        paymentDate = LocalDate.now(),
        chequeDate = null,
        bankName = "",
        remarks = "APPROVED VIA RIO PAY",
        tenant = "th124",
        change = true,
        source = AdvancePaymentSource.RIO_PAY
    )
    val company = Company(
        id =1L,
        companyCode = "companyCode",
        name = "company",
        darkStore = false,
        updatedBy = "user",
        cashBalance = BigDecimal.valueOf(5000),
        bankBalance = BigDecimal.valueOf(5000),
        enableRioAutoCn = false,
        isActive = true,
        rioEnabled = 0,
        isAutomail = false,
        bankSlipTemplateName = "template",
        dummyAccountPdi = 1L,
        isRetailerDnEnabled = false,
        isCnPrintEnabled = false
    )

    val receipt = Receipt(
        id = 0L,
        receiptNumber = "RCP-123",
        createdAt = LocalDateTime.now(),
        createdBy = "user",
        updatedAt = null,
        updatedBy = "user",
        paymentTransactionId = "staging_CL0011737198",
        remarks = "RECEIPT GENERATED",
        amount = 254.0,
        status =  ReceiptStatus.GENERATED,
        iteration =  1,
        advanceAmount = 0.0,
        source = AdvancePaymentSource.RIO_PAY
    )

    val multipleReceipt = Receipt(
        id = 0L,
        receiptNumber = "RCP-124",
        createdAt = LocalDateTime.now(),
        createdBy = "user",
        updatedAt = null,
        updatedBy = null,
        paymentTransactionId = "staging_CL0011737198",
        remarks = "RECEIPT GENERATED",
        amount = 808.0,
        status =  ReceiptStatus.GENERATED,
        iteration =  1,
        advanceAmount = 0.0,
        source = AdvancePaymentSource.RIO_PAY
    )

    val receiptWithAdvance = Receipt(
        id = 0L,
        receiptNumber = "RCP-123",
        createdAt = LocalDateTime.now(),
        createdBy = "user",
        updatedAt = null,
        updatedBy = null,
        paymentTransactionId = "staging_CL0011737198",
        remarks = "RECEIPT GENERATED",
        amount = 1191.0,
        status =  ReceiptStatus.GENERATED,
        iteration =  1,
        advanceAmount = 937.0,
        source = AdvancePaymentSource.RIO_PAY
    )


    val settlement1 = Settlement(
        id = 0L,
        createdOn = LocalDateTime.now(),
        updatedOn = LocalDateTime.now(),
        createdBy = "user",
        supplierId = 1L,
        supplierName = "Supplier",
        amount = 100.0,
        paidAmount = 100.0,
        remarks = "Test remarks",
        settlementNumber = "S123",
        paymentType = PaymentType.CHEQUE,
        paymentReference = "TX123",
        paymentDate = LocalDate.now(),
        partnerId = 40537,
        partnerDetailId = 40567,
        type = PartnerType.CUSTOMER,
        tenant = "th124",
        chequeDate = LocalDate.now(),
        bankId = 1L,
        bankName = "Bank",
        isBounced = false,
        reversed = false,
        invoices = mutableListOf(),
        creditNotes = mutableListOf(),
        advancePayment = mutableListOf(),
        chargeInvoice = mutableListOf(),
        charge = false,
        receipt = mutableListOf(),
        paymentSource = AdvancePaymentSource.SYSTEM,
        retailerDebitNotes = mutableListOf(),
        uuid = TODO()
    )

    val settlement2 = Settlement(
        id = 0L,
        createdOn = LocalDateTime.now(),
        updatedOn = LocalDateTime.now(),
        createdBy = "user",
        supplierId = 1L,
        supplierName = "Supplier",
        amount = 200.0,
        paidAmount = 200.0,
        remarks = "Test remarks",
        settlementNumber = "S124",
        paymentType = PaymentType.CHEQUE,
        paymentReference = "TX124",
        paymentDate = LocalDate.now(),
        partnerId = 40537,
        partnerDetailId = 40567,
        type = PartnerType.CUSTOMER,
        tenant = "th124",
        chequeDate = LocalDate.now(),
        bankId = 1L,
        bankName = "Bank",
        isBounced = false,
        reversed = false,
        invoices = mutableListOf(),
        creditNotes = mutableListOf(),
        advancePayment = mutableListOf(),
        chargeInvoice = mutableListOf(),
        charge = false,
        receipt = mutableListOf(),
        paymentSource = AdvancePaymentSource.SYSTEM,
        retailerDebitNotes = mutableListOf(),
        uuid = TODO()
    )

}

