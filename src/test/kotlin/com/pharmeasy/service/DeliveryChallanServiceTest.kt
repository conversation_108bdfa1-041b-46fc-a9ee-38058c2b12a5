package com.pharmeasy.service

import com.pharmeasy.BookkeeperApplication
import com.pharmeasy.data.DeliveryChallan
import com.pharmeasy.repo.*
import com.pharmeasy.service.DeliveryChallanService
import org.junit.jupiter.api.Test
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase
import org.springframework.transaction.annotation.Transactional
import org.junit.jupiter.api.Assertions.assertEquals
import org.springframework.beans.factory.annotation.Autowired
import org.mockito.ArgumentCaptor
import org.mockito.Captor
import com.pharmeasy.model.DeliveryChallanDto
import java.math.BigDecimal
import java.time.LocalDate

@SpringBootTest(classes = [BookkeeperApplication::class])
@ActiveProfiles("test")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@Transactional
class DeliveryChallanServiceTest {

    @Autowired
    private lateinit var deliveryChallanRepo: DeliveryChallanRepo

    @Autowired
    private lateinit var deliveryChallanService: DeliveryChallanService

    @Captor
    private lateinit var deliveryChallanCaptor: ArgumentCaptor<DeliveryChallan>

    @Test
    fun `test saveDeliveryChallan`() {
        val documentDate = LocalDate.now().minusDays(1)
        val deliveryChallanDto = DeliveryChallanDto(
            dcNumber = "DC123",
            dcValue = BigDecimal(1000),
            tenant = "th124",
            pdi = 40567,
            documentDate = documentDate,
            items = mutableListOf(),
            settledEvents = mutableListOf()
        )

         deliveryChallanService.saveDeliveryChallan(deliveryChallanDto)

        val savedDeliveryChallan = deliveryChallanRepo.findByDcNumber(deliveryChallanDto.dcNumber)
        assertEquals(documentDate, savedDeliveryChallan?.documentDate)
    }
}