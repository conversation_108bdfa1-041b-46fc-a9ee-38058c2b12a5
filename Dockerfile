FROM adoptopenjdk/openjdk11:jdk-11.0.6_10-alpine-slim AS BUILDER

RUN mkdir -p $HOME/.m2
RUN wget 'https://s3.ap-south-1.amazonaws.com/mercury.samples/settings.xml' -O $HOME/.m2/settings.xml
RUN wget 'https://s3.ap-south-1.amazonaws.com/mercury.samples/settings-security.xml' -O $HOME/.m2/settings-security.xml

RUN mkdir -p /src
COPY . /src
WORKDIR /src

RUN ./gradlew clean build

FROM sonarsource/sonar-scanner-cli AS SAT

ARG SONAR_LOGIN
ARG SONAR
ARG SONAR_HOST_URL

ENV BUILD_HOME=/src

COPY --from=BUILDER $BUILD_HOME /usr/src

RUN if [ "$SONAR" = "true" ] ; then sonar-scanner -Dsonar.login=${SONAR_LOGIN} -Dsonar.host.url=${SONAR_HOST_URL}; fi

FROM openjdk:11-jre

ENV BUILD_HOME=/src
WORKDIR /

COPY --from=BUILDER $BUILD_HOME/build/libs/bookkeeper.jar .

ADD run-java.sh /

ENV JAVA_MAX_MEM_RATIO 65
ENV JAVA_APP_JAR bookkeeper.jar

CMD sh /run-java.sh
