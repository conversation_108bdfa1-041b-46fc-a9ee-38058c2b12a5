env: dev
project_name: bookkeeper
namespace: dev-thea
fqdn: "{{ env }}\\.(app|wms|thea|arsenal|vault|hustler|store)\\.scm\\.gomercury\\.in"
route: "/api/bookkeeper/"
tag: "{{ env }}-{{ build_number }}"
context: scm-staging-eks
profile: gomercury
account_no: ************
min_replicas: 1
max_replicas: 1
requests_cpu: 130m
requests_memory: 230Mi
limits_cpu: 2.0
limits_memory: 2048Mi
sonar: "false"
config_map_yaml: |-
  application.yml: |-
    app:
      client.log: false
      reportEmailEnabled: true
      alertEmailEnabled: true
      url: https://vault.mercuryonline.co/advance-payment
      partner:
        retailer : 3
        retailerAlpha : 8
        cnf : 1
        distributor : 2
        holding : 4
        manufacturer : 5
    spring:
      profile: {{ env }}
      cloud:
        stream:
          bindings:
            createBookkeeperInvoice:
              destination: {{ env }}_create_book_keeper_invoice
              group: book_keeper
            createDebitNotes:
              destination: {{ env }}_create_debit_note
              group: book_keeper
            createDcCallBackProducer:
              destination: dev_pr_dc_callback
              group: book_keeper
            createDeliveryChallan:
              destination: dev_pr_dc_event
              group: book_keeper
            createCustomerDebitNotes:
              destination: {{ env }}_return_complete_accounts
              group: book_keeper
            createCustomerInvoice:
              destination: {{ env }}_retailer_invoice_creation
              group: book_keeper
            sendNotification:
              destination: {{ env }}_notify
            createGatePassEvent:
              destination: {{ env }}_gatepass_event
              group: book_keeper
            createCustomerDSInvoice:
              destination: {{ env }}_retailer_store_invoice_creation
              group: book_keeper
            createCustomerDsDebitNote:
              destination: {{ env }}_darkstore_return_complete_accounts
              group: book_keeper
            createConsolInvoice:
              destination: {{ env }}_invoice_creation
              group: book_keeper
            createVendorPortalConsumerSyncEvent:
              destination: {{ env }}_vp_bookkeeper_sync
              group: book_keeper
            createVendorPortalProducerSyncEvent:
              destination: {{ env }}_vp_bookkeeper_sync
              group: book_keeper
            createEInvoiceProducer:
              destination: einvoice_creation_bookkeeper
              producer:
                partitionKeyExpression: payload.tenant
                partitionCount: 1
            createGenericEWayBillProducer:
              destination: {{ env }}_generic_ewaybill_creation_bookkeeper
              group: book_keeper
              producer:
                partitionKeyExpression: payload.sourceType
                partitionCount: 1
            createGenericEWaybillConsumer:
              destination: {{ env }}_generic_ewaybill_creation_bookkeeper
              group: book_keeper
            createEInvoiceConsumer:
              destination: einvoice_creation_bookkeeper
              group: book_keeper
            createBookkeeperRioInvoice:
              destination: rio_invocie_event
            createInvoiceSettlementConsumer:
              destination: bookkeeper_settlement_event_consumer
              group: bookkeeper
            paymentTransactionUpdates:
              destination: {{ env }}_payment_transaction_updates
            genericEInvoiceRetryTriggerProducer:
              destination: {{ env }}_generic_einvoice_auto_retry_trigger
              group: book_keeper
              producer:
                partitionCount: 1
            genericEInvoiceRetryTriggerConsumer:
              destination: {{ env }}_generic_einvoice_auto_retry_trigger
              group: book_keeper
            genericEInvoiceCreationProducerV2:
              destination: {{ env }}_generic_einvoice_creation
              group: book_keeper
              producer:
                partitionCount: 2
            genericEInvoiceCreationConsumerV2:
              destination: {{ env }}_generic_einvoice_creation
              group: book_keeper
            cacheCornProducer:
              destination: {{ env }}_cache_vendor_ledger
              group: bookkeeper
            cacheCornConsumer:
              destination: {{ env }}_cache_vendor_ledger
              group: bookkeeper
          kafka:
             binder:
                brokers: kafka.scm.gomercury.in:9092
                replicationFactor: 1
      datasource-write
        hikari:
          password: ${WRITER_DB_PASS}
          jdbc-url: *******************************************************************************************************************************************
          username: ${WRITER_DB_USER}
      datasource-read:
        hikari:
          password: ${READER_DB_PASS}
          jdbc-url: *******************************************************************************************************************************************
          username: ${READER_DB_USER}
      jpa:
        show-sql: false