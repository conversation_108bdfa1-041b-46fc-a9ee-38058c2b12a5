env: staging
project_name: bookkeeper
namespace: staging-thea
fqdn: "{{ env }}\\.(app|wms|thea|arsenal|vault|hustler|store)\\.scm\\.gomercury\\.in"
route: "/api/bookkeeper/"
tag: "{{ env }}-{{ build_number }}"
context: scm-staging-eks
profile: gomercury
account_no: ************
min_replicas: 1
max_replicas: 1
role_arn: arn:aws:iam::************:role/K8sS3ServiceAccount
requests_cpu: 130m
requests_memory: 230Mi
limits_cpu: 2.0
limits_memory: 2048Mi
sonar: "false"
config_map_yaml: |-
  application.yml: |-
    app:
      product:
        url: https://inventory-staging.pharmeasy.in
        auth: 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjo0LCJzYWx0Ijo4ODgwNTR9.sqm8KlNegUpv2yH17vt3y8LET1HBCXkR8g69FB5ilyE'
      aws:
        region: ap-south-1
      reportEmailEnabled: false
      alertEmailEnabled: false
      url: http://staging.vault.gomercury.in/advance-payment
      partner:
        retailer : 3
        retailerInClinic : 13
        retailerFOFO : 12
        retailerCOCO : 17
        retailerHospitals : 15
        hospitals : 19
        retailerPL : 14
        retailerAlpha : 8
        cnf : 1
        distributor : 2
        holding : 4
        manufacturer : 5
      notificationservice:
        userId: pe_user
        event: CUSTOMER_LEDGER_REPORT_NEW1
        tenant: PE_PHARMA
        source: ABACUS_SERVICE
        url: https://notifications-requests.dev.pharmeasy.in
        fromMailId: <EMAIL>
        internalMailId: <EMAIL>
      retail-io:
        url: https://staging.retailio.in/api/oneroof
        version: 1.0.0
        source: vault-client
        key: n9d8342b-2b52-4c80-a987-360e19fffc74
    spring:
      profile: {{ env }}
      cloud:
        stream:
          bindings:
            createBookkeeperInvoice:
              destination: staging.createBookkeeperInvoice
              group: book_keeper
            createDebitNotes:
              destination: staging.createDebitNotes
              group: book_keeper
            createDcCallBackProducer:
              destination: staging_pr_dc_callback
              group: book_keeper
            createDeliveryChallan:
              destination: staging_pr_dc_event
              group: book_keeper
            createCustomerDebitNotes:
              destination: staging.return_complete_accounts
              group: book_keeper
            createCustomerInvoice:
              destination: {{ env }}_retailer_invoice_creation
              group: book_keeper
            sendNotification:
              destination: {{ env }}_notify
            createGatePassEvent:
              destination: {{ env }}_gatepass_event
              group: book_keeper
            createCustomerDSInvoice:
              destination: {{ env }}_retailer_store_invoice_creation
              group: book_keeper
            createCustomerDsDebitNote:
              destination: {{ env }}_darkstore_return_complete_accounts
              group: book_keeper
            createConsolInvoice:
              destination: {{ env }}_invoice_creation
              group: book_keeper
            updatePartnerEvent:
              destination: {{ env }}_partner_update_event
              group: book_keeper
            createAutoSettlement:
              destination: {{ env }}_cash_settlement
              group: book_keeper
            createRioInvoiceProducerSyncEvent:
              destination: {{ env }}_rio_invoice_sync
              group: book_keeper
            createRioInvoiceConsumerSyncEvent:
              destination: {{ env }}_rio_invoice_sync
              group: book_keeper
            createEInvoiceProducer:
              destination: {{ env }}_einvoice_creation_bookkeeper
              group: book_keeper
              producer:
                partitionKeyExpression: payload.tenant
                partitionCount: 1
            createEInvoiceConsumer:
              destination: {{ env }}_einvoice_creation_bookkeeper
              group: book_keeper
            createGenericEInvoiceProducer:
              destination: {{ env }}_generic_einvoice_creation_bookkeeper
              group: book_keeper
              producer:
                partitionKeyExpression: payload.type
                partitionCount: 1
            createGenericEInvoiceConsumer:
              destination: {{ env }}_generic_einvoice_creation_bookkeeper
              group: book_keeper  
            createVendorPortalConsumerSyncEvent:
              destination: {{ env }}_vp_bookkeeper_data_sync
              group: book_keeper
            createVendorPortalProducerSyncEvent:
              destination: {{ env }}_vp_bookkeeper_data_sync
              group: book_keeper
            sendInvoiceListProducerEvent:
              destination: staging_settlement_invoices
              group: book_keeper
            rioPayPaymentSync:
              destination: {{ env }}_rio_payment
              group: book_keeper
            rioPayDisbursementSync:
              destination: {{ env }}_rio_disbursement_sync_to_ops
              group: book_keeper
            logisticsPayableAmountUpdateProducer:
              destination: {{ env }}_logistics_payable_amount_update
              group: bookkeeper
            createBookkeeperRioInvoice:
              destination: {{ env }}_rio_invoice_event
              group: bookkeeper
            createRioDebitNotesV2:
              destination: {{ env }}_rio_return_event
            createInvoiceSettlementProducer:
              destination: {{ env }}_bookkeeper_rio_settlement_update
              group: bookkeeper
            createInvoiceSettlementConsumer:
              destination: {{ env }}_bookkeeper_settlement_event_consumer
              group: bookkeeper
            createInvoiceSettlementNotifierProducer:
              destination: {{ env }}_bookkeeper_settlement_event_consumer
              group: bookkeeper
            retailerDebitNoteCreationProducer:
              destination: {{ env }}_bk_debit_note_updates
              group: book_keeper
            rioCreditNoteEvents:
              destination: staging_rio_credit_note_pdf
              group: bookkeeper
            createRioDebitNotesV1:
              destination: staging_rio_return_complete_accounts
              group: book_keeper
            rioCreditNotePdfConsumer:
              destination: staging_vault_credit_note_pdf
              group: bookkeeper
            rioCreditNotePdfProducer:
              destination: staging_vault_credit_note_pdf
              group: bookkeeper
            blockedVendorSettlementSinkProducer:
              destination: {{ env }}_bookkeeper_blocked_vendor_settlement
              group: book_keeper
            blockedVendorSettlementSinkConsumer:
              destination: {{ env }}_bookkeeper_blocked_vendor_settlement
              group: book_keeper 
            createMigrationProducer:
              destination: {{ env }}_migration_data_vault
              group: book_keeper
              producer:
                partitionCount: 3
            createMigrationConsumer:
              destination: {{ env }}_migration_data_vault
              group: book_keeper
            updateChequeStatus:
              destination: staging_vault_cheque_file_upload
              group: book_keeper
            sendDraftFileProducer:
              destination: {{ env }}_draft_invoice_file
              producer:
                partitionCount: 3
              group: book_keeper
            sendDraftFileConsumer:
              destination: {{ env }}_draft_invoice_file
              group: book_keeper  
            createSlipEntry:
              destination: {{ env }}_rio_slip_event
              group: book_keeper  
            slipEventCallback:
              destination: {{ env }}_rio_slip_update
              group: book_keeper
            sendFileConsumer:
              destination: {{ env }}_send_email_event
              group: book_keeper
            sendFileProducer:
              destination: {{ env }}_send_email_event
              group: book_keeper
            createGenericEWayBillProducer:
              destination: {{ env }}_generic_ewaybill_creation_bookkeeper
              group: book_keeper
              producer:
                partitionKeyExpression: payload.sourceType
                partitionCount: 1
            createGenericEWaybillConsumer:
              destination: {{ env }}_generic_ewaybill_creation_bookkeeper
              group: book_keeper
            createExcelProducer:
              destination: {{ env }}_create_excel_file
              producer:
                partitionCount: 3
            excelUrlConsumer:
              destination: {{ env }}_file_url_event
              group: book_keeper
            makerCheckerDiffDownloadProducer:
              destination: {{ env }}_maker_checker_diff_download
              group: book_keeper
            makerCheckerDiffDownloadConsumer:
              destination: {{ env }}_maker_checker_diff_download
              group: book_keeper
            makerCheckerApprovalProducer:
              destination: {{ env }}_maker_checker_approval
              group: book_keeper
            makerCheckerApprovalConsumer:
              destination: {{ env }}_maker_checker_approval
              group: book_keeper
            paymentTransactionUpdates:
              destination: {{ env }}_payment_transaction_updates
            genericEInvoiceRetryTriggerProducer:
              destination: {{ env }}_generic_einvoice_auto_retry_trigger
              group: book_keeper
              producer:
                partitionCount: 1
            genericEInvoiceRetryTriggerConsumer:
              destination: {{ env }}_generic_einvoice_auto_retry_trigger
              group: book_keeper
            genericEInvoiceCreationProducerV2:
              destination: {{ env }}_generic_einvoice_creation
              group: book_keeper
              producer:
                partitionCount: 1
            genericEInvoiceCreationConsumerV2:
              destination: {{ env }}_generic_einvoice_creation
              group: book_keeper
            cacheCornProducer:
              destination: {{ env }}_cache_vendor_ledger
              group: bookkeeper
            cacheCornConsumer:
              destination: {{ env }}_cache_vendor_ledger
              group: bookkeeper
          kafka:
            binder:
              brokers: kafka.scm.gomercury.in:9092
      datasource-write:
        hikari:
          password: ${WRITER_DB_PASS}
          jdbc-url: ********************************************************************************************************************************************
          username: ${WRITER_DB_USER}
      datasource-read:
        hikari:
          password: ${READER_DB_PASS}
          jdbc-url: ********************************************************************************************************************************************
          username: ${READER_DB_USER}
      redis:
        host: redis-staging.redis.svc.cluster.local
        port: 6379
        time-to-live: 86400000
    transaction-outbox:
      catalog:
      kafka-bootstrap-servers: kafka.scm.gomercury.in:9092
      relay.frequency.ms: 1000